{"__meta": {"id": "01K0EDJP83AHMBSZWYG1VWBT3A", "datetime": "2025-07-18 11:24:22", "utime": **********.917816, "method": "POST", "uri": "/socialvibe/install/finalize", "ip": "::1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752830656.522204, "end": **********.917862, "duration": 6.395658016204834, "duration_str": "6.4s", "measures": [{"label": "Booting", "start": 1752830656.522204, "relative_start": 0, "end": **********.13321, "relative_end": **********.13321, "duration": 0.****************, "duration_str": "611ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.133235, "relative_start": 0.****************, "end": **********.917867, "relative_end": 5.0067901611328125e-06, "duration": 5.***************, "duration_str": "5.78s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.18964, "relative_start": 0.****************, "end": **********.19476, "relative_end": **********.19476, "duration": 0.005120038986206055, "duration_str": "5.12ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "37MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "9.x", "tooltip": {"Laravel Version": "9.52.20", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "Europe/Andorra", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 6, "nb_statements": 6, "nb_visible_statements": 6, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.*****************, "accumulated_duration_str": "154ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "SHOW FULL TABLES WHERE table_type = 'BASE TABLE'", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\socialvibe\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\socialvibe\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\socialvibe\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\socialvibe\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\xampp\\htdocs\\socialvibe\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 661}], "start": **********.716391, "duration": 0.12654, "duration_str": "127ms", "memory": 0, "memory_str": null, "filename": "BoundMethod.php:36", "source": {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\socialvibe\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fsocialvibe%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FContainer%2FBoundMethod.php&line=36", "ajax": false, "filename": "BoundMethod.php", "line": "36"}, "connection": "socialvibe", "explain": null, "start_percent": 0, "width_percent": 82.025}, {"sql": "select * from information_schema.tables where table_schema = 'socialvibe' and table_name = 'migrations' and table_type = 'BASE TABLE'", "type": "query", "params": [], "bindings": ["socialvibe", "migrations"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\socialvibe\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\socialvibe\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\socialvibe\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\socialvibe\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\xampp\\htdocs\\socialvibe\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 661}], "start": 1752830661.596164, "duration": 0.00189, "duration_str": "1.89ms", "memory": 0, "memory_str": null, "filename": "BoundMethod.php:36", "source": {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\socialvibe\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fsocialvibe%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FContainer%2FBoundMethod.php&line=36", "ajax": false, "filename": "BoundMethod.php", "line": "36"}, "connection": "socialvibe", "explain": null, "start_percent": 82.025, "width_percent": 1.225}, {"sql": "create table `migrations` (`id` int unsigned not null auto_increment primary key, `migration` varchar(191) not null, `batch` int not null) default character set utf8mb4 collate 'utf8mb4_unicode_ci'", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\socialvibe\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\socialvibe\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\socialvibe\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\socialvibe\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\xampp\\htdocs\\socialvibe\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 661}], "start": 1752830661.784248, "duration": 0.018879999999999997, "duration_str": "18.88ms", "memory": 0, "memory_str": null, "filename": "BoundMethod.php:36", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\socialvibe\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fsocialvibe%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FContainer%2FBoundMethod.php&line=36", "ajax": false, "filename": "BoundMethod.php", "line": "36"}, "connection": "socialvibe", "explain": null, "start_percent": 83.25, "width_percent": 12.238}, {"sql": "select * from information_schema.tables where table_schema = 'socialvibe' and table_name = 'migrations' and table_type = 'BASE TABLE'", "type": "query", "params": [], "bindings": ["socialvibe", "migrations"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\socialvibe\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\socialvibe\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\socialvibe\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\socialvibe\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\xampp\\htdocs\\socialvibe\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 661}], "start": 1752830661.811907, "duration": 0.00413, "duration_str": "4.13ms", "memory": 0, "memory_str": null, "filename": "BoundMethod.php:36", "source": {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\socialvibe\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fsocialvibe%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FContainer%2FBoundMethod.php&line=36", "ajax": false, "filename": "BoundMethod.php", "line": "36"}, "connection": "socialvibe", "explain": null, "start_percent": 95.488, "width_percent": 2.677}, {"sql": "select `migration` from `migrations` order by `batch` asc, `migration` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\socialvibe\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\socialvibe\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\socialvibe\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\socialvibe\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\xampp\\htdocs\\socialvibe\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 661}], "start": 1752830661.940064, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "BoundMethod.php:36", "source": {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\socialvibe\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fsocialvibe%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FContainer%2FBoundMethod.php&line=36", "ajax": false, "filename": "BoundMethod.php", "line": "36"}, "connection": "socialvibe", "explain": null, "start_percent": 98.166, "width_percent": 1.121}, {"sql": "select `migration` from `migrations` order by `batch` asc, `migration` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\socialvibe\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\socialvibe\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\socialvibe\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\socialvibe\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\xampp\\htdocs\\socialvibe\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 661}], "start": 1752830661.9470348, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "BoundMethod.php:36", "source": {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\socialvibe\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fsocialvibe%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FContainer%2FBoundMethod.php&line=36", "ajax": false, "filename": "BoundMethod.php", "line": "36"}, "connection": "socialvibe", "explain": null, "start_percent": 99.287, "width_percent": 0.713}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost/socialvibe/install/finalize", "action_name": "install.finalize", "controller_action": "App\\Http\\Controllers\\InstallController@final", "uri": "POST install/finalize", "controller": "App\\Http\\Controllers\\InstallController@final<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fsocialvibe%2Fapp%2FHttp%2FControllers%2FInstallController.php&line=85\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/install", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fsocialvibe%2Fapp%2FHttp%2FControllers%2FInstallController.php&line=85\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/InstallController.php:85-164</a>", "middleware": "web, XSS", "duration": "6.39s", "peak_memory": "38MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-138672853 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-138672853\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-26832573 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1UscDAbwwUS2vazEmWoK2iWxELXza25XhlJTNiD6</span>\"\n  \"<span class=sf-dump-key>user_details</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>first_name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Joshua</span>\"\n  \"<span class=sf-dump-key>last_name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Olawore</span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"24 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-26832573\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1938551941 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">708</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryts3bliI2BrxSAX62</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">http://localhost/socialvibe/install/initialize</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2749 characters\">token=true; nonce=d9ad4a91c035b095f3246cdfeee43868; telegramId=1071876598; __stripe_mid=94e9e018-9cbd-47d2-895e-b1e8f920306d1aac1e; __clerk_db_jwt_ZUsrNx-i=dvb_2tEitnKPq0f3EUEOF0IK0ziC8Da; __client_uat_ZUsrNx-i=0; __clerk_db_jwt_KNB88qHK=dvb_2tEmx7kfdmefYllCsMG5RoJzW0S; __client_uat_KNB88qHK=0; __clerk_db_jwt_RXCM8iKy=dvb_2tOur3VomVs18XpFa7tsVBCoyDj; __clerk_db_jwt=dvb_2tOur3VomVs18XpFa7tsVBCoyDj; rl_page_init_referrer=RudderEncrypt%3AU2FsdGVkX1%2FJ1YCrrtMYPWLMO8mw9mCr2QZYZlGVPgU%3D; rl_page_init_referring_domain=RudderEncrypt%3AU2FsdGVkX1%2FMNd81RCxs8QZ%2BBNelfRhrIxte4qqoQik%3D; rl_anonymous_id=RudderEncrypt%3AU2FsdGVkX189jz9mLeMegLHvk2UZ%2By%2B3KPr8NfFePxySUVQdmSdXrO3dg08l4o2U%2BT%2FuHno6CIcs5LpxMyd34w%3D%3D; rl_user_id=RudderEncrypt%3AU2FsdGVkX1%2FD%2B2ZlubmEDN5Yy7CMq7iujuuIuYzuL2k63Ly1eianUe04m3Eh0Lpk8P3EYPIa2ovmr0gMIx1xagu2HxJ3XBKhp%2BlGsF3tsxzO3AG3NnrbmmntfgdhE3P6xiq6sqwKhv91SxYBa0Zr4%2ByD2o7eXvkGPrQWuhnYIQo%3D; rl_trait=RudderEncrypt%3AU2FsdGVkX1%2Bj2rCBvpAranbfLx7LSV1a%2FGK9k8i7iqNwnpoDYFARGFME8AAp1Wr8k5wwrsC5OMBRR%2FhInOodw60sveYJiebrVxNaJQRVQNND%2FZEKvfG7OP5PAiVXuBT%2BCM6hKWLjiMfg1eHiOvgTig%3D%3D; rl_session=RudderEncrypt%3AU2FsdGVkX1%2FJxE7u0UBcHV2%2F0ZUZgRdfOVX22aSak5H6oU2P%2Fx%2BoVXZ7G%2BzP%2FrmuYuhgRYXNNwUOqhPblU6OcI6yLEIninsLAbjMiuWsrcclqgaTpe5WegZuGGVZBz0hDjA%2BzfCssJmWhrNUrcpFMA%3D%3D; ph_phc_4URIAm1uYfJO7j8kWSe0J8lc8IqnstRLS7Jx8NcakHo_posthog=%7B%22distinct_id%22%3A%22212243749f566accc2f950904fa4224aae025699bc538cd08af54f940b39b5e2%235442b004-39fc-4c0b-87cd-052c98356692%22%2C%22%24sesid%22%3A%5Bnull%2Cnull%2Cnull%5D%2C%22%24epp%22%3Atrue%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22%24direct%22%2C%22u%22%3A%22http%3A%2F%2Flocalhost%3A5678%2Fhome%2Fworkflows%22%7D%7D; __client_uat_RXCM8iKy=0; __client_uat=0; gdpr_cookie=eyJpdiI6IlpxcTkrbWdLSVRDNUxBUEpPTTd0T1E9PSIsInZhbHVlIjoiRE5Hcmt4MUk2SkJva3JZaVpLeHJSc0ZrR0lCUWNLZldnZGNvUHRWbXlTSUlWVnZTVklRSnRUcmFFZU43dGZQMyIsIm1hYyI6IjMxYWM0NmIyZThjNTUwNDgzMjdkZGI0N2QzNmM5NmMwMDVlMmIyMDFmNGJkNWJmYTYwN2JkNjExNDZhMmUxNzEiLCJ0YWciOiIifQ%3D%3D; XSRF-TOKEN=eyJpdiI6IklTNFBybUNPemw5TzNQV09pN2xZQnc9PSIsInZhbHVlIjoiVWpRQ3BUem9pOEpjaUhqTHhCam11eW1LemVlcGZVSk9td212L3lOVUxFaGpEUWpxSVk4N3Vzc1lTUHhYWHF5YjljbFBsM0xLNjRQY1d4RFNjVmlzVHdJS09VZkhWVFVPMVhCV1h2YWdrTlI4ak5xNEpzUmJLeHVjSElIZEZIdmIiLCJtYWMiOiI2NDk5OTE4ZDU0YmM1ZTQ3NzFjNWU1NDA4OWRiZWZjNWM1ZjRkZTU0ZDdlMTJkN2M0MzRkYzk0M2UxOTU2OWMzIiwidGFnIjoiIn0%3D; socialvibe_session=eyJpdiI6ImxXUUFtcStqZ09HQlFZVU1NYnFEb1E9PSIsInZhbHVlIjoiM0ZabWx4V2xLTGZzYmtWMFdsUiswVTZmQzEwenl4aFIyYUpuanJTR2FuUFZEYVUxb2ZRU0JoNnM4WDZSR09SUjExOVFxUkNES1lSK3NzWnltcG5nYWdkMGkwQzAyam9BdGRhZU53T3dZem9XSVhXVWZueWtSUDBRaTd6WGRTZVciLCJtYWMiOiJmZjViOGVhM2RiZjE2NGMzODE5ZjE3NzhhMDk4MzUzMmViNDJhNjRlZDk0ZWE1YjE1YmZjNjBkNmExMDIzZWJmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1938551941\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1411151136 data-indent-pad=\"  \"><span class=sf-dump-note>array:22</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>token</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>nonce</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>telegramId</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__clerk_db_jwt_ZUsrNx-i</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__client_uat_ZUsrNx-i</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__clerk_db_jwt_KNB88qHK</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__client_uat_KNB88qHK</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__clerk_db_jwt_RXCM8iKy</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__clerk_db_jwt</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>rl_page_init_referrer</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>rl_page_init_referring_domain</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>rl_anonymous_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>rl_user_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>rl_trait</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>rl_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>ph_phc_4URIAm1uYfJO7j8kWSe0J8lc8IqnstRLS7Jx8NcakHo_posthog</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__client_uat_RXCM8iKy</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__client_uat</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>gdpr_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1UscDAbwwUS2vazEmWoK2iWxELXza25XhlJTNiD6</span>\"\n  \"<span class=sf-dump-key>socialvibe_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XYPyWsopiu5MwScUheI9x5qevaUjETvxfmBMdxgZ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1411151136\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-400119317 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 18 Jul 2025 09:24:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-400119317\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-321617347 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1UscDAbwwUS2vazEmWoK2iWxELXza25XhlJTNiD6</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => []\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"46 characters\">http://localhost/socialvibe/install/initialize</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>hash_token</span>\" => \"<span class=sf-dump-str title=\"21 characters\">NrYOM5gWfHUnkvNjg57B1</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>activation_code</span>\" => \"<span class=sf-dump-str title=\"8 characters\">PoolotHq</span>\"\n  \"<span class=sf-dump-key>db_host</span>\" => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  \"<span class=sf-dump-key>db_name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">socialvibe</span>\"\n  \"<span class=sf-dump-key>db_user</span>\" => \"<span class=sf-dump-str title=\"4 characters\">root</span>\"\n  \"<span class=sf-dump-key>db_password</span>\" => \"\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-321617347\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost/socialvibe/install/finalize", "action_name": "install.finalize", "controller_action": "App\\Http\\Controllers\\InstallController@final"}, "badge": null}}