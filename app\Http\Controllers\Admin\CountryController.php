<?php

namespace App\Http\Controllers\Admin;

use App\DataTables\CountryDataTable;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\CountryRequest;
use App\Repositories\CountryRepository;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;

class CountryController extends Controller
{
    protected $country;

    public function __construct(CountryRepository $country)
    {
        $this->country = $country;
    }

    public function index(CountryDataTable $dataTable)
    {
        return $dataTable->render('backend.admin.country.index');
    }

    public function store(CountryRequest $request): \Illuminate\Http\JsonResponse
    {
        if (isDemoMode()) {
            $data = [
                'status' => false,
                'error'  => __('this_function_is_disabled_in_demo_server'),
                'title'  => 'error',
            ];

            return response()->json($data);
        }

        try {
            $this->country->store($request->all());

            return response()->json(['success' => __('create_successful')]);
        } catch (Exception $e) {
            return response()->json(['status' => false, 'error' => __('something_went_wrong_please_try_again')]);
        }
    }

    public function edit($id): \Illuminate\Http\JsonResponse
    {
        try {
            $country = $this->country->find($id);

            $data    = [
                'id'              => $country->id,
                'name'            => $country->name,
                'iso3'            => $country->iso3,
                'iso2'            => $country->iso2,
                'phonecode'       => $country->phonecode,
                'currency'        => $country->currency,
                'currency_symbol' => $country->currency_symbol,
            ];

            return response()->json($data);
        } catch (Exception $e) {
            return response()->json(['status' => false, 'error' => __('something_went_wrong_please_try_again')]);
        }
    }

    public function update(CountryRequest $request, $id): \Illuminate\Http\JsonResponse
    {
        if (isDemoMode()) {
            $data = [
                'status' => false,
                'error'  => __('this_function_is_disabled_in_demo_server'),
                'title'  => 'error',
            ];

            return response()->json($data);
        }
        try {

            $this->country->update($request->all(), $id);

            return response()->json(['status' => true, 'success' => __('update_successful')]);
        } catch (Exception $e) {
            return response()->json(['status' => false, 'error' => __('something_went_wrong_please_try_again')]);
        }
    }

    public function destroy($id): \Illuminate\Http\JsonResponse
    {
        if (isDemoMode()) {
            $data = [
                'status'  => false,
                'message' => __('this_function_is_disabled_in_demo_server'),
                'title'   => 'error',
            ];

            return response()->json($data);
        }
        try {
            $this->country->delete($id);

            $data = [
                'status'  => true,
                'message' => __('delete_successful'),
                'title'   => __('success'),
            ];

            return response()->json($data);
        } catch (Exception $e) {
            $data = [
                'status'  => 400,
                'message' => __('something_went_wrong_please_try_again'),
                'title'   => 'error',
            ];

            return response()->json($data);
        }
    }

    public function statusChange(Request $request): \Illuminate\Http\JsonResponse
    {
        if (isDemoMode()) {
            $data = [
                'status'  => false,
                'message' => __('this_function_is_disabled_in_demo_server'),
                'title'   => 'error',
            ];

            return response()->json($data);
        }
        try {
            $this->country->statusChange($request->all());
            $data = [
                'status'  => 200,
                'message' => __('update_successful'),
                'title'   => 'success',
            ];
            Artisan::call('all:clear');

            return response()->json($data);
        } catch (Exception $e) {
            $data = [
                'status'  => 400,
                'message' => __('something_went_wrong_please_try_again'),
                'title'   => 'error',
            ];

            return response()->json($data);
        }
    }
}
