{"__meta": {"id": "01K0ED674HTR4SRN76XXYRZS9E", "datetime": "2025-07-18 11:17:34", "utime": **********.227155, "method": "GET", "uri": "/socialvibe/install/initialize", "ip": "::1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752830252.200054, "end": **********.227193, "duration": 2.027139186859131, "duration_str": "2.03s", "measures": [{"label": "Booting", "start": 1752830252.200054, "relative_start": 0, "end": **********.216223, "relative_end": **********.216223, "duration": 1.****************, "duration_str": "1.02s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.216258, "relative_start": 1.****************, "end": **********.227197, "relative_end": 3.814697265625e-06, "duration": 1.****************, "duration_str": "1.01s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.288852, "relative_start": 1.****************, "end": **********.299827, "relative_end": **********.299827, "duration": 0.010975122451782227, "duration_str": "10.98ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: install.index", "start": **********.388313, "relative_start": 1.****************, "end": **********.388313, "relative_end": **********.388313, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "27MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "9.x", "tooltip": {"Laravel Version": "9.52.20", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "Europe/Andorra", "Locale": "en"}}, "views": {"count": 1, "nb_templates": 1, "templates": [{"name": "install.index", "param_count": null, "params": [], "start": **********.388082, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\socialvibe\\resources\\views/install/index.blade.phpinstall.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fsocialvibe%2Fresources%2Fviews%2Finstall%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}]}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost/socialvibe/install/initialize", "action_name": "install.initialize", "controller_action": "App\\Http\\Controllers\\InstallController@index", "uri": "GET install/initialize", "controller": "App\\Http\\Controllers\\InstallController@index<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fsocialvibe%2Fapp%2FHttp%2FControllers%2FInstallController.php&line=19\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fsocialvibe%2Fapp%2FHttp%2FControllers%2FInstallController.php&line=19\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/InstallController.php:19-31</a>", "middleware": "web", "duration": "2.02s", "peak_memory": "28MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-897041803 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-897041803\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1967541169 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1967541169\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1680356593 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2749 characters\">token=true; nonce=d9ad4a91c035b095f3246cdfeee43868; telegramId=1071876598; __stripe_mid=94e9e018-9cbd-47d2-895e-b1e8f920306d1aac1e; __clerk_db_jwt_ZUsrNx-i=dvb_2tEitnKPq0f3EUEOF0IK0ziC8Da; __client_uat_ZUsrNx-i=0; __clerk_db_jwt_KNB88qHK=dvb_2tEmx7kfdmefYllCsMG5RoJzW0S; __client_uat_KNB88qHK=0; __clerk_db_jwt_RXCM8iKy=dvb_2tOur3VomVs18XpFa7tsVBCoyDj; __clerk_db_jwt=dvb_2tOur3VomVs18XpFa7tsVBCoyDj; rl_page_init_referrer=RudderEncrypt%3AU2FsdGVkX1%2FJ1YCrrtMYPWLMO8mw9mCr2QZYZlGVPgU%3D; rl_page_init_referring_domain=RudderEncrypt%3AU2FsdGVkX1%2FMNd81RCxs8QZ%2BBNelfRhrIxte4qqoQik%3D; rl_anonymous_id=RudderEncrypt%3AU2FsdGVkX189jz9mLeMegLHvk2UZ%2By%2B3KPr8NfFePxySUVQdmSdXrO3dg08l4o2U%2BT%2FuHno6CIcs5LpxMyd34w%3D%3D; rl_user_id=RudderEncrypt%3AU2FsdGVkX1%2FD%2B2ZlubmEDN5Yy7CMq7iujuuIuYzuL2k63Ly1eianUe04m3Eh0Lpk8P3EYPIa2ovmr0gMIx1xagu2HxJ3XBKhp%2BlGsF3tsxzO3AG3NnrbmmntfgdhE3P6xiq6sqwKhv91SxYBa0Zr4%2ByD2o7eXvkGPrQWuhnYIQo%3D; rl_trait=RudderEncrypt%3AU2FsdGVkX1%2Bj2rCBvpAranbfLx7LSV1a%2FGK9k8i7iqNwnpoDYFARGFME8AAp1Wr8k5wwrsC5OMBRR%2FhInOodw60sveYJiebrVxNaJQRVQNND%2FZEKvfG7OP5PAiVXuBT%2BCM6hKWLjiMfg1eHiOvgTig%3D%3D; rl_session=RudderEncrypt%3AU2FsdGVkX1%2FJxE7u0UBcHV2%2F0ZUZgRdfOVX22aSak5H6oU2P%2Fx%2BoVXZ7G%2BzP%2FrmuYuhgRYXNNwUOqhPblU6OcI6yLEIninsLAbjMiuWsrcclqgaTpe5WegZuGGVZBz0hDjA%2BzfCssJmWhrNUrcpFMA%3D%3D; ph_phc_4URIAm1uYfJO7j8kWSe0J8lc8IqnstRLS7Jx8NcakHo_posthog=%7B%22distinct_id%22%3A%22212243749f566accc2f950904fa4224aae025699bc538cd08af54f940b39b5e2%235442b004-39fc-4c0b-87cd-052c98356692%22%2C%22%24sesid%22%3A%5Bnull%2Cnull%2Cnull%5D%2C%22%24epp%22%3Atrue%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22%24direct%22%2C%22u%22%3A%22http%3A%2F%2Flocalhost%3A5678%2Fhome%2Fworkflows%22%7D%7D; __client_uat_RXCM8iKy=0; __client_uat=0; gdpr_cookie=eyJpdiI6IlpxcTkrbWdLSVRDNUxBUEpPTTd0T1E9PSIsInZhbHVlIjoiRE5Hcmt4MUk2SkJva3JZaVpLeHJSc0ZrR0lCUWNLZldnZGNvUHRWbXlTSUlWVnZTVklRSnRUcmFFZU43dGZQMyIsIm1hYyI6IjMxYWM0NmIyZThjNTUwNDgzMjdkZGI0N2QzNmM5NmMwMDVlMmIyMDFmNGJkNWJmYTYwN2JkNjExNDZhMmUxNzEiLCJ0YWciOiIifQ%3D%3D; XSRF-TOKEN=eyJpdiI6IktoK3h2NHFnb0hqT24rRzVua0p4Unc9PSIsInZhbHVlIjoiNlFuMXYySUFjdUp5aHFaSFZIT0lFOW5OYVJvamlNTUNlYlJnQmdPQWs2eFZ1ckJUSXFQNGJ3bVA1Y01qUEcxaTczbld5bUk2U2dzUFVlV0tBUENaMmtxcVVjMzBLMGVQUCs2U3ZVUHN1NFdyT3VXaVNFTW5iTW0wWU91ckNMTlkiLCJtYWMiOiIwOTEzYTJlMDhkNjRjNGM2ZGI5YTE5MGM5ZjE3ZTJiNjUyNzJkMjhjMzk2OWI1OWMzMmIwOGI0OTU0NTg3NTFlIiwidGFnIjoiIn0%3D; socialvibe_session=eyJpdiI6Iisrdm1WYXd1N0xzRFdXR2o5RFFOL2c9PSIsInZhbHVlIjoicGhVakR1enliMCtOTzNxUUF6NEFnOUNZK2FDMlhhRUdNc1h3TzA0Y2lZTzkxK2dJYzJYWEx1ZGV4QjZpMldzY3VVQUMvZWhLbmxHOGZwem9HSE5zQjdOU3IyUlRra1ByRWh4Q2ZUVkNBeXFSMG8wbk5xS2xVL3QvRUsybGdpZ04iLCJtYWMiOiJjNDFhZjIyMTUzNDliYTMyNGU5Y2E1YjMyOWM2OWRhYzEwN2U4OWEwYzc2N2MwMmUwOWQ3MDQyMWQ5Njg2YTNkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1680356593\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1437433791 data-indent-pad=\"  \"><span class=sf-dump-note>array:22</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>token</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>nonce</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>telegramId</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__clerk_db_jwt_ZUsrNx-i</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__client_uat_ZUsrNx-i</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__clerk_db_jwt_KNB88qHK</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__client_uat_KNB88qHK</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__clerk_db_jwt_RXCM8iKy</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__clerk_db_jwt</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>rl_page_init_referrer</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>rl_page_init_referring_domain</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>rl_anonymous_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>rl_user_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>rl_trait</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>rl_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>ph_phc_4URIAm1uYfJO7j8kWSe0J8lc8IqnstRLS7Jx8NcakHo_posthog</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__client_uat_RXCM8iKy</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__client_uat</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>gdpr_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1UscDAbwwUS2vazEmWoK2iWxELXza25XhlJTNiD6</span>\"\n  \"<span class=sf-dump-key>socialvibe_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XYPyWsopiu5MwScUheI9x5qevaUjETvxfmBMdxgZ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1437433791\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-564800958 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 18 Jul 2025 09:17:33 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-564800958\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-555588511 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1UscDAbwwUS2vazEmWoK2iWxELXza25XhlJTNiD6</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => []\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"46 characters\">http://localhost/socialvibe/install/initialize</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>hash_token</span>\" => \"<span class=sf-dump-str title=\"21 characters\">TU9vmPmPH6FqmRvVxQdI8</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>activation_code</span>\" => \"<span class=sf-dump-str title=\"8 characters\">PoolotHq</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-555588511\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost/socialvibe/install/initialize", "action_name": "install.initialize", "controller_action": "App\\Http\\Controllers\\InstallController@index"}, "badge": null}}