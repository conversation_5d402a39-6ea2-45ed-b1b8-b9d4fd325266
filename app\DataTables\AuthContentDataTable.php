<?php

namespace App\DataTables;

use App\Models\AuthContent;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;
use Ya<PERSON>ra\DataTables\EloquentDataTable;
use Ya<PERSON>ra\DataTables\Html\Builder as HtmlBuilder;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class AuthContentDataTable extends DataTable
{
    public function dataTable(QueryBuilder $query): EloquentDataTable
    {
        return (new EloquentDataTable($query))
            ->addIndexColumn()
            ->addColumn('action', function ($content) {
                return view('backend.admin.website.auth_content.column.action', compact('content'));
            })->addColumn('image', function ($content) {
                return view('backend.admin.website.auth_content.column.image', compact('content'));
            })->addColumn('status', function ($content) {
                return view('backend.admin.website.auth_content.column.status', compact('content'));
            })->addColumn('title', function ($content) {
                return @$content->language->title;
            })->setRowId('id');
    }

    public function query(): QueryBuilder
    {
        $model = AuthContent::with('language');

        return $model->when($this->request->search['value'] ?? false, function ($query, $search) {
            $query->where(function ($query) use ($search) {
                $query->whereHas('language', function ($query) use ($search) {
                    $query->where('title', 'like', "%$search%");
                });
            });
        })->latest();
    }

    public function html(): HtmlBuilder
    {
        return $this->builder()
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->orderBy(1)
            ->selectStyleSingle()
            ->setTableAttribute('style', 'width:99.8%')
            ->footerCallback('function ( row, data, start, end, display ) {

                $(".dataTables_length select").addClass("form-select form-select-lg without_search mb-3");
                selectionFields();
            }')
            ->parameters([
                'dom'        => 'Blfrtip',
                'buttons'    => [
                    [],
                ],
                'lengthMenu' => [[10, 25, 50, 100, 250], [10, 25, 50, 100, 250]],
                'language'   => [
                    'searchPlaceholder' => __('search'),
                    'lengthMenu'        => '_MENU_ '.__('content_per_page'),
                    'search'            => '',
                ],
            ]);
    }

    public function getColumns(): array
    {
        return [
            Column::computed('id')->data('DT_RowIndex')->title('#')->searchable(false)->width(10),
            Column::make('title')->title(__('title')),
            Column::make('image')->title(__('image')),
            Column::computed('status')->title(__('status'))->exportable(false)
                ->printable(false)->width(10),
            Column::computed('action')->title(__('action'))
                ->exportable(false)
                ->printable(false)
                ->searchable(false)->addClass('action-card')->width(10),

        ];
    }

    protected function filename(): string
    {
        return 'client_'.date('YmdHis');
    }
}
