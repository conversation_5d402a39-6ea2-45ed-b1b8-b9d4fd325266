<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DatabaseStructureSeeder
{
    /**
     * Create all database tables from the SQL structure file
     *
     * @return bool
     */
    public function createDatabaseTables(): bool
    {
        try {
            // Path to the SQL file
            $sqlFilePath = database_path('sql/database_structure.sql');
            
            // Check if the file exists
            if (!file_exists($sqlFilePath)) {
                Log::error('Database structure SQL file not found at: ' . $sqlFilePath);
                return false;
            }
            
            // Read the SQL file
            $sql = file_get_contents($sqlFilePath);
            
            // Split the SQL file into individual statements
            $statements = $this->splitSqlFile($sql);
            
            // Execute each statement
            foreach ($statements as $statement) {
                if (!empty(trim($statement))) {
                    try {
                        DB::unprepared($statement);
                    } catch (\Exception $e) {
                        // Log the error but continue with other statements
                        Log::error('Error executing SQL statement: ' . $e->getMessage());
                        Log::error('Statement: ' . $statement);
                    }
                }
            }
            
            // Import countries, states, and cities data if available
            $this->importGeographicData();
            
            return true;
        } catch (\Exception $e) {
            Log::error('Error creating database tables: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Split SQL file into individual statements
     *
     * @param string $sql
     * @return array
     */
    private function splitSqlFile(string $sql): array
    {
        // Remove comments
        $sql = preg_replace('/\/\*.*?\*\/|--.*?\n|#.*?\n/s', '', $sql);
        
        // Split by semicolon followed by a newline or end of string
        $statements = preg_split('/;\s*(\n|$)/', $sql);
        
        return array_filter($statements);
    }
    
    /**
     * Import geographic data (countries, states, cities)
     *
     * @return void
     */
    private function importGeographicData(): void
    {
        // Import countries data
        $this->importSqlFile('public/sql/countries.sql');
        
        // Import states data
        $this->importSqlFile('public/sql/states.sql');
        
        // Import cities data
        $this->importSqlFile('public/sql/cities.sql');
    }
    
    /**
     * Import a SQL file if it exists
     *
     * @param string $path
     * @return void
     */
    private function importSqlFile(string $path): void
    {
        try {
            $fullPath = base_path($path);
            if (file_exists($fullPath)) {
                $sql = file_get_contents($fullPath);
                DB::unprepared($sql);
            }
        } catch (\Exception $e) {
            Log::error('Error importing SQL file ' . $path . ': ' . $e->getMessage());
        }
    }
}
