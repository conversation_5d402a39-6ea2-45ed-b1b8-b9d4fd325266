<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class ThemeOptionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $rules = [
            'light_logo' => 'mimes:jpg,JPG,JPEG,jpeg,png,PNG,webp,svg,WEBP|max:5120',
            'dark_logo'  => 'mimes:jpg,JPG,JPEG,jpeg,png,PNG,webp,svg,WEBP|max:5120',
            'favicon'    => 'mimes:jpg,JPG,JPEG,jpeg,png,PNG,webp,svg,WEBP|max:5120',
        ];

        return $rules;
    }
}
