{"__meta": {"id": "01K0EGHKV28SNW5ADGBM1GJW1R", "datetime": "2025-07-18 12:16:13", "utime": **********.411908, "method": "GET", "uri": "/socialvibe/_ignition/health-check", "ip": "::1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.213852, "end": **********.411937, "duration": 1.198085069656372, "duration_str": "1.2s", "measures": [{"label": "Booting", "start": **********.213852, "relative_start": 0, "end": **********.041752, "relative_end": **********.041752, "duration": 0.****************, "duration_str": "828ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.04178, "relative_start": 0.****************, "end": **********.41194, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "370ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.086928, "relative_start": 0.****************, "end": **********.092283, "relative_end": **********.092283, "duration": 0.005355119705200195, "duration_str": "5.36ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "30MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "9.x", "tooltip": {"Laravel Version": "9.52.20", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost/socialvibe", "Timezone": "Europe/Andorra", "Locale": ""}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost/socialvibe/_ignition/health-check", "action_name": "ignition.healthCheck", "controller_action": "Spatie\\LaravelIgnition\\Http\\Controllers\\HealthCheckController", "uri": "GET _ignition/health-check", "controller": "Spatie\\LaravelIgnition\\Http\\Controllers\\HealthCheckController", "prefix": "_ignition", "middleware": "Spatie\\LaravelIgnition\\Http\\Middleware\\RunnableSolutionsEnabled", "duration": "1.2s", "peak_memory": "32MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-68456356 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-68456356\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-406857947 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-406857947\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost/socialvibe/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2749 characters\">token=true; nonce=d9ad4a91c035b095f3246cdfeee43868; telegramId=1071876598; __stripe_mid=94e9e018-9cbd-47d2-895e-b1e8f920306d1aac1e; __clerk_db_jwt_ZUsrNx-i=dvb_2tEitnKPq0f3EUEOF0IK0ziC8Da; __client_uat_ZUsrNx-i=0; __clerk_db_jwt_KNB88qHK=dvb_2tEmx7kfdmefYllCsMG5RoJzW0S; __client_uat_KNB88qHK=0; __clerk_db_jwt_RXCM8iKy=dvb_2tOur3VomVs18XpFa7tsVBCoyDj; __clerk_db_jwt=dvb_2tOur3VomVs18XpFa7tsVBCoyDj; rl_page_init_referrer=RudderEncrypt%3AU2FsdGVkX1%2FJ1YCrrtMYPWLMO8mw9mCr2QZYZlGVPgU%3D; rl_page_init_referring_domain=RudderEncrypt%3AU2FsdGVkX1%2FMNd81RCxs8QZ%2BBNelfRhrIxte4qqoQik%3D; rl_anonymous_id=RudderEncrypt%3AU2FsdGVkX189jz9mLeMegLHvk2UZ%2By%2B3KPr8NfFePxySUVQdmSdXrO3dg08l4o2U%2BT%2FuHno6CIcs5LpxMyd34w%3D%3D; rl_user_id=RudderEncrypt%3AU2FsdGVkX1%2FD%2B2ZlubmEDN5Yy7CMq7iujuuIuYzuL2k63Ly1eianUe04m3Eh0Lpk8P3EYPIa2ovmr0gMIx1xagu2HxJ3XBKhp%2BlGsF3tsxzO3AG3NnrbmmntfgdhE3P6xiq6sqwKhv91SxYBa0Zr4%2ByD2o7eXvkGPrQWuhnYIQo%3D; rl_trait=RudderEncrypt%3AU2FsdGVkX1%2Bj2rCBvpAranbfLx7LSV1a%2FGK9k8i7iqNwnpoDYFARGFME8AAp1Wr8k5wwrsC5OMBRR%2FhInOodw60sveYJiebrVxNaJQRVQNND%2FZEKvfG7OP5PAiVXuBT%2BCM6hKWLjiMfg1eHiOvgTig%3D%3D; rl_session=RudderEncrypt%3AU2FsdGVkX1%2FJxE7u0UBcHV2%2F0ZUZgRdfOVX22aSak5H6oU2P%2Fx%2BoVXZ7G%2BzP%2FrmuYuhgRYXNNwUOqhPblU6OcI6yLEIninsLAbjMiuWsrcclqgaTpe5WegZuGGVZBz0hDjA%2BzfCssJmWhrNUrcpFMA%3D%3D; ph_phc_4URIAm1uYfJO7j8kWSe0J8lc8IqnstRLS7Jx8NcakHo_posthog=%7B%22distinct_id%22%3A%22212243749f566accc2f950904fa4224aae025699bc538cd08af54f940b39b5e2%235442b004-39fc-4c0b-87cd-052c98356692%22%2C%22%24sesid%22%3A%5Bnull%2Cnull%2Cnull%5D%2C%22%24epp%22%3Atrue%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22%24direct%22%2C%22u%22%3A%22http%3A%2F%2Flocalhost%3A5678%2Fhome%2Fworkflows%22%7D%7D; __client_uat_RXCM8iKy=0; __client_uat=0; gdpr_cookie=eyJpdiI6IlpxcTkrbWdLSVRDNUxBUEpPTTd0T1E9PSIsInZhbHVlIjoiRE5Hcmt4MUk2SkJva3JZaVpLeHJSc0ZrR0lCUWNLZldnZGNvUHRWbXlTSUlWVnZTVklRSnRUcmFFZU43dGZQMyIsIm1hYyI6IjMxYWM0NmIyZThjNTUwNDgzMjdkZGI0N2QzNmM5NmMwMDVlMmIyMDFmNGJkNWJmYTYwN2JkNjExNDZhMmUxNzEiLCJ0YWciOiIifQ%3D%3D; XSRF-TOKEN=eyJpdiI6IjZ3T0RzVVR5NEtQWlcvRGxVVGxLYUE9PSIsInZhbHVlIjoiTVRKcXJGcWJXWGxaM0V1R1lqWjVvTUUrQnh0Nk5yeU9NM1JZTi8rNkFod01CWkhvRFZVNFJIZ2pBZG00WU1CU0xtQW9OdE0zYmZzSmdTaHBtZ1ZjV25EUW9YbW00NzdnMDRnWHhEYWRLZ3lkazkwYmZsYXJTZnpmL3RoM2s5NisiLCJtYWMiOiI3ODg5MTEwOGRiMGRiOGExYzUxMzU1YWYwMjE0ZWFkY2MwYWI2M2EyOGIzNGRmZTZiYjdjNzA5OTc3OGYxZGJkIiwidGFnIjoiIn0%3D; socialvibe_session=eyJpdiI6ImlrY0ZzYWcxN3VNMzJWc0lVUXp2NXc9PSIsInZhbHVlIjoiZXhIUG50RmJQZUhPSDlUZW42cmpwQURnUEN6cDVSYmthaGRJYjRVQ2tXeVUrV1RvNG9NQUNCNkE5d3JQTzFEYWQwVEt3WXlrdDd2QTdtR2xoY1ZJZ2NEcUVjQk9QYlZvc1M4NzA5b2htdzV4NkNJYVRmczhzdURqaW1iR1MvV1giLCJtYWMiOiJkNWRhM2VmNGY2MDk5ODliZTBlZjUyZmRmY2Y0Y2M3MGU0ZDY0OTk3NWVkODA4MmNhMDBhYTA2NjI0YTM5ZjU3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-911225737 data-indent-pad=\"  \"><span class=sf-dump-note>array:22</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>token</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>nonce</span>\" => \"<span class=sf-dump-str title=\"32 characters\">d9ad4a91c035b095f3246cdfeee43868</span>\"\n  \"<span class=sf-dump-key>telegramId</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1071876598</span>\"\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => \"<span class=sf-dump-str title=\"42 characters\">94e9e018-9cbd-47d2-895e-b1e8f920306d1aac1e</span>\"\n  \"<span class=sf-dump-key>__clerk_db_jwt_ZUsrNx-i</span>\" => \"<span class=sf-dump-str title=\"31 characters\">dvb_2tEitnKPq0f3EUEOF0IK0ziC8Da</span>\"\n  \"<span class=sf-dump-key>__client_uat_ZUsrNx-i</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>__clerk_db_jwt_KNB88qHK</span>\" => \"<span class=sf-dump-str title=\"31 characters\">dvb_2tEmx7kfdmefYllCsMG5RoJzW0S</span>\"\n  \"<span class=sf-dump-key>__client_uat_KNB88qHK</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>__clerk_db_jwt_RXCM8iKy</span>\" => \"<span class=sf-dump-str title=\"31 characters\">dvb_2tOur3VomVs18XpFa7tsVBCoyDj</span>\"\n  \"<span class=sf-dump-key>__clerk_db_jwt</span>\" => \"<span class=sf-dump-str title=\"31 characters\">dvb_2tOur3VomVs18XpFa7tsVBCoyDj</span>\"\n  \"<span class=sf-dump-key>rl_page_init_referrer</span>\" => \"<span class=sf-dump-str title=\"58 characters\">RudderEncrypt:U2FsdGVkX1/J1YCrrtMYPWLMO8mw9mCr2QZYZlGVPgU=</span>\"\n  \"<span class=sf-dump-key>rl_page_init_referring_domain</span>\" => \"<span class=sf-dump-str title=\"58 characters\">RudderEncrypt:U2FsdGVkX1/MNd81RCxs8QZ+BNelfRhrIxte4qqoQik=</span>\"\n  \"<span class=sf-dump-key>rl_anonymous_id</span>\" => \"<span class=sf-dump-str title=\"102 characters\">RudderEncrypt:U2FsdGVkX189jz9mLeMegLHvk2UZ+y+3KPr8NfFePxySUVQdmSdXrO3dg08l4o2U+T/uHno6CIcs5LpxMyd34w==</span>\"\n  \"<span class=sf-dump-key>rl_user_id</span>\" => \"<span class=sf-dump-str title=\"186 characters\">RudderEncrypt:U2FsdGVkX1/D+2ZlubmEDN5Yy7CMq7iujuuIuYzuL2k63Ly1eianUe04m3Eh0Lpk8P3EYPIa2ovmr0gMIx1xagu2HxJ3XBKhp+lGsF3tsxzO3AG3NnrbmmntfgdhE3P6xiq6sqwKhv91SxYBa0Zr4+yD2o7eXvkGPrQWuhnYIQo=</span>\"\n  \"<span class=sf-dump-key>rl_trait</span>\" => \"<span class=sf-dump-str title=\"166 characters\">RudderEncrypt:U2FsdGVkX1+j2rCBvpAranbfLx7LSV1a/GK9k8i7iqNwnpoDYFARGFME8AAp1Wr8k5wwrsC5OMBRR/hInOodw60sveYJiebrVxNaJQRVQNND/ZEKvfG7OP5PAiVXuBT+CM6hKWLjiMfg1eHiOvgTig==</span>\"\n  \"<span class=sf-dump-key>rl_session</span>\" => \"<span class=sf-dump-str title=\"166 characters\">RudderEncrypt:U2FsdGVkX1/JxE7u0UBcHV2/0ZUZgRdfOVX22aSak5H6oU2P/x+oVXZ7G+zP/rmuYuhgRYXNNwUOqhPblU6OcI6yLEIninsLAbjMiuWsrcclqgaTpe5WegZuGGVZBz0hDjA+zfCssJmWhrNUrcpFMA==</span>\"\n  \"<span class=sf-dump-key>ph_phc_4URIAm1uYfJO7j8kWSe0J8lc8IqnstRLS7Jx8NcakHo_posthog</span>\" => \"<span class=sf-dump-str title=\"239 characters\">{&quot;distinct_id&quot;:&quot;212243749f566accc2f950904fa4224aae025699bc538cd08af54f940b39b5e2#5442b004-39fc-4c0b-87cd-052c98356692&quot;,&quot;$sesid&quot;:[null,null,null],&quot;$epp&quot;:true,&quot;$initial_person_info&quot;:{&quot;r&quot;:&quot;$direct&quot;,&quot;u&quot;:&quot;http://localhost:5678/home/<USER>/span>\"\n  \"<span class=sf-dump-key>__client_uat_RXCM8iKy</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>__client_uat</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>gdpr_cookie</span>\" => \"<span class=sf-dump-str title=\"256 characters\">eyJpdiI6IlpxcTkrbWdLSVRDNUxBUEpPTTd0T1E9PSIsInZhbHVlIjoiRE5Hcmt4MUk2SkJva3JZaVpLeHJSc0ZrR0lCUWNLZldnZGNvUHRWbXlTSUlWVnZTVklRSnRUcmFFZU43dGZQMyIsIm1hYyI6IjMxYWM0NmIyZThjNTUwNDgzMjdkZGI0N2QzNmM5NmMwMDVlMmIyMDFmNGJkNWJmYTYwN2JkNjExNDZhMmUxNzEiLCJ0YWciOiIifQ==</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjZ3T0RzVVR5NEtQWlcvRGxVVGxLYUE9PSIsInZhbHVlIjoiTVRKcXJGcWJXWGxaM0V1R1lqWjVvTUUrQnh0Nk5yeU9NM1JZTi8rNkFod01CWkhvRFZVNFJIZ2pBZG00WU1CU0xtQW9OdE0zYmZzSmdTaHBtZ1ZjV25EUW9YbW00NzdnMDRnWHhEYWRLZ3lkazkwYmZsYXJTZnpmL3RoM2s5NisiLCJtYWMiOiI3ODg5MTEwOGRiMGRiOGExYzUxMzU1YWYwMjE0ZWFkY2MwYWI2M2EyOGIzNGRmZTZiYjdjNzA5OTc3OGYxZGJkIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>socialvibe_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImlrY0ZzYWcxN3VNMzJWc0lVUXp2NXc9PSIsInZhbHVlIjoiZXhIUG50RmJQZUhPSDlUZW42cmpwQURnUEN6cDVSYmthaGRJYjRVQ2tXeVUrV1RvNG9NQUNCNkE5d3JQTzFEYWQwVEt3WXlrdDd2QTdtR2xoY1ZJZ2NEcUVjQk9QYlZvc1M4NzA5b2htdzV4NkNJYVRmczhzdURqaW1iR1MvV1giLCJtYWMiOiJkNWRhM2VmNGY2MDk5ODliZTBlZjUyZmRmY2Y0Y2M3MGU0ZDY0OTk3NWVkODA4MmNhMDBhYTA2NjI0YTM5ZjU3IiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-911225737\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-333691688 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 18 Jul 2025 10:16:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-333691688\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost/socialvibe/_ignition/health-check", "action_name": "ignition.healthCheck", "controller_action": "Spatie\\LaravelIgnition\\Http\\Controllers\\HealthCheckController"}, "badge": null}}