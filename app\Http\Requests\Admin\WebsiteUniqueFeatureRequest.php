<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class WebsiteUniqueFeatureRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'icon'        => 'mimes:jpg,JPG,JPEG,jpeg,png,PNG,svg,webp,WEBP|max:5120',
            'image'       => 'mimes:jpg,JPG,JPEG,jpeg,png,PNG,svg,webp,WEBP|max:5120',
            'title'       => 'required',
            'description' => 'nullable',
        ];
    }
}
