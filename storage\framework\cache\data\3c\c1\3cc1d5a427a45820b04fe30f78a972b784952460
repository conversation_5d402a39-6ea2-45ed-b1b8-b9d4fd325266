9999999999O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:67:{i:0;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:1;s:4:"lang";s:2:"en";s:5:"title";s:13:"email_address";s:5:"value";s:18:""<EMAIL>"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:11:" * original";a:7:{s:2:"id";i:1;s:4:"lang";s:2:"en";s:5:"title";s:13:"email_address";s:5:"value";s:18:""<EMAIL>"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:1;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:2;s:4:"lang";s:2:"en";s:5:"title";s:5:"phone";s:5:"value";s:16:""+8801400620055"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:11:" * original";a:7:{s:2:"id";i:2;s:4:"lang";s:2:"en";s:5:"title";s:5:"phone";s:5:"value";s:16:""+8801400620055"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:2;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:3;s:4:"lang";s:2:"en";s:5:"title";s:11:"mail_server";s:5:"value";s:6:""smtp"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:11:" * original";a:7:{s:2:"id";i:3;s:4:"lang";s:2:"en";s:5:"title";s:11:"mail_server";s:5:"value";s:6:""smtp"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:3;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:4;s:4:"lang";s:2:"en";s:5:"title";s:19:"smtp_server_address";s:5:"value";s:23:""smtp.elasticemail.com"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:11:" * original";a:7:{s:2:"id";i:4;s:4:"lang";s:2:"en";s:5:"title";s:19:"smtp_server_address";s:5:"value";s:23:""smtp.elasticemail.com"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:4;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:5;s:4:"lang";s:2:"en";s:5:"title";s:14:"smtp_user_name";s:5:"value";s:0:"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:11:" * original";a:7:{s:2:"id";i:5;s:4:"lang";s:2:"en";s:5:"title";s:14:"smtp_user_name";s:5:"value";s:0:"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:5;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:6;s:4:"lang";s:2:"en";s:5:"title";s:13:"smtp_password";s:5:"value";s:0:"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:11:" * original";a:7:{s:2:"id";i:6;s:4:"lang";s:2:"en";s:5:"title";s:13:"smtp_password";s:5:"value";s:0:"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:6;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:7;s:4:"lang";s:2:"en";s:5:"title";s:14:"smtp_mail_port";s:5:"value";s:5:""465"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:11:" * original";a:7:{s:2:"id";i:7;s:4:"lang";s:2:"en";s:5:"title";s:14:"smtp_mail_port";s:5:"value";s:5:""465"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:7;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:8;s:4:"lang";s:2:"en";s:5:"title";s:20:"smtp_encryption_type";s:5:"value";s:5:""ssl"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:11:" * original";a:7:{s:2:"id";i:8;s:4:"lang";s:2:"en";s:5:"title";s:20:"smtp_encryption_type";s:5:"value";s:5:""ssl"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:8;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:9;s:4:"lang";s:2:"en";s:5:"title";s:19:"smtp_mail_from_name";s:5:"value";s:9:""SaleBot"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:11:" * original";a:7:{s:2:"id";i:9;s:4:"lang";s:2:"en";s:5:"title";s:19:"smtp_mail_from_name";s:5:"value";s:9:""SaleBot"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:9;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:10;s:4:"lang";s:2:"en";s:5:"title";s:17:"mail_from_address";s:5:"value";s:0:"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:11:" * original";a:7:{s:2:"id";i:10;s:4:"lang";s:2:"en";s:5:"title";s:17:"mail_from_address";s:5:"value";s:0:"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:10;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:11;s:4:"lang";s:2:"en";s:5:"title";s:14:"mail_signature";s:5:"value";s:32:""<p>Thanks</p><p>SocialVibe</p>"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:11:" * original";a:7:{s:2:"id";i:11;s:4:"lang";s:2:"en";s:5:"title";s:14:"mail_signature";s:5:"value";s:32:""<p>Thanks</p><p>SocialVibe</p>"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:11;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:12;s:4:"lang";s:2:"en";s:5:"title";s:20:"is_offline_activated";s:5:"value";s:3:""1"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:11:" * original";a:7:{s:2:"id";i:12;s:4:"lang";s:2:"en";s:5:"title";s:20:"is_offline_activated";s:5:"value";s:3:""1"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:12;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:13;s:4:"lang";s:2:"en";s:5:"title";s:27:"offline_payment_instruction";s:5:"value";s:648:""<p>Please deposit to our bank account at:<\/p><p><br><\/p><p>FIRST CENTURY BANK USA<\/p><p>Routing (ABA): *********<\/p><p>Account number: *************<\/p><p>Beneficiary name: Abdul Mannan<\/p><p>Bank address: 1731 N Elm St&nbsp; Commerce, GA 30529 USA<\/p><p>---<\/p><p><br><\/p><p>Standard Chatered Bank PLC, Bangladesh<\/p><p>SWIFT CODE: SCBLBDDX<\/p><p>Account number: ***********<\/p><p>Branch: Gulshan<\/p><p>Address: 67, Gulshan Avenue, Gulshan, Dhaka-1212<\/p><p>Beneficiary name: SpaGreen Creative<\/p><p><font color=\"#ce0000\">* Above is sample payment information, you can change it in your payment gateway setting page<\/font><\/p>"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:11:" * original";a:7:{s:2:"id";i:13;s:4:"lang";s:2:"en";s:5:"title";s:27:"offline_payment_instruction";s:5:"value";s:648:""<p>Please deposit to our bank account at:<\/p><p><br><\/p><p>FIRST CENTURY BANK USA<\/p><p>Routing (ABA): *********<\/p><p>Account number: *************<\/p><p>Beneficiary name: Abdul Mannan<\/p><p>Bank address: 1731 N Elm St&nbsp; Commerce, GA 30529 USA<\/p><p>---<\/p><p><br><\/p><p>Standard Chatered Bank PLC, Bangladesh<\/p><p>SWIFT CODE: SCBLBDDX<\/p><p>Account number: ***********<\/p><p>Branch: Gulshan<\/p><p>Address: 67, Gulshan Avenue, Gulshan, Dhaka-1212<\/p><p>Beneficiary name: SpaGreen Creative<\/p><p><font color=\"#ce0000\">* Above is sample payment information, you can change it in your payment gateway setting page<\/font><\/p>"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:13;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:14;s:4:"lang";s:2:"en";s:5:"title";s:10:"stripe_key";s:5:"value";s:34:""pk_test_2ttZXh7jmC7V9JKQENaq7SVH"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:11:" * original";a:7:{s:2:"id";i:14;s:4:"lang";s:2:"en";s:5:"title";s:10:"stripe_key";s:5:"value";s:34:""pk_test_2ttZXh7jmC7V9JKQENaq7SVH"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:14;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:15;s:4:"lang";s:2:"en";s:5:"title";s:13:"stripe_secret";s:5:"value";s:34:""sk_test_2qDzEY6o64liVUfurzLosjbM"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:11:" * original";a:7:{s:2:"id";i:15;s:4:"lang";s:2:"en";s:5:"title";s:13:"stripe_secret";s:5:"value";s:34:""sk_test_2qDzEY6o64liVUfurzLosjbM"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:15;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:16;s:4:"lang";s:2:"en";s:5:"title";s:19:"is_stripe_activated";s:5:"value";s:3:""1"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:11:" * original";a:7:{s:2:"id";i:16;s:4:"lang";s:2:"en";s:5:"title";s:19:"is_stripe_activated";s:5:"value";s:3:""1"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:16;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:17;s:4:"lang";s:2:"en";s:5:"title";s:14:"payment_method";s:5:"value";s:8:""paypal"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:11:" * original";a:7:{s:2:"id";i:17;s:4:"lang";s:2:"en";s:5:"title";s:14:"payment_method";s:5:"value";s:8:""paypal"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:17;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:18;s:4:"lang";s:2:"en";s:5:"title";s:16:"paypal_client_id";s:5:"value";s:82:""ARVXzUnLw-7OmHzIVqqUe_otKVpgoYhv1lFv1SG3OZpUF6cUhDmS-DPawzUsSImQm8ezKo7W0cX0yCjc"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:11:" * original";a:7:{s:2:"id";i:18;s:4:"lang";s:2:"en";s:5:"title";s:16:"paypal_client_id";s:5:"value";s:82:""ARVXzUnLw-7OmHzIVqqUe_otKVpgoYhv1lFv1SG3OZpUF6cUhDmS-DPawzUsSImQm8ezKo7W0cX0yCjc"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:18;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:19;s:4:"lang";s:2:"en";s:5:"title";s:20:"paypal_client_secret";s:5:"value";s:82:""EA8fXJIkp6xHHG9iJmDbiKkuGvaicoDSqI4TIvwRmGy8zdRcKx--3vV3SGNWfySBI_W3a_kNnhCnddRQ"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:11:" * original";a:7:{s:2:"id";i:19;s:4:"lang";s:2:"en";s:5:"title";s:20:"paypal_client_secret";s:5:"value";s:82:""EA8fXJIkp6xHHG9iJmDbiKkuGvaicoDSqI4TIvwRmGy8zdRcKx--3vV3SGNWfySBI_W3a_kNnhCnddRQ"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:19;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:20;s:4:"lang";s:2:"en";s:5:"title";s:32:"is_paypal_sandbox_mode_activated";s:5:"value";s:3:""1"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:11:" * original";a:7:{s:2:"id";i:20;s:4:"lang";s:2:"en";s:5:"title";s:32:"is_paypal_sandbox_mode_activated";s:5:"value";s:3:""1"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:20;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:21;s:4:"lang";s:2:"en";s:5:"title";s:19:"is_paypal_activated";s:5:"value";s:3:""1"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:11:" * original";a:7:{s:2:"id";i:21;s:4:"lang";s:2:"en";s:5:"title";s:19:"is_paypal_activated";s:5:"value";s:3:""1"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:21;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:22;s:4:"lang";s:2:"en";s:5:"title";s:14:"paddle_api_key";s:5:"value";s:53:""3dacf9f5b8f8285caab1a7e0a181fec1756af9db9de6c6625a "";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:11:" * original";a:7:{s:2:"id";i:22;s:4:"lang";s:2:"en";s:5:"title";s:14:"paddle_api_key";s:5:"value";s:53:""3dacf9f5b8f8285caab1a7e0a181fec1756af9db9de6c6625a "";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:22;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:23;s:4:"lang";s:2:"en";s:5:"title";s:19:"paddle_client_token";s:5:"value";s:34:""test_ee6ed044679e6efb75700af5ad5"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:11:" * original";a:7:{s:2:"id";i:23;s:4:"lang";s:2:"en";s:5:"title";s:19:"paddle_client_token";s:5:"value";s:34:""test_ee6ed044679e6efb75700af5ad5"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:23;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:24;s:4:"lang";s:2:"en";s:5:"title";s:32:"is_paddle_sandbox_mode_activated";s:5:"value";s:3:""1"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:11:" * original";a:7:{s:2:"id";i:24;s:4:"lang";s:2:"en";s:5:"title";s:32:"is_paddle_sandbox_mode_activated";s:5:"value";s:3:""1"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:24;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:25;s:4:"lang";s:2:"en";s:5:"title";s:19:"is_paddle_activated";s:5:"value";s:3:""1"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:11:" * original";a:7:{s:2:"id";i:25;s:4:"lang";s:2:"en";s:5:"title";s:19:"is_paddle_activated";s:5:"value";s:3:""1"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:25;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:26;s:4:"lang";s:2:"en";s:5:"title";s:19:"is_paypal_activated";s:5:"value";s:3:""1"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:11:" * original";a:7:{s:2:"id";i:26;s:4:"lang";s:2:"en";s:5:"title";s:19:"is_paypal_activated";s:5:"value";s:3:""1"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:26;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:27;s:4:"lang";s:2:"en";s:5:"title";s:21:"is_razorpay_activated";s:5:"value";s:3:""1"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:11:" * original";a:7:{s:2:"id";i:27;s:4:"lang";s:2:"en";s:5:"title";s:21:"is_razorpay_activated";s:5:"value";s:3:""1"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:27;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:28;s:4:"lang";s:2:"en";s:5:"title";s:12:"razorpay_key";s:5:"value";s:25:""rzp_test_0TxiJynxZFTbwx"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:11:" * original";a:7:{s:2:"id";i:28;s:4:"lang";s:2:"en";s:5:"title";s:12:"razorpay_key";s:5:"value";s:25:""rzp_test_0TxiJynxZFTbwx"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:28;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:29;s:4:"lang";s:2:"en";s:5:"title";s:15:"razorpay_secret";s:5:"value";s:26:""3WTWGfrzVjwTcVMgzy8phSpJ"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:11:" * original";a:7:{s:2:"id";i:29;s:4:"lang";s:2:"en";s:5:"title";s:15:"razorpay_secret";s:5:"value";s:26:""3WTWGfrzVjwTcVMgzy8phSpJ"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:29;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:30;s:4:"lang";s:2:"en";s:5:"title";s:8:"cron_key";s:5:"value";s:15:""687a1d6c77bb4"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:11:" * original";a:7:{s:2:"id";i:30;s:4:"lang";s:2:"en";s:5:"title";s:8:"cron_key";s:5:"value";s:15:""687a1d6c77bb4"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:30;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:31;s:4:"lang";s:2:"en";s:5:"title";s:16:"last_cron_run_at";s:5:"value";s:21:""2025-07-18 12:09:48"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:11:" * original";a:7:{s:2:"id";i:31;s:4:"lang";s:2:"en";s:5:"title";s:16:"last_cron_run_at";s:5:"value";s:21:""2025-07-18 12:09:48"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:31;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:32;s:4:"lang";s:2:"en";s:5:"title";s:21:"api_documentation_url";s:5:"value";s:3:""#"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:11:" * original";a:7:{s:2:"id";i:32;s:4:"lang";s:2:"en";s:5:"title";s:21:"api_documentation_url";s:5:"value";s:3:""#"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:32;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:33;s:4:"lang";s:2:"en";s:5:"title";s:22:"show_default_menu_link";s:5:"value";s:3:""1"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:11:" * original";a:7:{s:2:"id";i:33;s:4:"lang";s:2:"en";s:5:"title";s:22:"show_default_menu_link";s:5:"value";s:3:""1"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:33;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:34;s:4:"lang";s:2:"en";s:5:"title";s:10:"hero_title";s:5:"value";s:12:""Hero Title"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:11:" * original";a:7:{s:2:"id";i:34;s:4:"lang";s:2:"en";s:5:"title";s:10:"hero_title";s:5:"value";s:12:""Hero Title"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:34;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:35;s:4:"lang";s:2:"en";s:5:"title";s:16:"hero_description";s:5:"value";s:18:""Hero Description"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:11:" * original";a:7:{s:2:"id";i:35;s:4:"lang";s:2:"en";s:5:"title";s:16:"hero_description";s:5:"value";s:18:""Hero Description"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:35;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:36;s:4:"lang";s:2:"en";s:5:"title";s:27:"hero_main_action_btn_enable";s:5:"value";s:3:""1"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:11:" * original";a:7:{s:2:"id";i:36;s:4:"lang";s:2:"en";s:5:"title";s:27:"hero_main_action_btn_enable";s:5:"value";s:3:""1"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:36;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:37;s:4:"lang";s:2:"en";s:5:"title";s:26:"hero_main_action_btn_label";s:5:"value";s:8:""Action"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:11:" * original";a:7:{s:2:"id";i:37;s:4:"lang";s:2:"en";s:5:"title";s:26:"hero_main_action_btn_label";s:5:"value";s:8:""Action"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:37;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:38;s:4:"lang";s:2:"en";s:5:"title";s:27:"whatsApp_settings_video_url";s:5:"value";s:45:""https://www.youtube.com/watch?v=ipRTUj4LjOo"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:11:" * original";a:7:{s:2:"id";i:38;s:4:"lang";s:2:"en";s:5:"title";s:27:"whatsApp_settings_video_url";s:5:"value";s:45:""https://www.youtube.com/watch?v=ipRTUj4LjOo"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:38;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:39;s:4:"lang";s:2:"en";s:5:"title";s:27:"telegram_settings_video_url";s:5:"value";s:45:""https://www.youtube.com/watch?v=KD-inlsobbI"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:11:" * original";a:7:{s:2:"id";i:39;s:4:"lang";s:2:"en";s:5:"title";s:27:"telegram_settings_video_url";s:5:"value";s:45:""https://www.youtube.com/watch?v=KD-inlsobbI"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:39;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:40;s:4:"lang";s:2:"en";s:5:"title";s:15:"activation_code";s:5:"value";s:10:""PoolotHq"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:11:" * original";a:7:{s:2:"id";i:40;s:4:"lang";s:2:"en";s:5:"title";s:15:"activation_code";s:5:"value";s:10:""PoolotHq"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:40;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:41;s:4:"lang";s:2:"en";s:5:"title";s:12:"version_code";s:5:"value";s:7:""2.1.0"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:11:" * original";a:7:{s:2:"id";i:41;s:4:"lang";s:2:"en";s:5:"title";s:12:"version_code";s:5:"value";s:7:""2.1.0"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:41;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:42;s:4:"lang";s:2:"en";s:5:"title";s:15:"current_version";s:5:"value";s:5:""210"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:11:" * original";a:7:{s:2:"id";i:42;s:4:"lang";s:2:"en";s:5:"title";s:15:"current_version";s:5:"value";s:5:""210"";s:6:"status";i:1;s:10:"created_at";s:19:"2025-07-18 12:09:48";s:10:"updated_at";s:19:"2025-07-18 12:09:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:42;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:43;s:4:"lang";s:2:"en";s:5:"title";s:20:"client_section_title";s:5:"value";s:71:""Trusted by the fastest growing brands in rapidly developing countries"";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:16:13";s:10:"updated_at";s:19:"2024-03-16 10:16:13";}s:11:" * original";a:7:{s:2:"id";i:43;s:4:"lang";s:2:"en";s:5:"title";s:20:"client_section_title";s:5:"value";s:71:""Trusted by the fastest growing brands in rapidly developing countries"";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:16:13";s:10:"updated_at";s:19:"2024-03-16 10:16:13";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:43;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:44;s:4:"lang";s:2:"en";s:5:"title";s:19:"story_section_title";s:5:"value";s:19:""Our Success Story"";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:16:13";s:10:"updated_at";s:19:"2024-03-16 10:16:13";}s:11:" * original";a:7:{s:2:"id";i:44;s:4:"lang";s:2:"en";s:5:"title";s:19:"story_section_title";s:5:"value";s:19:""Our Success Story"";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:16:13";s:10:"updated_at";s:19:"2024-03-16 10:16:13";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:44;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:45;s:4:"lang";s:2:"en";s:5:"title";s:28:"unique_feature_section_title";s:5:"value";s:21:""Our Unique Features"";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:16:13";s:10:"updated_at";s:19:"2024-03-16 10:16:13";}s:11:" * original";a:7:{s:2:"id";i:45;s:4:"lang";s:2:"en";s:5:"title";s:28:"unique_feature_section_title";s:5:"value";s:21:""Our Unique Features"";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:16:13";s:10:"updated_at";s:19:"2024-03-16 10:16:13";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:45;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:46;s:4:"lang";s:2:"en";s:5:"title";s:21:"feature_section_title";s:5:"value";s:44:""Say hello to Whatsapp & Telegram marketing"";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:16:13";s:10:"updated_at";s:19:"2024-03-16 10:16:13";}s:11:" * original";a:7:{s:2:"id";i:46;s:4:"lang";s:2:"en";s:5:"title";s:21:"feature_section_title";s:5:"value";s:44:""Say hello to Whatsapp & Telegram marketing"";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:16:13";s:10:"updated_at";s:19:"2024-03-16 10:16:13";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:46;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:47;s:4:"lang";s:2:"en";s:5:"title";s:25:"testimonial_section_title";s:5:"value";s:27:""Testimonial Section Title"";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:16:13";s:10:"updated_at";s:19:"2024-03-16 10:16:13";}s:11:" * original";a:7:{s:2:"id";i:47;s:4:"lang";s:2:"en";s:5:"title";s:25:"testimonial_section_title";s:5:"value";s:27:""Testimonial Section Title"";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:16:13";s:10:"updated_at";s:19:"2024-03-16 10:16:13";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:47;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:48;s:4:"lang";s:2:"en";s:5:"title";s:21:"pricing_section_title";s:5:"value";s:29:""Simple Pricing For Everyone"";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:16:13";s:10:"updated_at";s:19:"2024-03-16 10:16:13";}s:11:" * original";a:7:{s:2:"id";i:48;s:4:"lang";s:2:"en";s:5:"title";s:21:"pricing_section_title";s:5:"value";s:29:""Simple Pricing For Everyone"";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:16:13";s:10:"updated_at";s:19:"2024-03-16 10:16:13";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:48;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:49;s:4:"lang";s:2:"en";s:5:"title";s:23:"advantage_section_title";s:5:"value";s:30:""Choose the SaleBot advantage"";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:16:13";s:10:"updated_at";s:19:"2024-03-16 10:16:13";}s:11:" * original";a:7:{s:2:"id";i:49;s:4:"lang";s:2:"en";s:5:"title";s:23:"advantage_section_title";s:5:"value";s:30:""Choose the SaleBot advantage"";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:16:13";s:10:"updated_at";s:19:"2024-03-16 10:16:13";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:49;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:50;s:4:"lang";s:2:"en";s:5:"title";s:17:"faq_section_title";s:5:"value";s:28:""Frequently Asked Questions"";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:16:13";s:10:"updated_at";s:19:"2024-03-16 10:16:13";}s:11:" * original";a:7:{s:2:"id";i:50;s:4:"lang";s:2:"en";s:5:"title";s:17:"faq_section_title";s:5:"value";s:28:""Frequently Asked Questions"";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:16:13";s:10:"updated_at";s:19:"2024-03-16 10:16:13";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:50;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:51;s:4:"lang";s:2:"en";s:5:"title";s:22:"story_section_subtitle";s:5:"value";s:24:""Story Section Subtitle"";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:16:13";s:10:"updated_at";s:19:"2024-03-16 10:16:13";}s:11:" * original";a:7:{s:2:"id";i:51;s:4:"lang";s:2:"en";s:5:"title";s:22:"story_section_subtitle";s:5:"value";s:24:""Story Section Subtitle"";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:16:13";s:10:"updated_at";s:19:"2024-03-16 10:16:13";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:51;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:52;s:4:"lang";s:2:"en";s:5:"title";s:28:"testimonial_section_subtitle";s:5:"value";s:30:""Testimonial Section Subtitle"";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:16:13";s:10:"updated_at";s:19:"2024-03-16 10:16:13";}s:11:" * original";a:7:{s:2:"id";i:52;s:4:"lang";s:2:"en";s:5:"title";s:28:"testimonial_section_subtitle";s:5:"value";s:30:""Testimonial Section Subtitle"";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:16:13";s:10:"updated_at";s:19:"2024-03-16 10:16:13";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:52;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:53;s:4:"lang";s:2:"en";s:5:"title";s:24:"pricing_section_subtitle";s:5:"value";s:41:""Affordable pricing with zero setup fees"";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:16:13";s:10:"updated_at";s:19:"2024-03-16 10:16:13";}s:11:" * original";a:7:{s:2:"id";i:53;s:4:"lang";s:2:"en";s:5:"title";s:24:"pricing_section_subtitle";s:5:"value";s:41:""Affordable pricing with zero setup fees"";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:16:13";s:10:"updated_at";s:19:"2024-03-16 10:16:13";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:53;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:54;s:4:"lang";s:2:"en";s:5:"title";s:26:"advantage_section_subtitle";s:5:"value";s:72:""Use SaleBot to engage your prospects through the Whatsapp business API"";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:16:13";s:10:"updated_at";s:19:"2024-03-16 10:16:13";}s:11:" * original";a:7:{s:2:"id";i:54;s:4:"lang";s:2:"en";s:5:"title";s:26:"advantage_section_subtitle";s:5:"value";s:72:""Use SaleBot to engage your prospects through the Whatsapp business API"";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:16:13";s:10:"updated_at";s:19:"2024-03-16 10:16:13";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:54;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:55;s:4:"lang";s:2:"en";s:5:"title";s:20:"faq_section_subtitle";s:5:"value";s:67:""Still need help? Reach out to our dedicated support team anytime."";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:16:13";s:10:"updated_at";s:19:"2024-03-16 10:16:13";}s:11:" * original";a:7:{s:2:"id";i:55;s:4:"lang";s:2:"en";s:5:"title";s:20:"faq_section_subtitle";s:5:"value";s:67:""Still need help? Reach out to our dedicated support team anytime."";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:16:13";s:10:"updated_at";s:19:"2024-03-16 10:16:13";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:55;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:56;s:4:"lang";s:2:"en";s:5:"title";s:13:"contact_email";s:5:"value";s:20:""<EMAIL>"";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:42:16";s:10:"updated_at";s:19:"2024-03-16 10:42:16";}s:11:" * original";a:7:{s:2:"id";i:56;s:4:"lang";s:2:"en";s:5:"title";s:13:"contact_email";s:5:"value";s:20:""<EMAIL>"";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:42:16";s:10:"updated_at";s:19:"2024-03-16 10:42:16";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:56;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:57;s:4:"lang";s:2:"en";s:5:"title";s:13:"contact_phone";s:5:"value";s:16:""+8801400620055"";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:42:16";s:10:"updated_at";s:19:"2024-03-16 10:42:16";}s:11:" * original";a:7:{s:2:"id";i:57;s:4:"lang";s:2:"en";s:5:"title";s:13:"contact_phone";s:5:"value";s:16:""+8801400620055"";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:42:16";s:10:"updated_at";s:19:"2024-03-16 10:42:16";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:57;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:58;s:4:"lang";s:2:"en";s:5:"title";s:17:"high_lighted_text";s:5:"value";s:31:""Empower you sale with SaleBot"";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:42:16";s:10:"updated_at";s:19:"2024-03-16 10:42:16";}s:11:" * original";a:7:{s:2:"id";i:58;s:4:"lang";s:2:"en";s:5:"title";s:17:"high_lighted_text";s:5:"value";s:31:""Empower you sale with SaleBot"";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:42:16";s:10:"updated_at";s:19:"2024-03-16 10:42:16";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:58;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:59;s:4:"lang";s:2:"en";s:5:"title";s:4:"lang";s:5:"value";s:2:"""";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:42:56";s:10:"updated_at";s:19:"2024-03-16 10:42:56";}s:11:" * original";a:7:{s:2:"id";i:59;s:4:"lang";s:2:"en";s:5:"title";s:4:"lang";s:5:"value";s:2:"""";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:42:56";s:10:"updated_at";s:19:"2024-03-16 10:42:56";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:59;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:60;s:4:"lang";s:2:"en";s:5:"title";s:23:"footer_useful_link_menu";s:5:"value";s:192:""a:2:{i:0;a:3:{s:5:\"label\";s:14:\"Privacy Policy\";s:3:\"url\";s:1:\"#\";s:9:\"mega_menu\";N;}i:1;a:3:{s:5:\"label\";s:18:\"Terms & Conditions\";s:3:\"url\";s:1:\"#\";s:9:\"mega_menu\";N;}}"";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:42:56";s:10:"updated_at";s:19:"2024-03-16 10:42:56";}s:11:" * original";a:7:{s:2:"id";i:60;s:4:"lang";s:2:"en";s:5:"title";s:23:"footer_useful_link_menu";s:5:"value";s:192:""a:2:{i:0;a:3:{s:5:\"label\";s:14:\"Privacy Policy\";s:3:\"url\";s:1:\"#\";s:9:\"mega_menu\";N;}i:1;a:3:{s:5:\"label\";s:18:\"Terms & Conditions\";s:3:\"url\";s:1:\"#\";s:9:\"mega_menu\";N;}}"";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:42:56";s:10:"updated_at";s:19:"2024-03-16 10:42:56";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:60;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:61;s:4:"lang";s:2:"en";s:5:"title";s:22:"footer_quick_link_menu";s:5:"value";s:260:""a:3:{i:0;a:3:{s:5:\"label\";s:11:\"Contacts Us\";s:3:\"url\";s:1:\"#\";s:9:\"mega_menu\";N;}i:1;a:3:{s:5:\"label\";s:8:\"Features\";s:3:\"url\";s:1:\"#\";s:9:\"mega_menu\";N;}i:2;a:3:{s:5:\"label\";s:7:\"Pricing\";s:3:\"url\";s:1:\"#\";s:9:\"mega_menu\";N;}}"";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:43:52";s:10:"updated_at";s:19:"2024-03-16 10:43:52";}s:11:" * original";a:7:{s:2:"id";i:61;s:4:"lang";s:2:"en";s:5:"title";s:22:"footer_quick_link_menu";s:5:"value";s:260:""a:3:{i:0;a:3:{s:5:\"label\";s:11:\"Contacts Us\";s:3:\"url\";s:1:\"#\";s:9:\"mega_menu\";N;}i:1;a:3:{s:5:\"label\";s:8:\"Features\";s:3:\"url\";s:1:\"#\";s:9:\"mega_menu\";N;}i:2;a:3:{s:5:\"label\";s:7:\"Pricing\";s:3:\"url\";s:1:\"#\";s:9:\"mega_menu\";N;}}"";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:43:52";s:10:"updated_at";s:19:"2024-03-16 10:43:52";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:61;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:62;s:4:"lang";s:2:"en";s:5:"title";s:26:"show_payment_method_banner";s:5:"value";s:3:""1"";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:43:59";s:10:"updated_at";s:19:"2024-03-16 10:43:59";}s:11:" * original";a:7:{s:2:"id";i:62;s:4:"lang";s:2:"en";s:5:"title";s:26:"show_payment_method_banner";s:5:"value";s:3:""1"";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:43:59";s:10:"updated_at";s:19:"2024-03-16 10:43:59";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:62;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:63;s:4:"lang";s:2:"en";s:5:"title";s:8:"is_modal";s:5:"value";s:3:""0"";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:45:27";s:10:"updated_at";s:19:"2024-03-16 10:45:27";}s:11:" * original";a:7:{s:2:"id";i:63;s:4:"lang";s:2:"en";s:5:"title";s:8:"is_modal";s:5:"value";s:3:""0"";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:45:27";s:10:"updated_at";s:19:"2024-03-16 10:45:27";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:63;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:64;s:4:"lang";s:2:"en";s:5:"title";s:9:"cta_title";s:5:"value";s:44:""Say hello to Whatsapp & Telegram Marketing"";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:45:27";s:10:"updated_at";s:19:"2024-03-16 10:45:27";}s:11:" * original";a:7:{s:2:"id";i:64;s:4:"lang";s:2:"en";s:5:"title";s:9:"cta_title";s:5:"value";s:44:""Say hello to Whatsapp & Telegram Marketing"";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:45:27";s:10:"updated_at";s:19:"2024-03-16 10:45:27";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:64;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:65;s:4:"lang";s:2:"en";s:5:"title";s:25:"cta_main_action_btn_label";s:5:"value";s:17:""Get Started Now"";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:45:27";s:10:"updated_at";s:19:"2024-03-16 10:45:27";}s:11:" * original";a:7:{s:2:"id";i:65;s:4:"lang";s:2:"en";s:5:"title";s:25:"cta_main_action_btn_label";s:5:"value";s:17:""Get Started Now"";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:45:27";s:10:"updated_at";s:19:"2024-03-16 10:45:27";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:65;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:66;s:4:"lang";s:2:"en";s:5:"title";s:23:"cta_main_action_btn_url";s:5:"value";s:3:""#"";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:45:27";s:10:"updated_at";s:19:"2024-03-16 10:45:27";}s:11:" * original";a:7:{s:2:"id";i:66;s:4:"lang";s:2:"en";s:5:"title";s:23:"cta_main_action_btn_url";s:5:"value";s:3:""#"";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:45:27";s:10:"updated_at";s:19:"2024-03-16 10:45:27";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:66;O:18:"App\Models\Setting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:67;s:4:"lang";s:2:"en";s:5:"title";s:10:"cta_enable";s:5:"value";s:3:""1"";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:45:27";s:10:"updated_at";s:19:"2024-03-16 10:45:27";}s:11:" * original";a:7:{s:2:"id";i:67;s:4:"lang";s:2:"en";s:5:"title";s:10:"cta_enable";s:5:"value";s:3:""1"";s:6:"status";i:1;s:10:"created_at";s:19:"2024-03-16 10:45:27";s:10:"updated_at";s:19:"2024-03-16 10:45:27";}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:5:"value";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:5:"title";i:1;s:5:"value";i:2;s:4:"lang";i:3;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}