<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Create default admin user if it doesn't exist
        if (!User::where('email', '<EMAIL>')->exists()) {
            User::create([
                'id' => 1,
                'first_name' => 'Admin',
                'last_name' => 'User',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'user_type' => 'admin',
                'status' => 1,
                'email_verified_at' => now(),
                'is_primary' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
}
