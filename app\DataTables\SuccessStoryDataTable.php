<?php

namespace App\DataTables;

use App\Models\SuccessStory;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;
use Ya<PERSON>ra\DataTables\EloquentDataTable;
use Ya<PERSON>ra\DataTables\Html\Builder as HtmlBuilder;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class SuccessStoryDataTable extends DataTable
{
    public function dataTable(QueryBuilder $query): EloquentDataTable
    {
        return (new EloquentDataTable($query))
            ->addIndexColumn()
            ->addColumn('action', function ($success_story) {
                return view('backend.admin.website.successStory.action', compact('success_story'));
            })->addColumn('status', function ($success_story) {
                return view('backend.admin.website.successStory.status', compact('success_story'));
            })->addColumn('image', function ($success_story) {
                return view('backend.admin.website.successStory.image', compact('success_story'));
            })->addColumn('title', function ($success_story) {
                return $success_story->lang_title;
            })->setRowId('id');
    }

    public function query(): QueryBuilder
    {
        $model = new SuccessStory();

        return $model->with('language')->newQuery();
    }

    public function html(): HtmlBuilder
    {
        return $this->builder()
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->orderBy(1)
            ->selectStyleSingle()
            ->setTableAttribute('style', 'width:99.8%')
            ->footerCallback('function ( row, data, start, end, display ) {

                $(".dataTables_length select").addClass("form-select form-select-lg without_search mb-3");
                selectionFields();
            }')
            ->parameters([
                'dom'        => 'Blfrtip',
                'buttons'    => [
                    [],
                ],
                'lengthMenu' => [[10, 25, 50, 100, 250], [10, 25, 50, 100, 250]],
                'language'   => [
                    'searchPlaceholder' => __('search'),
                    'lengthMenu'        => '_MENU_ '.__('success_story_per_page'),
                    'search'            => '',
                ],
            ]);
    }

    public function getColumns(): array
    {
        return [
            Column::computed('id')->data('DT_RowIndex')->title('#')->searchable(false)->width(10),
            Column::make('title')->title(__('title')),
            Column::computed('image')->title(__('image')),
            Column::computed('status')->title(__('status'))->exportable(false)
                ->printable(false)->width(10),
            Column::computed('action')->title(__('action'))
                ->exportable(false)
                ->printable(false)
                ->searchable(false)->addClass('action-card')->width(10),

        ];
    }

    protected function filename(): string
    {
        return 'success_story_'.date('YmdHis');
    }
}
