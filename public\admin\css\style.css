/*-------------------------------------
    Template Name: SocialVibe - Html Template
    Template URI: https://---.com
    Description:  
    Author: ...
    Author URI: https://---.com
    Version: 1.0
    Tags: theme, template, etc
---------------------------------------*/
/*
    Table of Context
    -------------------------------
    01. Common CSS
    02. Content Box
    03. Video Box
    04. Icon Box
    05. Order, Unorder List
    06. Round One side
    07. Overlap Image Box
    08. Image Box
    09. Icon list block

    01. Header style
    02. Banner style

*/
/*********************************
/* Fonts
/* Jost
/*
*********************************/
@import url("https://fonts.googleapis.com/css2?family=Jost:ital,wght@0,100..900;1,100..900&display=swap");
/**
 * For max-width media query, device width are sets as::after
 * xl = 1440
 * medium = 1199
 * tabland = 991
 * tabport = 767
 * phone
 * phone-sm
 * phone-mid
 * phone-xm
 */
:host,
:root {
  --rem: 10;
}

:root {
  --font-default: "Jost", sans-serif;
  --color-black: #000000;
  --color-black-rgb: 0, 0, 0;
  --color-white: #ffffff;
  --color-white-rgb: 255, 255, 255;
  --color-body: #ffffff;
  --color-primary: #2fabf7;
  --color-primary-rgb: 47, 171, 247;
  --color-heading: #1b1c1e;
  --color-heading2: #333333;
  --color-content: #666666;
  --color-bg: #fafafa;
  --color-border: #eeeeee;
  --color-green: #5bcd79;
  --color-yellow: #ffc935;
  --font-light: 300;
  --font-regular: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;
  --font-black: 900;
  --radius: 10px;
  --shadow: 0px 15px 35px 0px rgba(0, 0, 0, 0.1);
  --transition: all 300ms ease-in-out;
}

* {
  margin: 0;
  padding: 0;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}

html {
  font-size: 62.5%;
}

body {
  background: var(--color-body);
  font-family: var(--font-default);
  font-weight: var(--font-regular);
  font-size: 1.6rem;
  line-height: 2.7rem;
  scroll-behavior: smooth;
}

a {
  text-decoration: none;
}
a:hover, a:focus {
  text-decoration: none;
  outline: 0px;
}

button:hover, button:focus {
  outline: 0px;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: var(--font-regular);
}

img {
  display: block;
  width: 100%;
  height: auto;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

ul {
  margin: 0;
  padding: 0;
  list-style: inherit;
}

figure {
  margin: 0;
}

.gutters-5 {
  margin-right: -5px;
  margin-left: -5px;
}
.gutters-5 > .col,
.gutters-5 > [class*=col-] {
  padding-right: 5px;
  padding-left: 5px;
}

.gutters-10 {
  margin-right: -10px;
  margin-left: -10px;
}
.gutters-10 > .col,
.gutters-10 > [class*=col-] {
  padding-right: 10px;
  padding-left: 10px;
}

.gutters-15 {
  margin-right: -15px;
  margin-left: -15px;
}
.gutters-15 > .col,
.gutters-15 > [class*=col-] {
  padding-right: 15px;
  padding-left: 15px;
}

.gutters-20 {
  margin-right: -20px;
  margin-left: -20px;
}
.gutters-20 > .col,
.gutters-20 > [class*=col-] {
  padding-right: 20px;
  padding-left: 20px;
}

.gutters-25 {
  margin-right: -25px;
  margin-left: -25px;
}
.gutters-25 > .col,
.gutters-25 > [class*=col-] {
  padding-right: 25px;
  padding-left: 25px;
}

.mb-05 {
  margin-bottom: 5px !important;
}

.mb-10 {
  margin-bottom: 10px !important;
}

.mb-15 {
  margin-bottom: 15px !important;
}

.mb-20 {
  margin-bottom: 20px !important;
}

.mb-25 {
  margin-bottom: 25px !important;
}

.mb-30 {
  margin-bottom: 30px !important;
}

.mb-35 {
  margin-bottom: 35px !important;
}

.mb-40 {
  margin-bottom: 40px !important;
}

.mb-45 {
  margin-bottom: 45px !important;
}

.mb-50 {
  margin-bottom: 50px !important;
}

.mb-55 {
  margin-bottom: 55px !important;
}

.mb-60 {
  margin-bottom: 60px !important;
}

.mb-65 {
  margin-bottom: 65px !important;
}

.mb-70 {
  margin-bottom: 70px !important;
}

.mb-75 {
  margin-bottom: 75px !important;
}

.mb-80 {
  margin-bottom: 80px !important;
}

.mb-85 {
  margin-bottom: 85px !important;
}

.mb-90 {
  margin-bottom: 90px !important;
}

.mb-95 {
  margin-bottom: 95px !important;
}

.mb-100 {
  margin-bottom: 100px !important;
}

.mb-105 {
  margin-bottom: 105px !important;
}

.mb-110 {
  margin-bottom: 110px !important;
}

.mb-115 {
  margin-bottom: 115px !important;
}

.mb-120 {
  margin-bottom: 120px !important;
}

.mb-125 {
  margin-bottom: 125px !important;
}

.mb-130 {
  margin-bottom: 130px !important;
}

.mb-135 {
  margin-bottom: 135px !important;
}

.mb-140 {
  margin-bottom: 140px !important;
}

.mb-145 {
  margin-bottom: 145px !important;
}

.mb-150 {
  margin-bottom: 150px !important;
}

.mt-05 {
  margin-top: 5px !important;
}

.mt-10 {
  margin-top: 10px !important;
}

.mt-15 {
  margin-top: 15px !important;
}

.mt-20 {
  margin-top: 20px !important;
}

.mt-25 {
  margin-top: 25px !important;
}

.mt-30 {
  margin-top: 30px !important;
}

.mt-35 {
  margin-top: 35px !important;
}

.mt-40 {
  margin-top: 40px !important;
}

.mt-45 {
  margin-top: 45px !important;
}

.mt-50 {
  margin-top: 50px !important;
}

.mt-55 {
  margin-top: 55px !important;
}

.mt-60 {
  margin-top: 60px !important;
}

.mt-65 {
  margin-top: 65px !important;
}

.mt-70 {
  margin-top: 70px !important;
}

.mt-75 {
  margin-top: 75px !important;
}

.mt-80 {
  margin-top: 80px !important;
}

.mt-85 {
  margin-top: 85px !important;
}

.mt-90 {
  margin-top: 90px !important;
}

.mt-95 {
  margin-top: 95px !important;
}

.mt-100 {
  margin-top: 100px !important;
}

.mt-105 {
  margin-top: 105px !important;
}

.mt-110 {
  margin-top: 110px !important;
}

.mt-115 {
  margin-top: 115px !important;
}

.mt-120 {
  margin-top: 120px !important;
}

.mt-125 {
  margin-top: 125px !important;
}

.mt-130 {
  margin-top: 130px !important;
}

.mt-135 {
  margin-top: 135px !important;
}

.mt-140 {
  margin-top: 140px !important;
}

.mt-145 {
  margin-top: 145px !important;
}

.mt-150 {
  margin-top: 150px !important;
}

.ml-05 {
  margin-left: 5px !important;
}

.ml-10 {
  margin-left: 10px !important;
}

.ml-15 {
  margin-left: 15px !important;
}

.ml-20 {
  margin-left: 20px !important;
}

.ml-25 {
  margin-left: 25px !important;
}

.ml-30 {
  margin-left: 30px !important;
}

.ml-35 {
  margin-left: 35px !important;
}

.ml-40 {
  margin-left: 40px !important;
}

.ml-45 {
  margin-left: 45px !important;
}

.ml-50 {
  margin-left: 50px !important;
}

.ml-55 {
  margin-left: 55px !important;
}

.ml-60 {
  margin-left: 60px !important;
}

.ml-65 {
  margin-left: 65px !important;
}

.ml-70 {
  margin-left: 70px !important;
}

.ml-75 {
  margin-left: 75px !important;
}

.ml-80 {
  margin-left: 80px !important;
}

.ml-85 {
  margin-left: 85px !important;
}

.ml-90 {
  margin-left: 90px !important;
}

.ml-95 {
  margin-left: 95px !important;
}

.ml-100 {
  margin-left: 100px !important;
}

.ml-105 {
  margin-left: 105px !important;
}

.ml-110 {
  margin-left: 110px !important;
}

.ml-115 {
  margin-left: 115px !important;
}

.ml-120 {
  margin-left: 120px !important;
}

.ml-125 {
  margin-left: 125px !important;
}

.ml-130 {
  margin-left: 130px !important;
}

.ml-135 {
  margin-left: 135px !important;
}

.ml-140 {
  margin-left: 140px !important;
}

.ml-145 {
  margin-left: 145px !important;
}

.ml-150 {
  margin-left: 150px !important;
}

.mr-05 {
  margin-right: 5px !important;
}

.mr-10 {
  margin-right: 10px !important;
}

.mr-15 {
  margin-right: 15px !important;
}

.mr-20 {
  margin-right: 20px !important;
}

.mr-25 {
  margin-right: 25px !important;
}

.mr-30 {
  margin-right: 30px !important;
}

.mr-35 {
  margin-right: 35px !important;
}

.mr-40 {
  margin-right: 40px !important;
}

.mr-45 {
  margin-right: 45px !important;
}

.mr-50 {
  margin-right: 50px !important;
}

.mr-55 {
  margin-right: 55px !important;
}

.mr-60 {
  margin-right: 60px !important;
}

.mr-65 {
  margin-right: 65px !important;
}

.mr-70 {
  margin-right: 70px !important;
}

.mr-75 {
  margin-right: 75px !important;
}

.mr-80 {
  margin-right: 80px !important;
}

.mr-85 {
  margin-right: 85px !important;
}

.mr-90 {
  margin-right: 90px !important;
}

.mr-95 {
  margin-right: 95px !important;
}

.mr-100 {
  margin-right: 100px !important;
}

.mr-105 {
  margin-right: 105px !important;
}

.mr-110 {
  margin-right: 110px !important;
}

.mr-115 {
  margin-right: 115px !important;
}

.mr-120 {
  margin-right: 120px !important;
}

.mr-125 {
  margin-right: 125px !important;
}

.mr-130 {
  margin-right: 130px !important;
}

.mr-135 {
  margin-right: 135px !important;
}

.mr-140 {
  margin-right: 140px !important;
}

.mr-145 {
  margin-right: 145px !important;
}

.mr-150 {
  margin-right: 150px !important;
}

.px-05 {
  padding-left: 5px !important;
  padding-right: 5px !important;
}

.px-10 {
  padding-left: 10px !important;
  padding-right: 10px !important;
}

.px-15 {
  padding-left: 15px !important;
  padding-right: 15px !important;
}

.px-20 {
  padding-left: 20px !important;
  padding-right: 20px !important;
}

.px-25 {
  padding-left: 25px !important;
  padding-right: 25px !important;
}

.px-30 {
  padding-left: 30px !important;
  padding-right: 30px !important;
}

.px-35 {
  padding-left: 35px !important;
  padding-right: 35px !important;
}

.px-40 {
  padding-left: 40px !important;
  padding-right: 40px !important;
}

.px-45 {
  padding-left: 45px !important;
  padding-right: 45px !important;
}

.px-50 {
  padding-left: 50px !important;
  padding-right: 50px !important;
}

.px-55 {
  padding-left: 55px !important;
  padding-right: 55px !important;
}

.px-60 {
  padding-left: 60px !important;
  padding-right: 60px !important;
}

.px-65 {
  padding-left: 65px !important;
  padding-right: 65px !important;
}

.px-70 {
  padding-left: 70px !important;
  padding-right: 70px !important;
}

.px-75 {
  padding-left: 75px !important;
  padding-right: 75px !important;
}

.px-80 {
  padding-left: 80px !important;
  padding-right: 80px !important;
}

.px-85 {
  padding-left: 85px !important;
  padding-right: 85px !important;
}

.px-90 {
  padding-left: 90px !important;
  padding-right: 90px !important;
}

.px-95 {
  padding-left: 95px !important;
  padding-right: 95px !important;
}

.px-100 {
  padding-left: 100px !important;
  padding-right: 100px !important;
}

.px-105 {
  padding-left: 105px !important;
  padding-right: 105px !important;
}

.px-110 {
  padding-left: 110px !important;
  padding-right: 110px !important;
}

.px-115 {
  padding-left: 115px !important;
  padding-right: 115px !important;
}

.px-120 {
  padding-left: 120px !important;
  padding-right: 120px !important;
}

.px-125 {
  padding-left: 125px !important;
  padding-right: 125px !important;
}

.px-130 {
  padding-left: 130px !important;
  padding-right: 130px !important;
}

.px-135 {
  padding-left: 135px !important;
  padding-right: 135px !important;
}

.px-140 {
  padding-left: 140px !important;
  padding-right: 140px !important;
}

.px-145 {
  padding-left: 145px !important;
  padding-right: 145px !important;
}

.px-150 {
  padding-left: 150px !important;
  padding-right: 150px !important;
}

.py-05 {
  padding-top: 5px !important;
  padding-bottom: 5px !important;
}

.py-10 {
  padding-top: 10px !important;
  padding-bottom: 10px !important;
}

.py-15 {
  padding-top: 15px !important;
  padding-bottom: 15px !important;
}

.py-20 {
  padding-top: 20px !important;
  padding-bottom: 20px !important;
}

.py-25 {
  padding-top: 25px !important;
  padding-bottom: 25px !important;
}

.py-30 {
  padding-top: 30px !important;
  padding-bottom: 30px !important;
}

.py-35 {
  padding-top: 35px !important;
  padding-bottom: 35px !important;
}

.py-40 {
  padding-top: 40px !important;
  padding-bottom: 40px !important;
}

.py-45 {
  padding-top: 45px !important;
  padding-bottom: 45px !important;
}

.py-50 {
  padding-top: 50px !important;
  padding-bottom: 50px !important;
}

.py-55 {
  padding-top: 55px !important;
  padding-bottom: 55px !important;
}

.py-60 {
  padding-top: 60px !important;
  padding-bottom: 60px !important;
}

.py-65 {
  padding-top: 65px !important;
  padding-bottom: 65px !important;
}

.py-70 {
  padding-top: 70px !important;
  padding-bottom: 70px !important;
}

.py-75 {
  padding-top: 75px !important;
  padding-bottom: 75px !important;
}

.py-80 {
  padding-top: 80px !important;
  padding-bottom: 80px !important;
}

.py-85 {
  padding-top: 85px !important;
  padding-bottom: 85px !important;
}

.py-90 {
  padding-top: 90px !important;
  padding-bottom: 90px !important;
}

.py-95 {
  padding-top: 95px !important;
  padding-bottom: 95px !important;
}

.py-100 {
  padding-top: 100px !important;
  padding-bottom: 100px !important;
}

.py-105 {
  padding-top: 105px !important;
  padding-bottom: 105px !important;
}

.py-110 {
  padding-top: 110px !important;
  padding-bottom: 110px !important;
}

.py-115 {
  padding-top: 115px !important;
  padding-bottom: 115px !important;
}

.py-120 {
  padding-top: 120px !important;
  padding-bottom: 120px !important;
}

.py-125 {
  padding-top: 125px !important;
  padding-bottom: 125px !important;
}

.py-130 {
  padding-top: 130px !important;
  padding-bottom: 130px !important;
}

.py-135 {
  padding-top: 135px !important;
  padding-bottom: 135px !important;
}

.py-140 {
  padding-top: 140px !important;
  padding-bottom: 140px !important;
}

.py-145 {
  padding-top: 145px !important;
  padding-bottom: 145px !important;
}

.py-150 {
  padding-top: 150px !important;
  padding-bottom: 150px !important;
}

.pb-05 {
  padding-bottom: 5px !important;
}

.pb-10 {
  padding-bottom: 10px !important;
}

.pb-15 {
  padding-bottom: 15px !important;
}

.pb-20 {
  padding-bottom: 20px !important;
}

.pb-25 {
  padding-bottom: 25px !important;
}

.pb-30 {
  padding-bottom: 30px !important;
}

.pb-35 {
  padding-bottom: 35px !important;
}

.pb-40 {
  padding-bottom: 40px !important;
}

.pb-45 {
  padding-bottom: 45px !important;
}

.pb-50 {
  padding-bottom: 50px !important;
}

.pb-55 {
  padding-bottom: 55px !important;
}

.pb-60 {
  padding-bottom: 60px !important;
}

.pb-65 {
  padding-bottom: 65px !important;
}

.pb-70 {
  padding-bottom: 70px !important;
}

.pb-75 {
  padding-bottom: 75px !important;
}

.pb-80 {
  padding-bottom: 80px !important;
}

.pb-85 {
  padding-bottom: 85px !important;
}

.pb-90 {
  padding-bottom: 90px !important;
}

.pb-95 {
  padding-bottom: 95px !important;
}

.pb-100 {
  padding-bottom: 100px !important;
}

.pb-105 {
  padding-bottom: 105px !important;
}

.pb-110 {
  padding-bottom: 110px !important;
}

.pb-115 {
  padding-bottom: 115px !important;
}

.pb-120 {
  padding-bottom: 120px !important;
}

.pb-125 {
  padding-bottom: 125px !important;
}

.pb-130 {
  padding-bottom: 130px !important;
}

.pb-135 {
  padding-bottom: 135px !important;
}

.pb-140 {
  padding-bottom: 140px !important;
}

.pb-145 {
  padding-bottom: 145px !important;
}

.pb-150 {
  padding-bottom: 150px !important;
}

.pt-05 {
  padding-top: 5px !important;
}

.pt-10 {
  padding-top: 10px !important;
}

.pt-15 {
  padding-top: 15px !important;
}

.pt-20 {
  padding-top: 20px !important;
}

.pt-25 {
  padding-top: 25px !important;
}

.pt-30 {
  padding-top: 30px !important;
}

.pt-35 {
  padding-top: 35px !important;
}

.pt-40 {
  padding-top: 40px !important;
}

.pt-45 {
  padding-top: 45px !important;
}

.pt-50 {
  padding-top: 50px !important;
}

.pt-55 {
  padding-top: 55px !important;
}

.pt-60 {
  padding-top: 60px !important;
}

.pt-65 {
  padding-top: 65px !important;
}

.pt-70 {
  padding-top: 70px !important;
}

.pt-75 {
  padding-top: 75px !important;
}

.pt-80 {
  padding-top: 80px !important;
}

.pt-85 {
  padding-top: 85px !important;
}

.pt-90 {
  padding-top: 90px !important;
}

.pt-95 {
  padding-top: 95px !important;
}

.pt-100 {
  padding-top: 100px !important;
}

.pt-105 {
  padding-top: 105px !important;
}

.pt-110 {
  padding-top: 110px !important;
}

.pt-115 {
  padding-top: 115px !important;
}

.pt-120 {
  padding-top: 120px !important;
}

.pt-125 {
  padding-top: 125px !important;
}

.pt-130 {
  padding-top: 130px !important;
}

.pt-135 {
  padding-top: 135px !important;
}

.pt-140 {
  padding-top: 140px !important;
}

.pt-145 {
  padding-top: 145px !important;
}

.pt-150 {
  padding-top: 150px !important;
}

.pl-05 {
  padding-left: 5px !important;
}

.pl-10 {
  padding-left: 10px !important;
}

.pl-15 {
  padding-left: 15px !important;
}

.pl-20 {
  padding-left: 20px !important;
}

.pl-25 {
  padding-left: 25px !important;
}

.pl-30 {
  padding-left: 30px !important;
}

.pl-35 {
  padding-left: 35px !important;
}

.pl-40 {
  padding-left: 40px !important;
}

.pl-45 {
  padding-left: 45px !important;
}

.pl-50 {
  padding-left: 50px !important;
}

.pl-55 {
  padding-left: 55px !important;
}

.pl-60 {
  padding-left: 60px !important;
}

.pl-65 {
  padding-left: 65px !important;
}

.pl-70 {
  padding-left: 70px !important;
}

.pl-75 {
  padding-left: 75px !important;
}

.pl-80 {
  padding-left: 80px !important;
}

.pl-85 {
  padding-left: 85px !important;
}

.pl-90 {
  padding-left: 90px !important;
}

.pl-95 {
  padding-left: 95px !important;
}

.pl-100 {
  padding-left: 100px !important;
}

.pl-105 {
  padding-left: 105px !important;
}

.pl-110 {
  padding-left: 110px !important;
}

.pl-115 {
  padding-left: 115px !important;
}

.pl-120 {
  padding-left: 120px !important;
}

.pl-125 {
  padding-left: 125px !important;
}

.pl-130 {
  padding-left: 130px !important;
}

.pl-135 {
  padding-left: 135px !important;
}

.pl-140 {
  padding-left: 140px !important;
}

.pl-145 {
  padding-left: 145px !important;
}

.pl-150 {
  padding-left: 150px !important;
}

.pr-05 {
  padding-right: 5px !important;
}

.pr-10 {
  padding-right: 10px !important;
}

.pr-15 {
  padding-right: 15px !important;
}

.pr-20 {
  padding-right: 20px !important;
}

.pr-25 {
  padding-right: 25px !important;
}

.pr-30 {
  padding-right: 30px !important;
}

.pr-35 {
  padding-right: 35px !important;
}

.pr-40 {
  padding-right: 40px !important;
}

.pr-45 {
  padding-right: 45px !important;
}

.pr-50 {
  padding-right: 50px !important;
}

.pr-55 {
  padding-right: 55px !important;
}

.pr-60 {
  padding-right: 60px !important;
}

.pr-65 {
  padding-right: 65px !important;
}

.pr-70 {
  padding-right: 70px !important;
}

.pr-75 {
  padding-right: 75px !important;
}

.pr-80 {
  padding-right: 80px !important;
}

.pr-85 {
  padding-right: 85px !important;
}

.pr-90 {
  padding-right: 90px !important;
}

.pr-95 {
  padding-right: 95px !important;
}

.pr-100 {
  padding-right: 100px !important;
}

.pr-105 {
  padding-right: 105px !important;
}

.pr-110 {
  padding-right: 110px !important;
}

.pr-115 {
  padding-right: 115px !important;
}

.pr-120 {
  padding-right: 120px !important;
}

.pr-125 {
  padding-right: 125px !important;
}

.pr-130 {
  padding-right: 130px !important;
}

.pr-135 {
  padding-right: 135px !important;
}

.pr-140 {
  padding-right: 140px !important;
}

.pr-145 {
  padding-right: 145px !important;
}

.pr-150 {
  padding-right: 150px !important;
}

@media screen and (max-width: 767px) {
  .px-sm-05 {
    padding-left: 5px !important;
    padding-right: 5px !important;
  }
  .px-sm-10 {
    padding-left: 10px !important;
    padding-right: 10px !important;
  }
  .px-sm-15 {
    padding-left: 15px !important;
    padding-right: 15px !important;
  }
  .px-sm-20 {
    padding-left: 20px !important;
    padding-right: 20px !important;
  }
  .px-sm-25 {
    padding-left: 25px !important;
    padding-right: 25px !important;
  }
  .px-sm-30 {
    padding-left: 30px !important;
    padding-right: 30px !important;
  }
  .px-sm-35 {
    padding-left: 35px !important;
    padding-right: 35px !important;
  }
  .px-sm-40 {
    padding-left: 40px !important;
    padding-right: 40px !important;
  }
  .px-sm-45 {
    padding-left: 45px !important;
    padding-right: 45px !important;
  }
  .px-sm-50 {
    padding-left: 50px !important;
    padding-right: 50px !important;
  }
  .px-sm-55 {
    padding-left: 55px !important;
    padding-right: 55px !important;
  }
  .px-sm-60 {
    padding-left: 60px !important;
    padding-right: 60px !important;
  }
  .px-sm-65 {
    padding-left: 65px !important;
    padding-right: 65px !important;
  }
  .px-sm-70 {
    padding-left: 70px !important;
    padding-right: 70px !important;
  }
  .px-sm-75 {
    padding-left: 75px !important;
    padding-right: 75px !important;
  }
  .px-sm-80 {
    padding-left: 80px !important;
    padding-right: 80px !important;
  }
  .px-sm-85 {
    padding-left: 85px !important;
    padding-right: 85px !important;
  }
  .px-sm-90 {
    padding-left: 90px !important;
    padding-right: 90px !important;
  }
  .px-sm-95 {
    padding-left: 95px !important;
    padding-right: 95px !important;
  }
  .px-sm-100 {
    padding-left: 100px !important;
    padding-right: 100px !important;
  }
  .px-sm-105 {
    padding-left: 105px !important;
    padding-right: 105px !important;
  }
  .px-sm-110 {
    padding-left: 110px !important;
    padding-right: 110px !important;
  }
  .px-sm-115 {
    padding-left: 115px !important;
    padding-right: 115px !important;
  }
  .px-sm-120 {
    padding-left: 120px !important;
    padding-right: 120px !important;
  }
  .px-sm-125 {
    padding-left: 125px !important;
    padding-right: 125px !important;
  }
  .px-sm-130 {
    padding-left: 130px !important;
    padding-right: 130px !important;
  }
  .px-sm-135 {
    padding-left: 135px !important;
    padding-right: 135px !important;
  }
  .px-sm-140 {
    padding-left: 140px !important;
    padding-right: 140px !important;
  }
  .px-sm-145 {
    padding-left: 145px !important;
    padding-right: 145px !important;
  }
  .px-sm-150 {
    padding-left: 150px !important;
    padding-right: 150px !important;
  }
  .py-sm-05 {
    padding-top: 5px !important;
    padding-bottom: 5px !important;
  }
  .py-sm-10 {
    padding-top: 10px !important;
    padding-bottom: 10px !important;
  }
  .py-sm-15 {
    padding-top: 15px !important;
    padding-bottom: 15px !important;
  }
  .py-sm-20 {
    padding-top: 20px !important;
    padding-bottom: 20px !important;
  }
  .py-sm-25 {
    padding-top: 25px !important;
    padding-bottom: 25px !important;
  }
  .py-sm-30 {
    padding-top: 30px !important;
    padding-bottom: 30px !important;
  }
  .py-sm-35 {
    padding-top: 35px !important;
    padding-bottom: 35px !important;
  }
  .py-sm-40 {
    padding-top: 40px !important;
    padding-bottom: 40px !important;
  }
  .py-sm-45 {
    padding-top: 45px !important;
    padding-bottom: 45px !important;
  }
  .py-sm-50 {
    padding-top: 50px !important;
    padding-bottom: 50px !important;
  }
  .py-sm-55 {
    padding-top: 55px !important;
    padding-bottom: 55px !important;
  }
  .py-sm-60 {
    padding-top: 60px !important;
    padding-bottom: 60px !important;
  }
  .py-sm-65 {
    padding-top: 65px !important;
    padding-bottom: 65px !important;
  }
  .py-sm-70 {
    padding-top: 70px !important;
    padding-bottom: 70px !important;
  }
  .py-sm-75 {
    padding-top: 75px !important;
    padding-bottom: 75px !important;
  }
  .py-sm-80 {
    padding-top: 80px !important;
    padding-bottom: 80px !important;
  }
  .py-sm-85 {
    padding-top: 85px !important;
    padding-bottom: 85px !important;
  }
  .py-sm-90 {
    padding-top: 90px !important;
    padding-bottom: 90px !important;
  }
  .py-sm-95 {
    padding-top: 95px !important;
    padding-bottom: 95px !important;
  }
  .py-sm-100 {
    padding-top: 100px !important;
    padding-bottom: 100px !important;
  }
  .py-sm-105 {
    padding-top: 105px !important;
    padding-bottom: 105px !important;
  }
  .py-sm-110 {
    padding-top: 110px !important;
    padding-bottom: 110px !important;
  }
  .py-sm-115 {
    padding-top: 115px !important;
    padding-bottom: 115px !important;
  }
  .py-sm-120 {
    padding-top: 120px !important;
    padding-bottom: 120px !important;
  }
  .py-sm-125 {
    padding-top: 125px !important;
    padding-bottom: 125px !important;
  }
  .py-sm-130 {
    padding-top: 130px !important;
    padding-bottom: 130px !important;
  }
  .py-sm-135 {
    padding-top: 135px !important;
    padding-bottom: 135px !important;
  }
  .py-sm-140 {
    padding-top: 140px !important;
    padding-bottom: 140px !important;
  }
  .py-sm-145 {
    padding-top: 145px !important;
    padding-bottom: 145px !important;
  }
  .py-sm-150 {
    padding-top: 150px !important;
    padding-bottom: 150px !important;
  }
  .pb-sm-05 {
    padding-bottom: 5px !important;
  }
  .pb-sm-10 {
    padding-bottom: 10px !important;
  }
  .pb-sm-15 {
    padding-bottom: 15px !important;
  }
  .pb-sm-20 {
    padding-bottom: 20px !important;
  }
  .pb-sm-25 {
    padding-bottom: 25px !important;
  }
  .pb-sm-30 {
    padding-bottom: 30px !important;
  }
  .pb-sm-35 {
    padding-bottom: 35px !important;
  }
  .pb-sm-40 {
    padding-bottom: 40px !important;
  }
  .pb-sm-45 {
    padding-bottom: 45px !important;
  }
  .pb-sm-50 {
    padding-bottom: 50px !important;
  }
  .pb-sm-55 {
    padding-bottom: 55px !important;
  }
  .pb-sm-60 {
    padding-bottom: 60px !important;
  }
  .pb-sm-65 {
    padding-bottom: 65px !important;
  }
  .pb-sm-70 {
    padding-bottom: 70px !important;
  }
  .pb-sm-75 {
    padding-bottom: 75px !important;
  }
  .pb-sm-80 {
    padding-bottom: 80px !important;
  }
  .pb-sm-85 {
    padding-bottom: 85px !important;
  }
  .pb-sm-90 {
    padding-bottom: 90px !important;
  }
  .pb-sm-95 {
    padding-bottom: 95px !important;
  }
  .pb-sm-100 {
    padding-bottom: 100px !important;
  }
  .pb-sm-105 {
    padding-bottom: 105px !important;
  }
  .pb-sm-110 {
    padding-bottom: 110px !important;
  }
  .pb-sm-115 {
    padding-bottom: 115px !important;
  }
  .pb-sm-120 {
    padding-bottom: 120px !important;
  }
  .pb-sm-125 {
    padding-bottom: 125px !important;
  }
  .pb-sm-130 {
    padding-bottom: 130px !important;
  }
  .pb-sm-135 {
    padding-bottom: 135px !important;
  }
  .pb-sm-140 {
    padding-bottom: 140px !important;
  }
  .pb-sm-145 {
    padding-bottom: 145px !important;
  }
  .pb-sm-150 {
    padding-bottom: 150px !important;
  }
  .pt-sm-05 {
    padding-top: 5px !important;
  }
  .pt-sm-10 {
    padding-top: 10px !important;
  }
  .pt-sm-15 {
    padding-top: 15px !important;
  }
  .pt-sm-20 {
    padding-top: 20px !important;
  }
  .pt-sm-25 {
    padding-top: 25px !important;
  }
  .pt-sm-30 {
    padding-top: 30px !important;
  }
  .pt-sm-35 {
    padding-top: 35px !important;
  }
  .pt-sm-40 {
    padding-top: 40px !important;
  }
  .pt-sm-45 {
    padding-top: 45px !important;
  }
  .pt-sm-50 {
    padding-top: 50px !important;
  }
  .pt-sm-55 {
    padding-top: 55px !important;
  }
  .pt-sm-60 {
    padding-top: 60px !important;
  }
  .pt-sm-65 {
    padding-top: 65px !important;
  }
  .pt-sm-70 {
    padding-top: 70px !important;
  }
  .pt-sm-75 {
    padding-top: 75px !important;
  }
  .pt-sm-80 {
    padding-top: 80px !important;
  }
  .pt-sm-85 {
    padding-top: 85px !important;
  }
  .pt-sm-90 {
    padding-top: 90px !important;
  }
  .pt-sm-95 {
    padding-top: 95px !important;
  }
  .pt-sm-100 {
    padding-top: 100px !important;
  }
  .pt-sm-105 {
    padding-top: 105px !important;
  }
  .pt-sm-110 {
    padding-top: 110px !important;
  }
  .pt-sm-115 {
    padding-top: 115px !important;
  }
  .pt-sm-120 {
    padding-top: 120px !important;
  }
  .pt-sm-125 {
    padding-top: 125px !important;
  }
  .pt-sm-130 {
    padding-top: 130px !important;
  }
  .pt-sm-135 {
    padding-top: 135px !important;
  }
  .pt-sm-140 {
    padding-top: 140px !important;
  }
  .pt-sm-145 {
    padding-top: 145px !important;
  }
  .pt-sm-150 {
    padding-top: 150px !important;
  }
  .pl-sm-05 {
    padding-left: 5px !important;
  }
  .pl-sm-10 {
    padding-left: 10px !important;
  }
  .pl-sm-15 {
    padding-left: 15px !important;
  }
  .pl-sm-20 {
    padding-left: 20px !important;
  }
  .pl-sm-25 {
    padding-left: 25px !important;
  }
  .pl-sm-30 {
    padding-left: 30px !important;
  }
  .pl-sm-35 {
    padding-left: 35px !important;
  }
  .pl-sm-40 {
    padding-left: 40px !important;
  }
  .pl-sm-45 {
    padding-left: 45px !important;
  }
  .pl-sm-50 {
    padding-left: 50px !important;
  }
  .pl-sm-55 {
    padding-left: 55px !important;
  }
  .pl-sm-60 {
    padding-left: 60px !important;
  }
  .pl-sm-65 {
    padding-left: 65px !important;
  }
  .pl-sm-70 {
    padding-left: 70px !important;
  }
  .pl-sm-75 {
    padding-left: 75px !important;
  }
  .pl-sm-80 {
    padding-left: 80px !important;
  }
  .pl-sm-85 {
    padding-left: 85px !important;
  }
  .pl-sm-90 {
    padding-left: 90px !important;
  }
  .pl-sm-95 {
    padding-left: 95px !important;
  }
  .pl-sm-100 {
    padding-left: 100px !important;
  }
  .pl-sm-105 {
    padding-left: 105px !important;
  }
  .pl-sm-110 {
    padding-left: 110px !important;
  }
  .pl-sm-115 {
    padding-left: 115px !important;
  }
  .pl-sm-120 {
    padding-left: 120px !important;
  }
  .pl-sm-125 {
    padding-left: 125px !important;
  }
  .pl-sm-130 {
    padding-left: 130px !important;
  }
  .pl-sm-135 {
    padding-left: 135px !important;
  }
  .pl-sm-140 {
    padding-left: 140px !important;
  }
  .pl-sm-145 {
    padding-left: 145px !important;
  }
  .pl-sm-150 {
    padding-left: 150px !important;
  }
  .pr-sm-05 {
    padding-right: 5px !important;
  }
  .pr-sm-10 {
    padding-right: 10px !important;
  }
  .pr-sm-15 {
    padding-right: 15px !important;
  }
  .pr-sm-20 {
    padding-right: 20px !important;
  }
  .pr-sm-25 {
    padding-right: 25px !important;
  }
  .pr-sm-30 {
    padding-right: 30px !important;
  }
  .pr-sm-35 {
    padding-right: 35px !important;
  }
  .pr-sm-40 {
    padding-right: 40px !important;
  }
  .pr-sm-45 {
    padding-right: 45px !important;
  }
  .pr-sm-50 {
    padding-right: 50px !important;
  }
  .pr-sm-55 {
    padding-right: 55px !important;
  }
  .pr-sm-60 {
    padding-right: 60px !important;
  }
  .pr-sm-65 {
    padding-right: 65px !important;
  }
  .pr-sm-70 {
    padding-right: 70px !important;
  }
  .pr-sm-75 {
    padding-right: 75px !important;
  }
  .pr-sm-80 {
    padding-right: 80px !important;
  }
  .pr-sm-85 {
    padding-right: 85px !important;
  }
  .pr-sm-90 {
    padding-right: 90px !important;
  }
  .pr-sm-95 {
    padding-right: 95px !important;
  }
  .pr-sm-100 {
    padding-right: 100px !important;
  }
  .pr-sm-105 {
    padding-right: 105px !important;
  }
  .pr-sm-110 {
    padding-right: 110px !important;
  }
  .pr-sm-115 {
    padding-right: 115px !important;
  }
  .pr-sm-120 {
    padding-right: 120px !important;
  }
  .pr-sm-125 {
    padding-right: 125px !important;
  }
  .pr-sm-130 {
    padding-right: 130px !important;
  }
  .pr-sm-135 {
    padding-right: 135px !important;
  }
  .pr-sm-140 {
    padding-right: 140px !important;
  }
  .pr-sm-145 {
    padding-right: 145px !important;
  }
  .pr-sm-150 {
    padding-right: 150px !important;
  }
}
.mw-10 {
  max-width: 100px !important;
}

.w-10 {
  width: 100px !important;
}

.mw-11 {
  max-width: 110px !important;
}

.w-11 {
  width: 110px !important;
}

.mw-12 {
  max-width: 120px !important;
}

.w-12 {
  width: 120px !important;
}

.mw-13 {
  max-width: 130px !important;
}

.w-13 {
  width: 130px !important;
}

.mw-14 {
  max-width: 140px !important;
}

.w-14 {
  width: 140px !important;
}

.mw-15 {
  max-width: 150px !important;
}

.w-15 {
  width: 150px !important;
}

.mw-16 {
  max-width: 160px !important;
}

.w-16 {
  width: 160px !important;
}

.mw-17 {
  max-width: 170px !important;
}

.w-17 {
  width: 170px !important;
}

.mw-18 {
  max-width: 180px !important;
}

.w-18 {
  width: 180px !important;
}

.mw-19 {
  max-width: 190px !important;
}

.w-19 {
  width: 190px !important;
}

.mw-20 {
  max-width: 200px !important;
}

.w-20 {
  width: 200px !important;
}

.mw-21 {
  max-width: 210px !important;
}

.w-21 {
  width: 210px !important;
}

.mw-22 {
  max-width: 220px !important;
}

.w-22 {
  width: 220px !important;
}

.mw-23 {
  max-width: 230px !important;
}

.w-23 {
  width: 230px !important;
}

.mw-24 {
  max-width: 240px !important;
}

.w-24 {
  width: 240px !important;
}

.mw-25 {
  max-width: 250px !important;
}

.w-25 {
  width: 250px !important;
}

.mw-26 {
  max-width: 260px !important;
}

.w-26 {
  width: 260px !important;
}

.mw-27 {
  max-width: 270px !important;
}

.w-27 {
  width: 270px !important;
}

.mw-28 {
  max-width: 280px !important;
}

.w-28 {
  width: 280px !important;
}

.mw-29 {
  max-width: 290px !important;
}

.w-29 {
  width: 290px !important;
}

.mw-30 {
  max-width: 300px !important;
}

.w-30 {
  width: 300px !important;
}

.mw-31 {
  max-width: 310px !important;
}

.w-31 {
  width: 310px !important;
}

.mw-32 {
  max-width: 320px !important;
}

.w-32 {
  width: 320px !important;
}

.mw-33 {
  max-width: 330px !important;
}

.w-33 {
  width: 330px !important;
}

.mw-34 {
  max-width: 340px !important;
}

.w-34 {
  width: 340px !important;
}

.mw-35 {
  max-width: 350px !important;
}

.w-35 {
  width: 350px !important;
}

.mw-36 {
  max-width: 360px !important;
}

.w-36 {
  width: 360px !important;
}

.mw-37 {
  max-width: 370px !important;
}

.w-37 {
  width: 370px !important;
}

.mw-38 {
  max-width: 380px !important;
}

.w-38 {
  width: 380px !important;
}

.mw-39 {
  max-width: 390px !important;
}

.w-39 {
  width: 390px !important;
}

.mw-40 {
  max-width: 400px !important;
}

.w-40 {
  width: 400px !important;
}

.mw-41 {
  max-width: 410px !important;
}

.w-41 {
  width: 410px !important;
}

.mw-42 {
  max-width: 420px !important;
}

.w-42 {
  width: 420px !important;
}

.mw-43 {
  max-width: 430px !important;
}

.w-43 {
  width: 430px !important;
}

.mw-44 {
  max-width: 440px !important;
}

.w-44 {
  width: 440px !important;
}

.mw-45 {
  max-width: 450px !important;
}

.w-45 {
  width: 450px !important;
}

.mw-46 {
  max-width: 460px !important;
}

.w-46 {
  width: 460px !important;
}

.mw-47 {
  max-width: 470px !important;
}

.w-47 {
  width: 470px !important;
}

.mw-48 {
  max-width: 480px !important;
}

.w-48 {
  width: 480px !important;
}

.mw-49 {
  max-width: 490px !important;
}

.w-49 {
  width: 490px !important;
}

.mw-50 {
  max-width: 500px !important;
}

.w-50 {
  width: 500px !important;
}

.mw-51 {
  max-width: 510px !important;
}

.w-51 {
  width: 510px !important;
}

.mw-52 {
  max-width: 520px !important;
}

.w-52 {
  width: 520px !important;
}

.mw-53 {
  max-width: 530px !important;
}

.w-53 {
  width: 530px !important;
}

.mw-54 {
  max-width: 540px !important;
}

.w-54 {
  width: 540px !important;
}

.mw-55 {
  max-width: 550px !important;
}

.w-55 {
  width: 550px !important;
}

.mw-56 {
  max-width: 560px !important;
}

.w-56 {
  width: 560px !important;
}

.mw-57 {
  max-width: 570px !important;
}

.w-57 {
  width: 570px !important;
}

.mw-58 {
  max-width: 580px !important;
}

.w-58 {
  width: 580px !important;
}

.mw-59 {
  max-width: 590px !important;
}

.w-59 {
  width: 590px !important;
}

.mw-60 {
  max-width: 600px !important;
}

.w-60 {
  width: 600px !important;
}

.mw-61 {
  max-width: 610px !important;
}

.w-61 {
  width: 610px !important;
}

.mw-62 {
  max-width: 620px !important;
}

.w-62 {
  width: 620px !important;
}

.mw-63 {
  max-width: 630px !important;
}

.w-63 {
  width: 630px !important;
}

.mw-64 {
  max-width: 640px !important;
}

.w-64 {
  width: 640px !important;
}

.mw-65 {
  max-width: 650px !important;
}

.w-65 {
  width: 650px !important;
}

.mw-66 {
  max-width: 660px !important;
}

.w-66 {
  width: 660px !important;
}

.mw-67 {
  max-width: 670px !important;
}

.w-67 {
  width: 670px !important;
}

.mw-68 {
  max-width: 680px !important;
}

.w-68 {
  width: 680px !important;
}

.mw-69 {
  max-width: 690px !important;
}

.w-69 {
  width: 690px !important;
}

.mw-70 {
  max-width: 700px !important;
}

.w-70 {
  width: 700px !important;
}

.mw-71 {
  max-width: 710px !important;
}

.w-71 {
  width: 710px !important;
}

.mw-72 {
  max-width: 720px !important;
}

.w-72 {
  width: 720px !important;
}

.mw-73 {
  max-width: 730px !important;
}

.w-73 {
  width: 730px !important;
}

.mw-74 {
  max-width: 740px !important;
}

.w-74 {
  width: 740px !important;
}

.mw-75 {
  max-width: 750px !important;
}

.w-75 {
  width: 750px !important;
}

.mw-76 {
  max-width: 760px !important;
}

.w-76 {
  width: 760px !important;
}

.mw-77 {
  max-width: 770px !important;
}

.w-77 {
  width: 770px !important;
}

.mw-78 {
  max-width: 780px !important;
}

.w-78 {
  width: 780px !important;
}

.mw-79 {
  max-width: 790px !important;
}

.w-79 {
  width: 790px !important;
}

.mw-80 {
  max-width: 800px !important;
}

.w-80 {
  width: 800px !important;
}

.mw-81 {
  max-width: 810px !important;
}

.w-81 {
  width: 810px !important;
}

.mw-82 {
  max-width: 820px !important;
}

.w-82 {
  width: 820px !important;
}

.mw-83 {
  max-width: 830px !important;
}

.w-83 {
  width: 830px !important;
}

.mw-84 {
  max-width: 840px !important;
}

.w-84 {
  width: 840px !important;
}

.mw-85 {
  max-width: 850px !important;
}

.w-85 {
  width: 850px !important;
}

.mw-86 {
  max-width: 860px !important;
}

.w-86 {
  width: 860px !important;
}

.mw-87 {
  max-width: 870px !important;
}

.w-87 {
  width: 870px !important;
}

.mw-88 {
  max-width: 880px !important;
}

.w-88 {
  width: 880px !important;
}

.mw-89 {
  max-width: 890px !important;
}

.w-89 {
  width: 890px !important;
}

.mw-90 {
  max-width: 900px !important;
}

.w-90 {
  width: 900px !important;
}

.mw-91 {
  max-width: 910px !important;
}

.w-91 {
  width: 910px !important;
}

.mw-92 {
  max-width: 920px !important;
}

.w-92 {
  width: 920px !important;
}

.mw-93 {
  max-width: 930px !important;
}

.w-93 {
  width: 930px !important;
}

.mw-94 {
  max-width: 940px !important;
}

.w-94 {
  width: 940px !important;
}

.mw-95 {
  max-width: 950px !important;
}

.w-95 {
  width: 950px !important;
}

.mw-96 {
  max-width: 960px !important;
}

.w-96 {
  width: 960px !important;
}

.mw-97 {
  max-width: 970px !important;
}

.w-97 {
  width: 970px !important;
}

.mw-98 {
  max-width: 980px !important;
}

.w-98 {
  width: 980px !important;
}

.mw-99 {
  max-width: 990px !important;
}

.w-99 {
  width: 990px !important;
}

.text-10 {
  font-size: 10px !important;
}

.text-11 {
  font-size: 11px !important;
}

.text-12 {
  font-size: 12px !important;
}

.text-13 {
  font-size: 13px !important;
}

.text-14 {
  font-size: 14px !important;
}

.text-15 {
  font-size: 15px !important;
}

.text-16 {
  font-size: 16px !important;
}

.text-17 {
  font-size: 17px !important;
}

.text-18 {
  font-size: 18px !important;
}

.text-19 {
  font-size: 19px !important;
}

.text-20 {
  font-size: 20px !important;
}

.text-21 {
  font-size: 21px !important;
}

.text-22 {
  font-size: 22px !important;
}

.text-23 {
  font-size: 23px !important;
}

.text-24 {
  font-size: 24px !important;
}

.text-25 {
  font-size: 25px !important;
}

.text-26 {
  font-size: 26px !important;
}

.text-27 {
  font-size: 27px !important;
}

.text-28 {
  font-size: 28px !important;
}

.text-29 {
  font-size: 29px !important;
}

.text-30 {
  font-size: 30px !important;
}

.text-31 {
  font-size: 31px !important;
}

.text-32 {
  font-size: 32px !important;
}

/*********************************
/*  Common Css Start
*********************************/
.border-none {
  border: none !important;
}

.bg-white {
  background-color: var(--color-white) !important;
}

.bg-dark {
  background-color: var(--color-heading) !important;
}

.text-primary {
  color: var(--color-primary) !important;
}

/*********************************
/*  Input Apperance Start
*********************************/
input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
  -webkit-appearance: none;
}

input[type=number] {
  -moz-appearance: textfield !important;
}

input[type=date]::-webkit-calendar-picker-indicator {
  cursor: pointer;
  border-radius: 4px;
  margin-right: 2px;
}

/*********************************
/*  Input Apperance End
*********************************/
/*********************************
/*  Custom Scroll Bar Css
*********************************/
::-webkit-scrollbar {
  width: 4px;
  height: 4px;
  border-radius: var(--radius-8);
}

::-webkit-scrollbar-track {
  background-color: #ddd;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(var(--color-primary-rgb), 0.8);
}

/*********************************
/*  Custom Scroll Bar Css
*********************************/
/*********************************
/*  row Custom
*********************************/
.row > * {
  max-width: 100%;
  padding-right: calc(var(--bs-gutter-x) * 0.6);
  padding-left: calc(var(--bs-gutter-x) * 0.6);
  margin-top: var(--bs-gutter-y);
}

@media (min-width: 479px) and (max-width: 575px) {
  .col-xs-6 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
            flex: 0 0 auto;
    width: 50%;
  }
}
/*********************************
/*  Heading Typography
*********************************/
h1 {
  font-family: var(--font-default);
  font-size: 46px;
  line-height: 60px;
  font-weight: var(--font-bold);
  color: var(--color-heading);
}
@media screen and (max-width: 767px) {
  h1 {
    font-size: 38px;
    line-height: 50px;
  }
}
@media screen and (max-width: 575px) {
  h1 {
    font-size: 36px;
    line-height: 50px;
  }
}
@media screen and (max-width: 375px) {
  h1 {
    font-size: 28px;
    line-height: 36px;
  }
}

h2 {
  font-family: var(--font-default);
  font-size: 28px;
  line-height: 40px;
  font-weight: var(--font-medium);
  color: var(--color-heading);
}

h3 {
  font-family: var(--font-default);
  font-size: 24px;
  line-height: 32px;
  font-weight: var(--font-medium);
  color: var(--color-heading);
}

/*********************************
/*  Content Typography
*********************************/
p {
  font-family: var(--font-default);
  font-size: 16px;
  font-weight: var(--font-regular);
  line-height: 24px;
  color: var(--color-content);
}

/*********************************
/*  Button Start
*********************************/
.sg-btn {
  font-family: var(--font-default);
  font-weight: var(--font-regular);
  font-size: 16px;
  line-height: 18px;
  color: var(--color-heading);
  padding: 10px 15px;
  border: 1px solid var(--color-border);
  display: inline-block;
  text-transform: capitalize;
  text-align: center;
  border-radius: 30px;
  -webkit-transition: var(--transition);
  transition: var(--transition);
}
.sg-btn-primary {
  background: var(--color-primary);
  border: 1px solid var(--color-primary);
  color: var(--color-white);
}
.sg-btn-primary:hover {
  background: var(--color-heading);
  border: 1px solid var(--color-heading);
  color: var(--color-white);
}
.sg-btn-outline {
  background: transparent;
  border: 1px solid var(--color-border);
  color: var(--color-heading2);
}
.sg-btn-outline:hover {
  background: var(--color-heading);
  border: 1px solid var(--color-heading);
  color: var(--color-white);
}
.sg-btn-dark {
  background: var(--color-heading);
  border: 1px solid var(--color-heading);
  color: var(--color-white);
}
.sg-btn-dark:hover {
  background: var(--color-primary);
  border: 1px solid var(--color-primary);
  color: var(--color-white);
}
.sg-btn-white {
  background: var(--color-white);
  border: 1px solid var(--color-white);
  color: var(--color-content);
}
.sg-btn-white:hover {
  background: var(--color-heading);
  border: 1px solid var(--color-heading);
  color: var(--color-white);
}

.solid__btn {
  font-family: var(--font-default);
  font-weight: var(--font-regular);
  font-size: 16px;
  line-height: 24px;
  color: var(--color-content);
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 8px;
  text-transform: capitalize;
  -webkit-transition: var(--transition);
  transition: var(--transition);
}
.solid__btn:hover {
  color: var(--color-primary);
}

/*********************************
/*  Button End
*********************************/
/*********************************
/*  Section Tittle Start
*********************************/
.section__title-wrapper {
  max-width: 530px;
  margin-bottom: 32px;
}
.section__title-wrapper.flex-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  gap: 10px;
  max-width: 100%;
  margin-bottom: 32px;
  position: relative;
}
.section__title-wrapper.flex-item .title {
  margin-bottom: 0;
}
@media screen and (max-width: 991px) {
  .section__title-wrapper.flex-item {
    display: block;
    text-align: center;
  }
  .section__title-wrapper.flex-item .title {
    margin-bottom: 16px;
  }
}
.section__title-wrapper.text-center {
  margin: 0 auto 32px;
}
.section__title .subtitle {
  font-family: var(--font-default);
  font-size: 16px;
  font-weight: var(--font-regular);
  line-height: 24px;
  color: var(--color-white);
  text-transform: capitalize;
  position: relative;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 10px;
  background: var(--color-heading2);
  border-radius: 30px;
  padding: 6px 16px;
  margin-bottom: 20px;
}
.section__title .title {
  font-family: var(--font-default);
  font-size: 28px;
  line-height: 40px;
  font-weight: var(--font-medium);
  color: var(--color-heading);
  margin: 0;
}
.section__title p,
.section__title .desc {
  font-family: var(--font-default);
  font-size: 16px;
  font-weight: var(--font-regular);
  line-height: 24px;
  color: var(--color-heading2);
  margin: 24px 0 0;
}

/*********************************
/*  Section Tittle End
*********************************/
/*********************************
/*  Header Start
*********************************/
.header {
  background: var(--color-white);
  padding: 25px 0;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 999;
  -webkit-transition: var(--transition);
  transition: var(--transition);
}
@media screen and (max-width: 991px) {
  .header {
    padding: 15px 0;
  }
}
.header__wrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  gap: 20px;
}
.header__logo img {
  width: auto;
  height: 40px;
  display: block;
}
@media screen and (max-width: 575px) {
  .header__logo img {
    height: 35px;
  }
}
@media screen and (max-width: 991px) {
  .header__menu {
    position: fixed;
    width: 100%;
    height: 100vh;
    overflow: auto;
    padding: 12px 16px;
    background: var(--color-white);
    left: 0;
    top: 0;
    z-index: -1;
    -webkit-transform-origin: top;
            transform-origin: top;
    -webkit-transform: scaleY(0);
            transform: scaleY(0);
    -webkit-transition: var(--transition);
    transition: var(--transition);
    display: block;
  }
  .header__menu.mblMenu__open {
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
  .header__menu li a {
    font-size: 18px !important;
  }
}
.header__menu .main__menu {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 24px;
}
@media screen and (max-width: 991px) {
  .header__menu .main__menu {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    gap: 22px;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    height: 100%;
  }
}
.header__menu .main__menu li {
  list-style: none;
}
.header__menu .main__menu li a {
  font-family: var(--font-default);
  font-weight: var(--font-regular);
  font-size: 16px;
  line-height: 18px;
  color: var(--color-heading2);
  text-transform: capitalize;
  padding: 0;
  -webkit-transition: var(--transition);
  transition: var(--transition);
}
.header__menu .main__menu li a.active, .header__menu .main__menu li a:hover {
  color: var(--color-primary);
}
.header__menu .main__menu li .sg-btn {
  padding: 10px 15px;
}
.header__menu .main__menu li .sg-btn:hover {
  color: var(--color-white);
}
.header__menu .main__menu li .sg-btn-dark {
  color: var(--color-white);
}
.header__meta {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 16px;
}
.header__btn {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 16px;
}
.header__btn .sg-btn-outline:hover {
  background: var(--color-primary);
  border-color: var(--color-primary);
  color: var(--color-white);
}
.header__toggle {
  height: 40px;
  width: 45px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  border-radius: 4px;
  cursor: pointer;
  display: none;
  -webkit-transition: var(--transition);
  transition: var(--transition);
  z-index: 99;
}
@media screen and (max-width: 991px) {
  .header__toggle {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
  }
}
@media screen and (max-width: 479px) {
  .header__toggle {
    margin-left: 0;
  }
}
.header__toggle.active .toggle__bar {
  background: transparent;
}
.header__toggle.active .toggle__bar::before {
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
  top: 0;
}
.header__toggle.active .toggle__bar::after {
  -webkit-transform: rotate(-45deg);
          transform: rotate(-45deg);
  bottom: 0;
}
.header__toggle .toggle__bar {
  height: 2px;
  width: 25px;
  background: var(--color-heading);
  position: relative;
  -webkit-transition: var(--transition);
  transition: var(--transition);
}
.header__toggle .toggle__bar::after, .header__toggle .toggle__bar::before {
  content: "";
  position: absolute;
  top: -8px;
  height: 2px;
  width: 100%;
  background: var(--color-heading);
  -webkit-transition: var(--transition);
  transition: var(--transition);
}
.header__toggle .toggle__bar::after {
  top: auto;
  bottom: -8px;
}

.nav-bg {
  background: var(--color-white);
  -webkit-box-shadow: 0 22px 20px rgba(0, 0, 0, 0.05);
          box-shadow: 0 22px 20px rgba(0, 0, 0, 0.05);
  top: 0;
}

/*********************************
/*  Header End
*********************************/
/**************************************/
/* language Dropdown list Start */
/**************************************/
.language__dropdown {
  position: relative;
}
.language__dropdown .selected {
  font-family: var(--font-default);
  font-size: 16px;
  font-weight: var(--font-regular);
  line-height: 28px;
  text-transform: capitalize;
  color: var(--color-heading2);
  cursor: pointer;
  position: relative;
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.language__dropdown .selected::after {
  content: "\ea4e";
  font-family: "remixicon";
  font-size: 22px;
  position: relative;
  top: 0;
  right: -4px;
  height: 20px;
  width: 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  font-weight: var(--font-regular);
}
.language__dropdown .dropdown__list {
  list-style: none;
  position: absolute;
  right: 0;
  top: 170%;
  width: 150px;
  background: var(--color-white);
  border-radius: 12px;
  padding: 16px 16px 8px;
  z-index: 99;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: var(--transition);
  transition: var(--transition);
  border: 1px solid var(--color-border);
  -webkit-box-shadow: 0px 32px 50px 0px rgba(0, 0, 0, 0.1019607843);
          box-shadow: 0px 32px 50px 0px rgba(0, 0, 0, 0.1019607843);
  overflow-y: auto;
}
.language__dropdown .dropdown__list.active {
  opacity: 1;
  visibility: visible;
  top: 140%;
}
.language__dropdown .dropdown__list li a {
  font-family: var(--font-default);
  font-size: 16px;
  line-height: 100%;
  font-weight: var(--font-regular);
  color: var(--color-content);
  padding: 7.5px 0;
  display: block;
  -webkit-transition: var(--transition);
  transition: var(--transition);
  cursor: pointer;
}
.language__dropdown .dropdown__list li a:hover {
  color: var(--color-primary);
  border-color: var(--color-primary);
}
.language__dropdown .dropdown__list li:last-child a {
  border: none;
}

/**************************************/
/* language Dropdown list End */
/**************************************/
/*********************************
/*  Banner Section Start
*********************************/
.banner__wrapper {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 20px;
  background: url("../images/banner/banner-bg.png");
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  border-radius: var(--radius);
  padding: 26px 0 50px 75px;
  border: 1px solid rgba(var(--color-primary-rgb), 0.1);
  position: relative;
  margin-top: 90px;
}
@media screen and (max-width: 991px) {
  .banner__wrapper {
    grid-template-columns: auto;
    margin-top: 70px;
    padding: 50px 0 50px 75px;
  }
}
@media screen and (max-width: 767px) {
  .banner__wrapper {
    padding: 50px 10px 50px 20px;
  }
}

.banner__text .subtitle {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 10px;
  font-family: var(--font-default);
  font-weight: var(--font-regular);
  font-size: 16px;
  line-height: 24px;
  color: var(--color-heading2);
  margin: 0;
  padding: 6px 16px;
  border-radius: 30px;
  background: var(--color-white);
  margin-bottom: 14px;
}
.banner__text .subtitle img {
  width: 24px;
  height: auto;
  display: block;
}
.banner__text .title {
  font-family: var(--font-default);
  font-size: 46px;
  line-height: 60px;
  font-weight: var(--font-bold);
  color: var(--color-heading);
  margin-bottom: 14px;
}
.banner__text .title span {
  color: var(--color-green);
}
@media screen and (max-width: 767px) {
  .banner__text .title {
    font-size: 38px;
    line-height: 50px;
  }
}
@media screen and (max-width: 575px) {
  .banner__text .title {
    font-size: 36px;
    line-height: 50px;
  }
}
@media screen and (max-width: 375px) {
  .banner__text .title {
    font-size: 28px;
    line-height: 36px;
  }
}
.banner__text .desc {
  font-family: var(--font-default);
  font-size: 16px;
  font-weight: var(--font-regular);
  line-height: 24px;
  color: var(--color-heading2);
  max-width: 440px;
  margin: 0;
}
.banner__text .btn__group {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 14px;
  margin-top: 24px;
}
.banner__text .popup__btn {
  font-size: 20px;
  color: var(--color-heading2);
  height: 40px;
  width: 40px;
  background: var(--color-white);
  border-radius: 50%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.0196078431);
          box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.0196078431);
  -webkit-animation: pulse-animation 2s infinite;
          animation: pulse-animation 2s infinite;
  -webkit-transition: var(--transition);
  transition: var(--transition);
}
.banner__text .popup__btn svg {
  margin-left: 2px;
}
.banner__text .popup__btn svg path {
  fill: var(--color-heading2);
  -webkit-transition: var(--transition);
  transition: var(--transition);
}
.banner__text .popup__btn:hover {
  background: var(--color-heading);
}
.banner__text .popup__btn:hover svg path {
  fill: var(--color-white);
}

.banner__thumb img {
  width: 100%;
  height: auto;
  display: block;
  max-height: 501px;
  margin-left: auto;
}
.banner__thumb .banner__avatar {
  max-width: 178px;
  position: absolute;
  bottom: -30px;
  left: -70px;
}
@media screen and (max-width: 991px) {
  .banner__thumb .banner__avatar {
    left: -65px;
  }
}
@media screen and (max-width: 767px) {
  .banner__thumb .banner__avatar {
    display: none;
  }
}

@-webkit-keyframes pulse-animation {
  0% {
    -webkit-box-shadow: 0 0 0 0px rgba(0, 0, 0, 0.2);
            box-shadow: 0 0 0 0px rgba(0, 0, 0, 0.2);
  }
  100% {
    -webkit-box-shadow: 0 0 0 20px rgba(0, 0, 0, 0);
            box-shadow: 0 0 0 20px rgba(0, 0, 0, 0);
  }
}

@keyframes pulse-animation {
  0% {
    -webkit-box-shadow: 0 0 0 0px rgba(0, 0, 0, 0.2);
            box-shadow: 0 0 0 0px rgba(0, 0, 0, 0.2);
  }
  100% {
    -webkit-box-shadow: 0 0 0 20px rgba(0, 0, 0, 0);
            box-shadow: 0 0 0 20px rgba(0, 0, 0, 0);
  }
}
/*********************************
/*  Banner Section End
*********************************/
/*********************************
/*  Breadcrumb Section Start
*********************************/
.breadcrumb__wrapper {
  position: relative;
  padding: 50px 30px;
  background: url("../images/banner/banner-bg.png");
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  border-radius: var(--radius);
  border: 1px solid rgba(var(--color-primary-rgb), 0.1);
  margin-top: 90px;
}
@media screen and (max-width: 991px) {
  .breadcrumb__wrapper {
    margin-top: 70px;
  }
}
.breadcrumb__wrapper .breadcrumb__meta {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 26px;
}
.breadcrumb__wrapper .breadcrumb__header {
  max-width: 445px;
  margin: auto;
}
.breadcrumb__wrapper .breadcrumb__header .title {
  font-family: var(--font-default);
  font-size: 28px;
  line-height: 40px;
  font-weight: var(--font-semibold);
  color: var(--color-heading);
  margin-bottom: 10px;
  position: relative;
  text-transform: capitalize;
}
.breadcrumb__wrapper .breadcrumb__header p {
  font-family: var(--font-default);
  font-size: 16px;
  line-height: 24px;
  font-weight: var(--font-regular);
  margin: 0;
  color: var(--color-content);
  position: relative;
}
.breadcrumb__wrapper .breadcrumb__header p::after {
  content: "\ea64";
  position: absolute;
  top: 5px;
  left: -20px;
  width: 14px;
  height: 14px;
  font-size: 16px;
  color: var(--color-content);
  border-radius: 50%;
  font-family: "remixicon";
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.breadcrumb__wrapper .breadcrumb__header p a {
  color: var(--color-heading2);
  display: block;
  -webkit-transition: var(--transition);
  transition: var(--transition);
}
.breadcrumb__wrapper .breadcrumb__header p a:hover {
  color: var(--color-primary);
}
.breadcrumb__wrapper .breadcrumb__header p:first-of-type::after {
  display: none;
}

/*********************************
/*  Breadcrumb Section End
*********************************/
/*********************************
/* Clients Area Start
*********************************/
.clients__area {
  padding: 33px 0;
}
.clients__area .clients__content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 60px;
  overflow: hidden;
  position: relative;
  height: 100%;
}
.clients__area .clients__content .title {
  font-family: var(--font-default);
  font-weight: var(--font-semibold);
  font-size: 16px;
  line-height: 18px;
  margin: 0;
  min-width: 100px;
  color: var(--color-heading);
  background: var(--color-white);
  z-index: 9;
  height: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  white-space: nowrap;
}
.clients__area .clients__content .clients__wrapper {
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 40px;
  -webkit-animation: slide-har 35s linear infinite;
          animation: slide-har 35s linear infinite;
  -webkit-transition: -webkit-animation-duration 300ms;
  transition: -webkit-animation-duration 300ms;
  transition: animation-duration 300ms;
  transition: animation-duration 300ms, -webkit-animation-duration 300ms;
}
.clients__area .clients__content .clients__wrapper:hover {
  -webkit-animation-play-state: paused;
          animation-play-state: paused;
}
.clients__area .clients__content .clients__wrapper .clients__item {
  white-space: nowrap;
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.clients__area .clients__content .clients__wrapper .clients__item img {
  width: auto;
  height: 40px;
}

@-webkit-keyframes slide-har {
  0% {
    -webkit-transform: translateX(0%);
            transform: translateX(0%);
  }
  100% {
    -webkit-transform: translateX(-50%);
            transform: translateX(-50%);
  }
}

@keyframes slide-har {
  0% {
    -webkit-transform: translateX(0%);
            transform: translateX(0%);
  }
  100% {
    -webkit-transform: translateX(-50%);
            transform: translateX(-50%);
  }
}
/*********************************
/* Clients Area End
*********************************/
/*********************************
/*  Feature Section Start
*********************************/
.featureBox {
  background: var(--color-bg);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 15px;
  padding: 20px;
  height: calc(100% - 20px);
  margin-bottom: 20px;
  border-radius: var(--radius);
  -webkit-transition: var(--transition);
  transition: var(--transition);
  position: relative;
  z-index: 1;
}
.featureBox__icon {
  height: 50px;
  width: 50px;
  background: var(--color-white);
  border-radius: 50%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-transition: var(--transition);
  transition: var(--transition);
}
.featureBox__icon svg {
  width: 24px;
}
.featureBox__icon svg path {
  fill: var(--color-content);
  -webkit-transition: var(--transition);
  transition: var(--transition);
}
.featureBox__content {
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
}
.featureBox__content .title {
  font-family: var(--font-default);
  font-size: 20px;
  line-height: 20px;
  font-weight: var(--font-medium);
  color: var(--color-heading2);
  margin-bottom: 0;
  -webkit-transition: var(--transition);
  transition: var(--transition);
}
@media screen and (max-width: 767px) {
  .featureBox__content .title {
    font-size: 18px;
  }
}
.featureBox__content .desc {
  font-family: var(--font-default);
  font-weight: var(--font-regular);
  font-size: 16px;
  line-height: 24px;
  margin: 12px 0 0;
  color: var(--color-content);
  -webkit-transition: var(--transition);
  transition: var(--transition);
}
.featureBox.active, .featureBox:hover {
  background: var(--color-primary);
}
.featureBox.active .featureBox__icon, .featureBox:hover .featureBox__icon {
  background: rgba(var(--color-white-rgb), 0.2);
}
.featureBox.active .featureBox__icon svg path, .featureBox:hover .featureBox__icon svg path {
  fill: var(--color-white);
}
.featureBox.active .featureBox__content .title, .featureBox:hover .featureBox__content .title {
  color: var(--color-white);
}
.featureBox.active .featureBox__content .desc, .featureBox:hover .featureBox__content .desc {
  color: var(--color-white);
}

/*********************************
/*  Feature Section End
*********************************/
/*********************************
/*  Growth Section Start
*********************************/
.custom__accordion {
  margin-right: 40px;
}
@media screen and (max-width: 991px) {
  .custom__accordion {
    margin: 0;
  }
}
.custom__accordion.v2 {
  max-width: 1000px;
  margin: auto;
  background: rgba(var(--color-primary-rgb), 0.1);
  padding: 60px;
  border-radius: var(--radius);
}
@media screen and (max-width: 767px) {
  .custom__accordion.v2 {
    padding: 60px 30px;
  }
}
.custom__accordion.v2 .accordion-button:not(.collapsed) {
  color: var(--color-heading2);
  background: var(--color-white);
  -webkit-box-shadow: none;
          box-shadow: none;
}
.custom__accordion.v2 .accordion-button::before {
  background: var(--color-white);
}
.custom__accordion.v2 .accordion-body {
  background: var(--color-white);
}
.custom__accordion .accordion-item {
  background-color: transparent;
  border: none;
  margin-bottom: 10px;
}
.custom__accordion .accordion-item:last-of-type {
  margin-bottom: 0;
}
.custom__accordion .accordion-header {
  background-color: transparent;
}
.custom__accordion .accordion-button {
  font-family: var(--font-default);
  font-weight: var(--font-medium);
  font-size: 20px;
  line-height: 26px;
  color: var(--color-heading2);
  padding: 20px 16px 10px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 16px;
  border-radius: 6px 6px 0 0 !important;
  background-color: transparent;
}
@media screen and (max-width: 575px) {
  .custom__accordion .accordion-button {
    font-size: 18px;
  }
}
.custom__accordion .accordion-button:not(.collapsed) {
  color: var(--color-heading2);
  background: #f6fafa;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.custom__accordion .accordion-button:not(.collapsed)::before {
  content: "\f1af";
  background: var(--color-primary);
  color: var(--color-white);
  font-size: 18px;
}
.custom__accordion .accordion-button:focus {
  -webkit-box-shadow: none;
          box-shadow: none;
}
.custom__accordion .accordion-button::after {
  display: none;
}
.custom__accordion .accordion-button::before {
  content: "\f4b2";
  position: relative;
  left: 0;
  min-width: 30px;
  height: 30px;
  font-size: 16px;
  color: var(--color-content);
  background: rgba(var(--color-primary-rgb), 0.1);
  border-radius: 50%;
  font-family: "remixicon";
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.custom__accordion .accordion-body {
  padding: 0px 16px 20px 60px;
  border-bottom: 2px solid rgba(var(--color-primary-rgb), 0.5);
  background: #f6fafa;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.custom__accordion .accordion-body .desc {
  font-family: var(--font-default);
  font-weight: var(--font-regular);
  font-size: 16px;
  line-height: 24px;
  color: var(--color-content);
  margin: 0;
}

.growth__thumb {
  width: 100%;
  height: auto;
  display: block;
}
@media screen and (max-width: 991px) {
  .growth__thumb {
    max-width: 500px;
    margin: 0 auto 30px;
  }
}
.growth__thumb img {
  width: 100%;
  height: auto;
  display: block;
  border-radius: var(--radius);
}

/*********************************
/*  Growth Section End
*********************************/
/*********************************
/*  Integrate Section Start
*********************************/
.integrate__wrapper {
  position: relative;
  display: grid;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  grid-template-columns: repeat(2, 1fr);
  background: rgba(var(--color-primary-rgb), 0.1);
  padding: 60px;
  border-radius: var(--radius);
  gap: 30px;
  z-index: 1;
}
@media screen and (max-width: 991px) {
  .integrate__wrapper {
    grid-template-columns: auto;
    text-align: center;
    padding: 60px 30px;
  }
}
@media screen and (max-width: 400px) {
  .integrate__wrapper {
    padding: 60px 20px;
  }
}
.integrate__wrapper .integrate__content {
  max-width: 490px;
}
@media screen and (max-width: 991px) {
  .integrate__wrapper .integrate__content {
    margin: auto;
  }
}
.integrate__wrapper .integrate__content .title {
  font-family: var(--font-default);
  font-size: 28px;
  font-weight: var(--font-medium);
  line-height: 40px;
  color: var(--color-heading);
  margin-bottom: 14px;
}
.integrate__wrapper .integrate__content .desc {
  font-family: var(--font-default);
  font-weight: var(--font-regular);
  font-size: 16px;
  line-height: 24px;
  color: var(--color-heading2);
  margin: 0;
}
.integrate__wrapper .btn__group {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 12px;
  margin-top: 26px;
}
.integrate__wrapper .integrate__channel .channel__list {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}
@media screen and (max-width: 1199px) {
  .integrate__wrapper .integrate__channel .channel__list {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media screen and (max-width: 479px) {
  .integrate__wrapper .integrate__channel .channel__list {
    gap: 10px;
  }
}
.integrate__wrapper .integrate__channel .channel__list li {
  font-family: var(--font-default);
  font-weight: var(--font-medium);
  font-size: 20px;
  line-height: 18px;
  color: var(--color-heading2);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 15px;
  padding: 15px 16px;
  border-radius: var(--radius);
  background: var(--color-white);
}
@media screen and (max-width: 479px) {
  .integrate__wrapper .integrate__channel .channel__list li {
    font-size: 16px;
    gap: 10px;
    padding: 15px 10px;
  }
}
.integrate__wrapper .integrate__channel .channel__list li .icon {
  width: 30px;
  height: 30px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  border-radius: 50%;
  color: var(--color-white);
  font-weight: 400;
  font-size: 16px;
  line-height: 18px;
}
@media screen and (max-width: 479px) {
  .integrate__wrapper .integrate__channel .channel__list li .icon {
    width: 25px;
    height: 25px;
    font-size: 14px;
  }
}
.integrate__wrapper .integrate__channel .channel__list li .icon img {
  width: auto;
  height: auto;
  max-width: 30px;
  display: block;
  border-radius: 50%;
}
@media screen and (max-width: 479px) {
  .integrate__wrapper .integrate__channel .channel__list li .icon img {
    max-width: 25px;
  }
}

/*********************************
/*  Integrate Section End
*********************************/
/*********************************
/*  Pricing Section Start
*********************************/
.pricing__grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 27px;
}
@media screen and (max-width: 991px) {
  .pricing__grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media screen and (max-width: 575px) {
  .pricing__grid {
    grid-template-columns: repeat(1, 1fr);
  }
}

.pricing__item {
  padding: 40px 30px;
  background: #f6fafa;
  border-radius: var(--radius);
  -webkit-transition: var(--transition);
  transition: var(--transition);
}
@media screen and (max-width: 767px) {
  .pricing__item {
    padding: 30px 20px;
  }
}
.pricing__item .pricing__header {
  font-family: var(--font-default);
  margin-bottom: 20px;
}
.pricing__item .pricing__header .title {
  font-size: 20px;
  line-height: 18px;
  font-weight: var(--font-medium);
  margin-bottom: 16px;
  text-transform: capitalize;
  color: var(--color-heading2);
  -webkit-transition: var(--transition);
  transition: var(--transition);
}
@media screen and (max-width: 479px) {
  .pricing__item .pricing__header .title {
    font-size: 18px;
  }
}
.pricing__item .pricing__header p,
.pricing__item .pricing__header .desc {
  font-size: 16px;
  line-height: 24px;
  font-weight: var(--font-regular);
  margin-bottom: 0;
  -webkit-transition: var(--transition);
  transition: var(--transition);
}
.pricing__item .pricing__tag {
  padding-bottom: 24px;
  margin-bottom: 24px;
  border-bottom: 1px solid var(--color-border);
}
.pricing__item .pricing__tag span {
  font-family: var(--font-default);
  font-size: 28px;
  line-height: 40px;
  font-weight: var(--font-medium);
  color: var(--color-heading);
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: baseline;
      -ms-flex-align: baseline;
          align-items: baseline;
  position: relative;
  gap: 2px;
  -webkit-transition: var(--transition);
  transition: var(--transition);
}
.pricing__item .pricing__tag span sub {
  font-size: 16px;
  line-height: 18px;
  font-weight: var(--font-regular);
  color: var(--color-black);
  position: relative;
  bottom: 0px;
  -webkit-transition: var(--transition);
  transition: var(--transition);
}
.pricing__item .pricing__features li {
  font-family: var(--font-default);
  font-size: 16px;
  line-height: 24px;
  font-weight: var(--font-regular);
  color: var(--color-content);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 14px;
  margin-bottom: 12px;
  -webkit-transition: var(--transition);
  transition: var(--transition);
}
.pricing__item .pricing__features li:last-of-type {
  margin-bottom: 0;
}
.pricing__item .pricing__features li i {
  font-size: 22px;
  color: var(--color-primary);
  -webkit-transition: var(--transition);
  transition: var(--transition);
}
@media screen and (max-width: 767px) {
  .pricing__item .pricing__features li i {
    font-size: 16px;
  }
}
.pricing__item .pricing__btn {
  margin-top: 36px;
}
.pricing__item.active, .pricing__item:hover {
  background: var(--color-primary);
}
.pricing__item.active .pricing__header .title, .pricing__item:hover .pricing__header .title {
  color: var(--color-white);
}
.pricing__item.active .pricing__header p,
.pricing__item.active .pricing__header .desc, .pricing__item:hover .pricing__header p,
.pricing__item:hover .pricing__header .desc {
  color: var(--color-white);
}
.pricing__item.active .pricing__tag, .pricing__item:hover .pricing__tag {
  border-color: rgba(var(--color-white-rgb), 0.2);
}
.pricing__item.active .pricing__tag span, .pricing__item:hover .pricing__tag span {
  color: var(--color-white);
}
.pricing__item.active .pricing__tag span sub, .pricing__item:hover .pricing__tag span sub {
  color: var(--color-white);
}
.pricing__item.active .pricing__features li, .pricing__item:hover .pricing__features li {
  color: rgba(var(--color-white-rgb), 0.7);
}
.pricing__item.active .pricing__features li i, .pricing__item:hover .pricing__features li i {
  color: var(--color-white);
}
.pricing__item.active .pricing__btn .btn, .pricing__item:hover .pricing__btn .btn {
  background: var(--color-white);
  color: var(--color-primary);
}

/*********************************
/*  Pricing Section End
*********************************/
/*********************************
/* Tabs Start
*********************************/
.custom__tabs .nav {
  border: 1px solid var(--color-border);
  padding: 10px;
  border-radius: 30px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -ms-flex-wrap: nowrap;
      flex-wrap: nowrap;
  margin-bottom: 26px;
}
.custom__tabs .nav li .nav-link {
  font-family: var(--font-default);
  font-size: 16px;
  font-weight: var(--font-regular);
  line-height: 18px;
  padding: 11px 16px;
  background: transparent;
  color: var(--color-content);
  border: none;
  border-radius: 30px;
  text-transform: capitalize;
}
.custom__tabs .nav li .nav-link.active {
  background: var(--color-heading);
  color: var(--color-white);
}

/*********************************
/* Tabs End
*********************************/
/*********************************
/*  Testimonial Section Start
*********************************/
.testimonial__section {
  position: relative;
}

.testimonial__inner {
  position: relative;
  margin-left: auto;
}
.testimonial__inner .swiper-slide-active .testimonialCard {
  background: #f6fafa;
  border-bottom: 2px solid rgba(var(--color-primary-rgb), 0.5);
}

.testimonialCard {
  background: var(--color-bg);
  border-bottom: 2px solid var(--color-border);
  border-radius: 6px 6px 0 0;
  padding: 20px 16px;
  -webkit-transition: var(--transition);
  transition: var(--transition);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 16px;
}
.testimonialCard .avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: 2px solid var(--color-white);
}
.testimonialCard .avatar img {
  width: 100%;
  height: 100%;
  display: block;
  border-radius: 50%;
}
.testimonialCard__content {
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
}
.testimonialCard__content .header__flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 10px;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--color-border);
}
@media screen and (max-width: 375px) {
  .testimonialCard__content .header__flex {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
}
.testimonialCard__content .content .title {
  font-family: var(--font-default);
  font-size: 20px;
  font-weight: var(--font-medium);
  line-height: 20px;
  color: var(--color-heading2);
  margin-bottom: 10px;
}
.testimonialCard__content .content .designation {
  font-family: var(--font-default);
  font-size: 16px;
  font-weight: var(--font-regular);
  line-height: 24px;
  color: var(--color-content);
  margin-bottom: 0px;
}
.testimonialCard__content p {
  font-size: 16px;
  line-height: 24px;
  color: var(--color-content);
  font-weight: var(--font-regular);
  margin: 10px 0 0;
}
.testimonialCard__content .rating {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  gap: 1px;
}
.testimonialCard__content .rating a {
  height: 24px;
  width: 24px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.testimonialCard__content .rating a svg path {
  fill: var(--color-yellow);
}

.list__item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 20px;
}
.list__item li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 14px;
  list-style: none;
  font-family: var(--font-default);
  font-weight: var(--font-regular);
  font-size: 16px;
  line-height: 24px;
  color: var(--color-content);
}
.list__item li i {
  font-size: 20px;
  color: var(--color-heading);
}

/*********************************
/*  Testimonial Section End
*********************************/
/*********************************
/* Swiper Navigation & Pagination 
*********************************/
.swiper__navigation {
  position: relative;
  width: 100%;
  margin-top: 24px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 5px;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  z-index: 9;
}
.swiper__navigation .swiper-button-next,
.swiper__navigation .swiper-button-prev {
  font-size: 22px;
  height: 37px;
  width: 37px;
  color: var(--color-content);
  background: var(--color-bg);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  border-radius: 50%;
  -webkit-transition: var(--transition);
  transition: var(--transition);
  margin: 0;
  position: relative;
  top: 0;
  left: 0;
}
.swiper__navigation .swiper-button-next.swiper-button-disabled,
.swiper__navigation .swiper-button-prev.swiper-button-disabled {
  color: var(--color-border);
  opacity: 1;
}
.swiper__navigation .swiper-button-next::after,
.swiper__navigation .swiper-button-prev::after {
  display: none;
}
.swiper__navigation .swiper-button-next:hover,
.swiper__navigation .swiper-button-prev:hover {
  background: var(--color-primary);
  color: var(--color-white);
}

.swiper__pagination .swiper-pagination-bullets {
  bottom: 0px;
  z-index: 99;
}
.swiper__pagination .swiper-pagination-bullets .swiper-pagination-bullet {
  width: 14px;
  height: 6px;
  border-radius: 30px;
  background: #a9a9a9;
  -webkit-transition: var(--transition);
  transition: var(--transition);
  opacity: 1;
}
.swiper__pagination .swiper-pagination-bullets .swiper-pagination-bullet-active {
  width: 20px;
  background: var(--color-yellow);
}

/*********************************
/*  CtaBox Section Start
*********************************/
.ctaBox__wrapper {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  background: var(--color-primary);
  padding: 39px 48px;
  border-radius: var(--radius);
  gap: 30px;
  z-index: 1;
}
@media screen and (max-width: 991px) {
  .ctaBox__wrapper {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    text-align: center;
  }
}
@media screen and (max-width: 400px) {
  .ctaBox__wrapper {
    padding: 60px 20px;
  }
}
.ctaBox__wrapper .ctaBox__content {
  max-width: 528px;
}
.ctaBox__wrapper .ctaBox__content .title {
  font-family: var(--font-default);
  font-size: 36px;
  font-weight: var(--font-semibold);
  line-height: 50px;
  color: var(--color-white);
  margin-bottom: 14px;
}
@media screen and (max-width: 767px) {
  .ctaBox__wrapper .ctaBox__content .title {
    font-size: 28px;
    line-height: 38px;
  }
}
.ctaBox__wrapper .ctaBox__content .desc {
  font-family: var(--font-default);
  font-weight: var(--font-regular);
  font-size: 16px;
  line-height: 24px;
  color: rgba(var(--color-white-rgb), 0.7);
  margin: 0;
}
.ctaBox__wrapper .btn__group {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 12px;
}
.ctaBox__wrapper .btn__group .sg-btn {
  padding: 14px 36px;
  white-space: nowrap;
}
.ctaBox__wrapper .btn__group .sg-btn-dark:hover {
  background: var(--color-white);
  border-color: var(--color-white);
  color: var(--color-heading);
}

/*********************************
/*  CtaBox Section End
*********************************/
/*********************************
/*  Blog Section Start
*********************************/
.blogPost {
  background: var(--color-bg);
  border-bottom: 2px solid var(--color-border);
  border-radius: 10px 10px 0 0;
  -webkit-transition: var(--transition);
  transition: var(--transition);
  margin-bottom: 20px;
}
.blogPost:hover {
  background: #f6fafa;
  border-bottom: 2px solid rgba(var(--color-primary-rgb), 0.5);
}
.blogPost:hover .thumb img {
  -webkit-transform: scale(1.1);
          transform: scale(1.1);
}
.blogPost:hover .solid__btn {
  color: var(--color-primary) !important;
}
.blogPost .thumb {
  border-radius: 10px 10px 0 0;
  overflow: hidden;
  position: relative;
}
.blogPost .thumb img {
  width: 100%;
  height: auto;
  display: block;
  aspect-ratio: 3/1.5;
  -webkit-transition: var(--transition);
  transition: var(--transition);
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: top;
     object-position: top;
}
.blogPost .thumb .meta {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  gap: 30px;
  position: absolute;
  bottom: 0;
  left: 0;
  padding: 13px 15px;
  width: 100%;
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
}
.blogPost .thumb .meta span {
  font-size: 16px;
  line-height: 18px;
  font-weight: var(--font-medium);
  color: var(--color-white);
  text-transform: capitalize;
  position: relative;
}
.blogPost .thumb .meta span a {
  display: block;
  color: currentColor;
  -webkit-transition: var(--transition);
  transition: var(--transition);
}
.blogPost .thumb .meta span a:hover {
  color: var(--color-heading);
}
.blogPost .content {
  padding: 20px;
}
.blogPost .content .title {
  font-family: var(--font-default);
  font-size: 20px;
  line-height: 26px;
  font-weight: var(--font-medium);
  color: var(--color-heading);
  margin-bottom: 16px;
}
.blogPost .content .title a {
  color: currentColor;
  display: block;
  -webkit-transition: var(--transition);
  transition: var(--transition);
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.blogPost .content .title a:hover {
  color: var(--color-primary);
}
.blogPost .content p,
.blogPost .content .desc {
  font-size: 16px;
  line-height: 24px;
  font-weight: var(--font-regular);
  color: var(--color-content);
  margin-bottom: 0;
}
.blogPost .blog__footer {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  gap: 15px;
  margin-top: 20px;
}
.blogPost .blog__footer span {
  font-size: 16px;
  line-height: 24px;
  font-weight: var(--font-regular);
  color: var(--color-heading);
  display: block;
}
.blogPost .blog__footer .solid__btn {
  color: var(--color-heading);
}
.blogPost.v2 {
  background: transparent;
  border: none;
}
.blogPost.v2 .thumb {
  border-radius: 10px;
}
.blogPost.v2 .content {
  padding: 0;
  padding-top: 20px;
}
.blogPost.v2 .content p {
  margin-bottom: 30px;
}
.blogPost.v2 .content p:last-of-type {
  margin-bottom: 0;
}
.blogPost.v2 .content h4 {
  font-size: 16px;
  line-height: 24px;
  font-weight: var(--font-regular);
  color: var(--color-black);
  margin: 24px 0 14px;
  display: block;
}
.blogPost.v2 .thumb__wrapper .thumbs {
  border-radius: 6px;
  overflow: hidden;
  position: relative;
}
@media screen and (max-width: 767px) {
  .blogPost.v2 .thumb__wrapper .thumbs {
    margin-bottom: 20px;
  }
}
.blogPost.v2 .thumb__wrapper .thumbs img {
  width: 100%;
  height: auto;
  display: block;
  aspect-ratio: 4/3;
  -webkit-transition: var(--transition);
  transition: var(--transition);
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: top;
     object-position: top;
}

.pagination {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 5px;
}
.pagination a {
  height: 37px;
  width: 37px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  background: #fafafa;
  color: var(--color-content);
  font-size: 18px;
  border-radius: 50%;
  -webkit-transition: var(--transition);
  transition: var(--transition);
}
.pagination a.active, .pagination a:hover {
  background: var(--color-primary);
  color: var(--color-white);
}

@media screen and (max-width: 991px) {
  .blog__sidebar {
    margin-top: 40px;
  }
}

.aside {
  margin-top: 24px;
}
.aside .widget__title {
  font-family: var(--font-default);
  font-weight: var(--font-medium);
  font-size: 16px;
  line-height: 18px;
  color: var(--color-heading2);
  margin-bottom: 16px;
}
.aside .widget__list li {
  margin-bottom: 10px;
  list-style: none;
}
.aside .widget__list li:last-of-type {
  margin-bottom: 0;
}
.aside .widget__list li .widget__item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 15px;
  background: #fafafa;
  border-radius: var(--radius);
  padding: 10px;
}
.aside .widget__list li .widget__item .thumb {
  width: 60px;
  height: 60px;
  border-radius: 6px;
}
.aside .widget__list li .widget__item .thumb img {
  width: 100%;
  height: 100%;
  display: block;
  -o-object-fit: cover;
     object-fit: cover;
  border-radius: 6px;
}
.aside .widget__list li .widget__item .content {
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
}
.aside .widget__list li .widget__item .content h5 {
  font-family: var(--font-default);
  font-weight: var(--font-medium);
  color: var(--color-heading2);
  font-size: 16px;
  line-height: 18px;
  -webkit-transition: var(--transition);
  transition: var(--transition);
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}
.aside .widget__list li .widget__item:hover .content h5 {
  color: var(--color-primary);
}
.aside .social__icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 15px;
  background: #fafafa;
  border-radius: var(--radius);
  padding: 20px 10px;
}
.aside .social__icon a {
  font-size: 20px;
  height: 40px;
  width: 40px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  border-radius: 50%;
  background: var(--color-white);
  color: var(--color-heading2);
  -webkit-transition: var(--transition);
  transition: var(--transition);
}
.aside .social__icon a:hover {
  background: var(--color-heading);
  color: var(--color-white);
}

/*********************************
/*  Blog Section End
*********************************/
/*********************************
/*  Filter Area Start
*********************************/
.filter__area {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  gap: 20px;
  margin-bottom: 20px;
}

.search__form {
  max-width: 180px;
}
.search__form .form-group {
  position: relative;
}
.search__form .form-group .form-control {
  font-family: var(--font-default);
  font-weight: var(--font-regular);
  font-size: 14px;
  line-height: 24px;
  color: var(--color-content);
  background: var(--color-bg);
  border: 1px solid rgba(var(--color-primary-rgb), 0.1);
  border-radius: 30px;
  padding: 9px 16px;
  padding-right: 36px;
}
.search__form .form-group .form-control:focus {
  -webkit-box-shadow: none;
          box-shadow: none;
}
.search__form .form-group .form-control::-webkit-search-cancel-button {
  display: none;
}
.search__form .form-group .submit {
  background: transparent;
  border: none;
  outline: none;
  color: var(--color-content);
  font-size: 18px;
  position: absolute;
  right: 16px;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}

.search__filter {
  width: 100px;
}
.search__filter .form-select {
  font-family: var(--font-default);
  font-weight: var(--font-regular);
  font-size: 14px;
  line-height: 24px;
  color: var(--color-content);
  padding: 10px 16px;
  border-radius: 30px;
  background-color: var(--color-bg);
  border: none;
  padding-right: 30px;
  background-position: right 10px center;
}
.search__filter .form-select:focus {
  -webkit-box-shadow: none;
          box-shadow: none;
}

/*********************************
/*  Filter Area End
*********************************/
/*********************************
/* Privacy Section Start
*********************************/
.privacy__content .main__title {
  font-size: 24px;
  line-height: 28px;
  font-weight: var(--font-medium);
  color: var(--color-heading);
  text-transform: capitalize;
  padding: 16px 0;
  border-bottom: 1px solid var(--color-border);
  margin-bottom: 0;
  position: relative;
  padding-left: 20px;
}
.privacy__content .main__title::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  height: 4px;
  width: 4px;
  border-radius: 50%;
  background: var(--color-heading);
}
.privacy__content-title {
  font-size: 24px;
  line-height: 28px;
  font-weight: var(--font-medium);
  color: var(--color-heading);
  text-transform: capitalize;
  padding: 0;
  margin-bottom: 0;
}
.privacy__content-subtitle {
  font-size: 20px;
  line-height: 24px;
  font-weight: var(--font-semibold);
  color: var(--color-heading);
  text-transform: capitalize;
  margin-top: 30px;
  margin-bottom: 0;
  position: relative;
}
.privacy__content p {
  font-size: 16px;
  line-height: 24px;
  margin-bottom: 0px;
  margin: 24px 0;
}
.privacy__content p span {
  color: var(--color-heading);
  font-weight: var(--font-medium);
}
.privacy__content p a {
  color: var(--color-primary);
  display: inline-block;
}
.privacy__content p:last-of-type {
  margin-bottom: 0;
}
.privacy__content-list {
  margin: 24px 0;
  padding-left: 15px;
}
.privacy__content-list li {
  list-style: none;
  font-size: 16px;
  line-height: 24px;
  color: var(--color-content);
  margin-bottom: 12px;
  position: relative;
  padding-left: 20px;
}
.privacy__content-list li::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  height: 4px;
  width: 4px;
  border-radius: 50%;
  background: var(--color-content);
}
.privacy__content-list li:last-of-type {
  margin-bottom: 0;
}

/*********************************
/* Privacy Section End
*********************************/
/*********************************
/*  Login Control Start
*********************************/
.login__form {
  padding: 50px 30px 36px 50px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 30px;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  height: 100%;
}
@media screen and (max-width: 1199px) {
  .login__form {
    padding: 30px;
  }
}
@media screen and (max-width: 991px) {
  .login__form {
    height: 100%;
  }
}
@media screen and (max-width: 575px) {
  .login__form {
    padding: 30px 15px;
  }
}
.login__form .login__logo img {
  width: auto;
  height: 40px;
  display: block;
}
.login__form .footer__copyright p {
  font-weight: var(--font-regular);
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0.15px;
  color: var(--color-content);
  margin: 0;
}
.login__form .footer__copyright p a {
  font-weight: var(--font-semibold);
  text-decoration: underline;
  text-underline-offset: 2px;
  color: var(--color-primary);
}
.login__form .footer__copyright p span {
  font-weight: var(--font-semibold);
  color: var(--color-heading2);
}

.login__wrapper {
  background: #fbfbfb;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  height: calc(100% - 40px);
  top: 20px;
  border-radius: var(--radius);
  margin-right: 10px;
}
@media screen and (max-width: 991px) {
  .login__wrapper {
    height: 100%;
    padding: 30px;
    margin: 0;
  }
}
.login__wrapper .swiper-slide {
  background: #fbfbfb;
  height: 100%;
  border-radius: var(--radius);
}

.login__item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  height: 100%;
}
.login__item .avatar {
  max-width: 600px;
  margin: 0 auto;
  aspect-ratio: 4/3.4;
}

.with-country-code {
  font-size: 12px;
  color: gray;
  margin-left: 5px;
}

@media screen and (max-width: 1440px) {
  .login__item .avatar {
    max-width: 400px;
  }
}
.login__item .avatar img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  object-position: center;
  display: block;
}
.login__item .login__content {
  max-width: 504px;
  margin: 32px auto 0;
  padding: 0 20px;
  min-height: 120px;
}
.login__item .login__content .title {
  font-family: var(--font-default);
  font-weight: var(--font-medium);
  font-size: 30px;
  line-height: 40px;
  color: var(--color-heading2);
  margin: 0;
}
.login__item .login__content .title span {
  color: var(--color-green);
}

/*********************************
/*  Login Control End
*********************************/
/*********************************
/*  Login area Start
*********************************/
.form__wrapper {
  position: relative;
  background: var(--color-white);
  padding: 30px;
  border-radius: var(--radius);
  border: 1px solid var(--color-border);
  max-width: 480px;
  width: 100%;
  margin: auto;
}
@media screen and (max-width: 767px) {
  .form__wrapper {
    max-width: 100%;
  }
}
@media screen and (max-width: 575px) {
  .form__wrapper {
    padding: 30px 15px;
  }
}

.heading__content {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--color-border);
}
.heading__content .title {
  font-family: var(--font-default);
  font-weight: var(--font-semibold);
  font-size: 28px;
  line-height: 40px;
  color: var(--color-heading);
  margin-bottom: 10px;
}
@media screen and (max-width: 479px) {
  .heading__content .title {
    font-size: 24px;
  }
}
.heading__content p,
.heading__content .desc {
  font-family: var(--font-roboto);
  font-size: 16px;
  font-weight: var(--font-regular);
  line-height: 24px;
  color: var(--color-content);
  margin-bottom: 0;
}
.heading__content p a,
.heading__content .desc a {
  color: var(--color-heading2);
  display: inline-block;
  -webkit-transition: var(--transition);
  transition: var(--transition);
}
.heading__content p a:hover,
.heading__content .desc a:hover {
  color: var(--color-primary);
}

.flex__input {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  gap: 16px;
}
@media screen and (max-width: 479px) {
  .flex__input {
    gap: 0;
    display: block;
  }
}

.form-group {
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  margin-bottom: 16px;
}
.form-group label {
  font-family: var(--font-roboto);
  font-size: 16px;
  line-height: 22px;
  font-weight: var(--font-regular);
  color: var(--color-heading2);
  text-transform: capitalize;
  margin-bottom: 12px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.form-group label .required {
  font-size: 16px;
  color: #fa113d;
}
.form-group .form-control {
  font-family: var(--font-roboto);
  font-size: 14px;
  line-height: 24px;
  font-weight: var(--font-regular);
  color: var(--color-content);
  padding: 10px 20px;
  border: 1px solid rgba(var(--color-primary-rgb), 0.1);
  background: var(--color-bg);
  border-radius: 30px;
}
.form-group .form-control::-webkit-input-placeholder {
  color: var(--color-content);
  text-transform: capitalize;
}
.form-group .form-control::-moz-placeholder {
  color: var(--color-content);
  text-transform: capitalize;
}
.form-group .form-control:-ms-input-placeholder {
  color: var(--color-content);
  text-transform: capitalize;
}
.form-group .form-control::-ms-input-placeholder {
  color: var(--color-content);
  text-transform: capitalize;
}
.form-group .form-control::placeholder {
  color: var(--color-content);
  text-transform: capitalize;
}
.form-group .form-control:focus {
  -webkit-box-shadow: none;
          box-shadow: none;
  background: var(--color-white);
  border-color: rgba(var(--color-primary-rgb), 0.5);
}
.form-group .toggle__password {
  position: absolute;
  right: 15px;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  font-size: 16px;
  color: var(--color-heading);
  cursor: pointer;
}

.btn__group .sg-btn {
  line-height: 24.4px;
}

.devider {
  font-family: var(--font-default);
  font-size: 16px;
  font-weight: var(--font-medium);
  line-height: 24px;
  color: var(--color-heading2);
  position: relative;
  margin: 20px 0 16px;
}
.devider span {
  background: var(--color-white);
  padding: 0 20px;
  z-index: 1;
  position: relative;
}
.devider::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  height: 1px;
  background: var(--color-border);
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}

.instant__login {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 12px;
  background: #f6fafa;
  padding: 30px 20px;
  border-radius: 6px;
}
.instant__login .sg-btn {
  font-size: 14px;
  line-height: 22px;
  font-weight: var(--font-medium);
  width: 100%;
}

.login__action {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 10px;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
@media screen and (max-width: 479px) {
  .login__action {
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
  }
}
.login__action a {
  font-family: var(--font-default);
  font-weight: var(--font-medium);
  font-size: 16px;
  line-height: 24px;
  background: #f6fafa;
  color: var(--color-content);
  padding: 7px 10px;
  border-radius: 30px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  gap: 10px;
  width: 100%;
  border: 1px solid #f6fafa;
  -webkit-transition: var(--transition);
  transition: var(--transition);
}
.login__action a i {
  font-size: 22px;
  color: var(--color-heading2);
  -webkit-transition: var(--transition);
  transition: var(--transition);
}
.login__action a:hover {
  color: var(--color-primary);
  background: var(--color-white);
  border-color: rgba(var(--color-primary-rgb), 0.5);
}
.login__action a:hover i {
  color: var(--color-primary);
}
@media screen and (max-width: 479px) {
  .login__action a {
    width: calc(50% - 5px);
  }
  .login__action a:last-of-type {
    width: 100%;
  }
}

.custom__checkbox {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 12px;
}
.custom__checkbox input {
  display: none;
}
.custom__checkbox input:checked + label::after {
  content: "";
  display: block;
  position: absolute;
  top: 50%;
  left: 0;
  width: 5px;
  height: 10px;
  border: solid var(--color-white);
  border-width: 0 2px 2px 0;
  -webkit-transform: rotate(45deg) translateY(-86%);
          transform: rotate(45deg) translateY(-86%);
}
.custom__checkbox input:checked + label::before {
  border-color: var(--color-primary);
  background: var(--color-primary);
}
.custom__checkbox label {
  font-family: var(--font-default);
  font-size: 16px;
  font-weight: var(--font-regular);
  line-height: 16px;
  color: var(--color-content);
  cursor: pointer;
  position: relative;
  margin-bottom: 0;
  padding-left: 26px;
}
.custom__checkbox label:before {
  content: "";
  height: 16px;
  width: 16px;
  border: 1px solid #eeeeee;
  border-radius: 2px;
  display: inline-block;
  position: absolute;
  left: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  -webkit-appearance: none;
}

.forget {
  font-family: var(--font-default);
  font-size: 16px;
  font-weight: var(--font-regular);
  line-height: 16px;
  color: var(--color-heading);
  text-decoration: none;
  display: inline-block;
  -webkit-transition: var(--transition);
  transition: var(--transition);
}
.forget:hover {
  color: var(--color-primary);
}

.badges {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 12px;
  font-family: var(--font-default);
  font-weight: var(--font-regular);
  font-size: 16px;
  line-height: 24px;
  color: #00ac11;
  padding: 8px;
  border-radius: 8px;
  background: rgba(0, 172, 17, 0.1);
  margin-bottom: 20px;
}
.badges .icon {
  height: 20px;
  width: 20px;
  min-width: 20px;
  border-radius: 4px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  background: var(--color-white);
}
.badges.failed {
  color: #fa113d;
  background: rgba(250, 17, 61, 0.1);
}

.back__text {
  font-family: var(--font-default);
  font-weight: var(--font-regular);
  font-size: 16px;
  line-height: 24px;
  color: var(--color-content);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 6px;
  margin-top: 24px;
}
.back__text i {
  width: 16px;
  height: 18px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.back__text a {
  color: var(--color-heading2);
  display: inline-block;
  -webkit-transition: var(--transition);
  transition: var(--transition);
}
.back__text a:hover {
  color: var(--color-primary);
}

/*********************************
/*  Footer Section Start
*********************************/
.footer__section {
  padding-top: 60px;
  background: var(--color-heading);
  position: relative;
}

.footer__top {
  max-width: 526px;
}
@media screen and (max-width: 991px) {
  .footer__top {
    margin-bottom: 30px;
  }
}
.footer__top .footer__logo {
  margin-bottom: 31px;
}
.footer__top .footer__logo img {
  height: 40px;
  width: auto;
}
.footer__top .footer__slogun {
  margin-bottom: 36px;
}
.footer__top .footer__slogun:last-of-type {
  margin-bottom: 0;
}
.footer__top .footer__slogun .title {
  font-family: var(--font-default);
  font-size: 28px;
  line-height: 40px;
  font-weight: var(--font-medium);
  color: var(--color-white);
  margin-bottom: 14px;
}
@media screen and (max-width: 479px) {
  .footer__top .footer__slogun .title {
    font-size: 22px;
    line-height: 30px;
  }
}
.footer__top .footer__slogun p {
  font-family: var(--font-default);
  font-size: 16px;
  font-weight: var(--font-regular);
  line-height: 24px;
  color: rgba(var(--color-white-rgb), 0.7);
  margin: 0;
}
.footer__top .footer__slogun .payment__icon {
  max-width: 510px;
  margin-top: 36px;
}

.footer__wrapper .footer__widget .widget__flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
}
.footer__wrapper .footer__widget .widget__flex .widget__list {
  min-width: 130px;
}
.footer__wrapper .footer__widget .widget__list li {
  font-family: var(--font-default);
  font-size: 16px;
  font-weight: var(--font-medium);
  line-height: 18px;
  list-style: none;
  color: var(--color-white);
  text-transform: capitalize;
  position: relative;
  margin-bottom: 32px;
}
.footer__wrapper .footer__widget .widget__list li:last-of-type {
  margin-bottom: 0;
}
.footer__wrapper .footer__widget .widget__list li a {
  position: relative;
  color: currentColor;
  -webkit-transition: var(--transition);
  transition: var(--transition);
}
.footer__wrapper .footer__widget .widget__list li a:hover {
  color: var(--color-primary);
}
.footer__wrapper .footer__widget .widget__list li a:hover::before {
  background: var(--color-primary);
}
.footer__wrapper .footer__widget .widget__list li a:hover::after {
  width: 100%;
  right: auto;
  left: 0;
  background: var(--color-primary);
}
.footer__wrapper .footer__widget .widget__list li:last-of-type {
  margin-bottom: 0;
}

.footer__bottom {
  margin-top: 24px;
}
.footer__bottom .footer__copyright {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  gap: 20px;
  padding: 24px 0;
  border-top: 1px solid rgba(var(--color-white-rgb), 0.1);
}
@media screen and (max-width: 991px) {
  .footer__bottom .footer__copyright {
    -webkit-box-orient: vertical;
    -webkit-box-direction: reverse;
        -ms-flex-direction: column-reverse;
            flex-direction: column-reverse;
  }
}
.footer__bottom .footer__copyright .copyright {
  font-family: var(--font-default);
  font-size: 14px;
  line-height: 20px;
  font-weight: var(--font-regular);
  letter-spacing: 0.15px;
  color: rgba(var(--color-white-rgb), 0.7);
  margin-bottom: 0;
}
.footer__bottom .footer__copyright .copyright a {
  color: var(--color-white);
  display: inline-block;
  text-decoration: none;
}

.footer__toplink {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 16px;
}
.footer__toplink .footer__link {
  position: relative;
  gap: 12px;
}
@media (max-width: 374px) {
  .footer__toplink .footer__link {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
}
.footer__toplink .footer__link .icon {
  height: 40px;
  width: 40px;
  background: rgba(var(--color-white-rgb), 0.1);
  border: 1px solid rgba(var(--color-primary-rgb), 0.16);
  border-radius: 50%;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.footer__toplink .footer__link .icon img {
  width: 17px;
}
.footer__toplink .footer__link a {
  font-family: var(--font-default);
  font-size: 16px;
  line-height: 24px;
  font-weight: var(--font-regular);
  color: var(--color-white);
  -webkit-transition: var(--transition);
  transition: var(--transition);
}
.footer__toplink .footer__link a:hover {
  color: var(--color-primary);
}

/*********************************
/*  Footer Section End
*********************************/