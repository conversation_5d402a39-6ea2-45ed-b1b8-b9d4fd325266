@charset "UTF-8";
@import url("https://fonts.googleapis.com/css2?family=Outfit:wght@400;500&display=swap");
/*!
 * Bootstrap  v5.2.3 (https://getbootstrap.com/)
 * Copyright 2011-2022 The Bootstrap Authors
 * Copyright 2011-2022 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
 */
:root {
  --bs-light: #eeeeee;
  --bs-dark: #111120;
  --bs-primary: #2FABF7;
  --bs-secondary: #434343;
  --bs-link-hover-color: #000000;
  --bs-btn-hover-color: #2FABF7;
  --bs-btn-hover-border-color: #2FABF7;
  --bs-table-border-color: #d7dde9;
  --bs-body-bg-color: #f7fcff;
  --bs-table-header-bg-color: #f1f3fb;
  --bs-sidebar-menu-active: #2FABF7;
  --bs-gray-100: #f8f9fa;
  --bs-gray-200: #e9ecef;
  --bs-gray-300: #dee2e6;
  --bs-gray-400: #ced4da;
  --bs-gray-500: #adb5bd;
  --bs-gray-600: #6c757d;
  --bs-gray-700: #495057;
  --bs-gray-800: #343a40;
  --bs-gray-900: #212529;
  --bs-primary: #2fabf7;
  --bs-secondary: #434343;
  --bs-success: #41cc68;
  --bs-info: #02a2b9;
  --bs-warning: #ffbb33;
  --bs-danger: #ff2424;
  --bs-light: #ffffff;
  --bs-dark: #111120;
  --bs-gray-ligth: #d7dde9;
  --bs-primary-rgb: 63, 82, 227;
  --bs-secondary-rgb: 67, 67, 67;
  --bs-success-rgb: 65, 204, 104;
  --bs-info-rgb: 2, 162, 185;
  --bs-warning-rgb: 255, 187, 51;
  --bs-danger-rgb: 255, 36, 36;
  --bs-light-rgb: 255, 255, 255;
  --bs-dark-rgb: 17, 17, 32;
  --bs-gray-ligth-rgb: 215, 221, 233;
  --bs-white-rgb: 255, 255, 255;
  --bs-black-rgb: 0, 0, 0;
  --bs-body-color-rgb: 33, 37, 41;
  --bs-body-bg-rgb: 255, 255, 255;
  --bs-font-sans-serif: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", "Noto Sans", "Liberation Sans", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --bs-font-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  --bs-gradient: linear-gradient(180deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));
  --bs-body-font-family: var(--bs-font-sans-serif);
  --bs-body-font-size: 1rem;
  --bs-body-font-weight: 400;
  --bs-body-line-height: 1.5;
  --bs-body-color: #212529;
  --bs-body-bg: #ffffff;
  --bs-border-width: 1px;
  --bs-border-style: solid;
  --bs-border-color: #d7dde9;
  --bs-border-color-translucent: rgba(0, 0, 0, 0.175);
  --bs-border-radius: 0.375rem;
  --bs-border-radius-sm: 0.25rem;
  --bs-border-radius-lg: 0.5rem;
  --bs-border-radius-xl: 1rem;
  --bs-border-radius-2xl: 2rem;
  --bs-border-radius-pill: 50rem;
  --bs-link-color: #2fabf7;
  --bs-link-hover-color: #3242b6;
  --bs-code-color: #d63384;
  --bs-highlight-bg: #fff3cd;
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

@media (prefers-reduced-motion: no-preference) {
  :root {
    scroll-behavior: smooth;
  }
}

body {
  margin: 0;
  font-family: var(--bs-body-font-family);
  font-size: var(--bs-body-font-size);
  font-weight: var(--bs-body-font-weight);
  line-height: var(--bs-body-line-height);
  color: var(--bs-body-color);
  text-align: var(--bs-body-text-align);
  background-color: var(--bs-body-bg);
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

hr {
  margin: 1rem 0;
  color: inherit;
  border: 0;
  border-top: 1px solid;
  opacity: 0.25;
}

h6, .h6, h5, .h5, h4, .h4, h3, .h3, h2, .h2, h1, .h1 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-weight: 500;
  line-height: 1.2;
}

h1, .h1 {
  font-size: calc(1.375rem + 1.5vw);
}
@media (min-width: 1200px) {
  h1, .h1 {
    font-size: 2.5rem;
  }
}

h2, .h2 {
  font-size: calc(1.325rem + 0.9vw);
}
@media (min-width: 1200px) {
  h2, .h2 {
    font-size: 2rem;
  }
}

h3, .h3 {
  font-size: calc(1.3rem + 0.6vw);
}
@media (min-width: 1200px) {
  h3, .h3 {
    font-size: 1.75rem;
  }
}


h4, .h4 {
  font-size: calc(1.275rem + 0.3vw);
}
@media (min-width: 1200px) {
  h4, .h4 {
    font-size: 1.5rem;
  }
}

h5, .h5 {
  font-size: 1.25rem;
}

h6, .h6 {
  font-size: 1rem;
}

p {
  margin-top: 0;
  margin-bottom: 1rem;
}

abbr[title] {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
  cursor: help;
  -webkit-text-decoration-skip-ink: none;
          text-decoration-skip-ink: none;
}

address {
  margin-bottom: 1rem;
  font-style: normal;
  line-height: inherit;
}

ol,
ul {
  padding-left: 2rem;
}

ol,
ul,
dl {
  margin-top: 0;
  margin-bottom: 1rem;
}

ol ol,
ul ul,
ol ul,
ul ol {
  margin-bottom: 0;
}

dt {
  font-weight: 700;
}

dd {
  margin-bottom: 0.5rem;
  margin-left: 0;
}

/* Positioning the right-submenu */
.right-submenu {
  position: absolute;
  top: 0;
  left: 100%; /* Position to the right of the parent */
  min-width: 10rem; /* Adjust as needed */
  z-index: 1000; /* Ensure it appears on top of other elements */
}

/* Hide the right-submenu by default */
.right-submenu {
  display: none;
}

/* Show the right-submenu when its parent is hovered */
.dropdown-submenu:hover .right-submenu {
  display: block;
}


blockquote {
  margin: 0 0 1rem;
}

b,
strong {
  font-weight: bolder;
}

small, .small {
  font-size: 0.875em;
}

mark, .mark {
  padding: 0.1875em;
  background-color: var(--bs-highlight-bg);
}

sub,
sup {
  position: relative;
  font-size: 0.75em;
  line-height: 0;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

a {
  color: var(--bs-link-color);
  text-decoration: underline;
}
a:hover {
  color: var(--bs-link-hover-color);
}

a:not([href]):not([class]), a:not([href]):not([class]):hover {
  color: inherit;
  text-decoration: none;
}

pre,
code,
kbd,
samp {
  font-family: var(--bs-font-monospace);
  font-size: 1em;
}

pre {
  display: block;
  margin-top: 0;
  margin-bottom: 1rem;
  overflow: auto;
  font-size: 0.875em;
}
pre code {
  font-size: inherit;
  color: inherit;
  word-break: normal;
}

code {
  font-size: 0.875em;
  color: var(--bs-code-color);
  word-wrap: break-word;
}
a > code {
  color: inherit;
}

kbd {
  padding: 0.1875rem 0.375rem;
  font-size: 0.875em;
  color: var(--bs-body-bg);
  background-color: var(--bs-body-color);
  border-radius: 0.25rem;
}
kbd kbd {
  padding: 0;
  font-size: 1em;
}

figure {
  margin: 0 0 1rem;
}

img,
svg {
  vertical-align: middle;
}

table {
  caption-side: bottom;
  border-collapse: collapse;
}

caption {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  color: #6c757d;
  text-align: left;
}

th {
  text-align: inherit;
  text-align: -webkit-match-parent;
}

thead,
tbody,
tfoot,
tr,
td,
th {
  border-color: inherit;
  border-style: solid;
  border-width: 0;
}

label {
  display: inline-block;
}

button {
  border-radius: 0;
}

button:focus:not(:focus-visible) {
  outline: 0;
}

input,
button,
select,
optgroup,
textarea {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

button,
select {
  text-transform: none;
}

[role=button] {
  cursor: pointer;
}

select {
  word-wrap: normal;
}
select:disabled {
  opacity: 1;
}

[list]:not([type=date]):not([type=datetime-local]):not([type=month]):not([type=week]):not([type=time])::-webkit-calendar-picker-indicator {
  display: none !important;
}

button,
[type=button],
[type=reset],
[type=submit] {
  -webkit-appearance: button;
}
button:not(:disabled),
[type=button]:not(:disabled),
[type=reset]:not(:disabled),
[type=submit]:not(:disabled) {
  cursor: pointer;
}

::-moz-focus-inner {
  padding: 0;
  border-style: none;
}

textarea {
  resize: vertical;
}

fieldset {
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0;
}

legend {
  float: left;
  width: 100%;
  padding: 0;
  margin-bottom: 0.5rem;
  font-size: calc(1.275rem + 0.3vw);
  line-height: inherit;
}
@media (min-width: 1200px) {
  legend {
    font-size: 1.5rem;
  }
}
legend + * {
  clear: left;
}

::-webkit-datetime-edit-fields-wrapper,
::-webkit-datetime-edit-text,
::-webkit-datetime-edit-minute,
::-webkit-datetime-edit-hour-field,
::-webkit-datetime-edit-day-field,
::-webkit-datetime-edit-month-field,
::-webkit-datetime-edit-year-field {
  padding: 0;
}

::-webkit-inner-spin-button {
  height: auto;
}

[type=search] {
  outline-offset: -2px;
  -webkit-appearance: textfield;
}

/* rtl:raw:
[type="tel"],
[type="url"],
[type="email"],
[type="number"] {
  direction: ltr;
}
*/
::-webkit-search-decoration {
  -webkit-appearance: none;
}

::-webkit-color-swatch-wrapper {
  padding: 0;
}

::file-selector-button {
  font: inherit;
  -webkit-appearance: button;
}

output {
  display: inline-block;
}

iframe {
  border: 0;
}

summary {
  display: list-item;
  cursor: pointer;
}

progress {
  vertical-align: baseline;
}

[hidden] {
  display: none !important;
}

.lead {
  font-size: 1.25rem;
  font-weight: 300;
}

.display-1 {
  font-size: calc(1.625rem + 4.5vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .display-1 {
    font-size: 5rem;
  }
}

.display-2 {
  font-size: calc(1.575rem + 3.9vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .display-2 {
    font-size: 4.5rem;
  }
}

.display-3 {
  font-size: calc(1.525rem + 3.3vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .display-3 {
    font-size: 4rem;
  }
}

.display-4 {
  font-size: calc(1.475rem + 2.7vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .display-4 {
    font-size: 3.5rem;
  }
}

.display-5 {
  font-size: calc(1.425rem + 2.1vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .display-5 {
    font-size: 3rem;
  }
}

.display-6 {
  font-size: calc(1.375rem + 1.5vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .display-6 {
    font-size: 2.5rem;
  }
}

.list-unstyled {
  padding-left: 0;
  list-style: none;
}

.list-inline {
  padding-left: 0;
  list-style: none;
}

.list-inline-item {
  display: inline-block;
}
.list-inline-item:not(:last-child) {
  margin-right: 0.5rem;
}

.initialism {
  font-size: 0.875em;
  text-transform: uppercase;
}

.blockquote {
  margin-bottom: 1rem;
  font-size: 1.25rem;
}
.blockquote > :last-child {
  margin-bottom: 0;
}

.blockquote-footer {
  margin-top: -1rem;
  margin-bottom: 1rem;
  font-size: 0.875em;
  color: #6c757d;
}
.blockquote-footer::before {
  content: "— ";
}

.img-fluid {
  max-width: 100%;
  height: auto;
}

.img-thumbnail {
  padding: 0.25rem;
  background-color: #ffffff;
  border: 1px solid var(--bs-border-color);
  border-radius: 0.375rem;
  max-width: 100%;
  height: auto;
}

.figure {
  display: inline-block;
}

.figure-img {
  margin-bottom: 0.5rem;
  line-height: 1;
}

.figure-caption {
  font-size: 0.875em;
  color: #6c757d;
}

.container,
.container-fluid,
.container-xxxl,
.container-xxl,
.container-xl,
.container-lg,
.container-md,
.container-sm {
  --bs-gutter-x: 1.5rem;
  --bs-gutter-y: 0;
  width: 100%;
  padding-right: calc(var(--bs-gutter-x) * 0.5);
  padding-left: calc(var(--bs-gutter-x) * 0.5);
  margin-right: auto;
  margin-left: auto;
}

@media (min-width: 576px) {
  .container-sm, .container {
    max-width: 540px;
  }
}
@media (min-width: 768px) {
  .container-md, .container-sm, .container {
    max-width: 720px;
  }
}
@media (min-width: 992px) {
  .container-lg, .container-md, .container-sm, .container {
    max-width: 960px;
  }
}
@media (min-width: 1200px) {
  .container-xl, .container-lg, .container-md, .container-sm, .container {
    max-width: 1140px;
  }
}
@media (min-width: 1400px) {
  .container-xxl, .container-xl, .container-lg, .container-md, .container-sm, .container {
    max-width: 1320px;
  }
}
.container-xxl, .container-xl, .container-lg, .container-md, .container-sm, .container {
  max-width: 1560px;
}

.row {
  --bs-gutter-x: 1.5rem;
  --bs-gutter-y: 0;
  display: flex;
  flex-wrap: wrap;
  margin-top: calc(-1 * var(--bs-gutter-y));
  margin-right: calc(-0.5 * var(--bs-gutter-x));
  margin-left: calc(-0.5 * var(--bs-gutter-x));
}
.row > * {
  flex-shrink: 0;
  width: 100%;
  max-width: 100%;
  padding-right: calc(var(--bs-gutter-x) * 0.5);
  padding-left: calc(var(--bs-gutter-x) * 0.5);
  margin-top: var(--bs-gutter-y);
}

.col {
  flex: 1 0 0%;
}

.row-cols-auto > * {
  flex: 0 0 auto;
  width: auto;
}

.row-cols-1 > * {
  flex: 0 0 auto;
  width: 100%;
}

.row-cols-2 > * {
  flex: 0 0 auto;
  width: 50%;
}

.row-cols-3 > * {
  flex: 0 0 auto;
  width: 33.3333333333%;
}

.row-cols-4 > * {
  flex: 0 0 auto;
  width: 25%;
}

.row-cols-5 > * {
  flex: 0 0 auto;
  width: 20%;
}

.row-cols-6 > * {
  flex: 0 0 auto;
  width: 16.6666666667%;
}

.col-auto {
  flex: 0 0 auto;
  width: auto;
}

.col-1 {
  flex: 0 0 auto;
  width: 8.33333333%;
}

.col-2 {
  flex: 0 0 auto;
  width: 16.66666667%;
}

.col-3 {
  flex: 0 0 auto;
  width: 25%;
}

.col-4 {
  flex: 0 0 auto;
  width: 33.33333333%;
}

.col-5 {
  flex: 0 0 auto;
  width: 41.66666667%;
}

.col-6 {
  flex: 0 0 auto;
  width: 50%;
}

.col-7 {
  flex: 0 0 auto;
  width: 58.33333333%;
}

.col-8 {
  flex: 0 0 auto;
  width: 66.66666667%;
}

.col-9 {
  flex: 0 0 auto;
  width: 75%;
}

.col-10 {
  flex: 0 0 auto;
  width: 83.33333333%;
}

.col-11 {
  flex: 0 0 auto;
  width: 91.66666667%;
}

.col-12 {
  flex: 0 0 auto;
  width: 100%;
}

.offset-1 {
  margin-left: 8.33333333%;
}

.offset-2 {
  margin-left: 16.66666667%;
}

.offset-3 {
  margin-left: 25%;
}

.offset-4 {
  margin-left: 33.33333333%;
}

.offset-5 {
  margin-left: 41.66666667%;
}

.offset-6 {
  margin-left: 50%;
}

.offset-7 {
  margin-left: 58.33333333%;
}

.offset-8 {
  margin-left: 66.66666667%;
}

.offset-9 {
  margin-left: 75%;
}

.offset-10 {
  margin-left: 83.33333333%;
}

.offset-11 {
  margin-left: 91.66666667%;
}

.g-0,
.gx-0 {
  --bs-gutter-x: 0;
}

.g-0,
.gy-0 {
  --bs-gutter-y: 0;
}

.g-1,
.gx-1 {
  --bs-gutter-x: 0.25rem;
}

.g-1,
.gy-1 {
  --bs-gutter-y: 0.25rem;
}

.g-2,
.gx-2 {
  --bs-gutter-x: 0.5rem;
}

.g-2,
.gy-2 {
  --bs-gutter-y: 0.5rem;
}

.g-3,
.gx-3 {
  --bs-gutter-x: 1rem;
}

.g-3,
.gy-3 {
  --bs-gutter-y: 1rem;
}

.g-4,
.gx-4 {
  --bs-gutter-x: 1.5rem;
}

.g-4,
.gy-4 {
  --bs-gutter-y: 1.5rem;
}

.g-5,
.gx-5 {
  --bs-gutter-x: 3rem;
}

.g-5,
.gy-5 {
  --bs-gutter-y: 3rem;
}

.g-12,
.gx-12 {
  --bs-gutter-x: 0.75rem;
}

.g-12,
.gy-12 {
  --bs-gutter-y: 0.75rem;
}

.g-18,
.gx-18 {
  --bs-gutter-x: 1.125rem;
}

.g-18,
.gy-18 {
  --bs-gutter-y: 1.125rem;
}

.g-20,
.gx-20 {
  --bs-gutter-x: 1.25rem;
}

.g-20,
.gy-20 {
  --bs-gutter-y: 1.25rem;
}

.g-30,
.gx-30 {
  --bs-gutter-x: 1.875rem;
}

.g-30,
.gy-30 {
  --bs-gutter-y: 1.875rem;
}

.g-40,
.gx-40 {
  --bs-gutter-x: 2.5rem;
}

.g-40,
.gy-40 {
  --bs-gutter-y: 2.5rem;
}

@media (min-width: 576px) {
  .col-sm {
    flex: 1 0 0%;
  }
  .row-cols-sm-auto > * {
    flex: 0 0 auto;
    width: auto;
  }
  .row-cols-sm-1 > * {
    flex: 0 0 auto;
    width: 100%;
  }
  .row-cols-sm-2 > * {
    flex: 0 0 auto;
    width: 50%;
  }
  .row-cols-sm-3 > * {
    flex: 0 0 auto;
    width: 33.3333333333%;
  }
  .row-cols-sm-4 > * {
    flex: 0 0 auto;
    width: 25%;
  }
  .row-cols-sm-5 > * {
    flex: 0 0 auto;
    width: 20%;
  }
  .row-cols-sm-6 > * {
    flex: 0 0 auto;
    width: 16.6666666667%;
  }
  .col-sm-auto {
    flex: 0 0 auto;
    width: auto;
  }
  .col-sm-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }
  .col-sm-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }
  .col-sm-3 {
    flex: 0 0 auto;
    width: 25%;
  }
  .col-sm-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }
  .col-sm-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }
  .col-sm-6 {
    flex: 0 0 auto;
    width: 50%;
  }
  .col-sm-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }
  .col-sm-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  }
  .col-sm-9 {
    flex: 0 0 auto;
    width: 75%;
  }
  .col-sm-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  }
  .col-sm-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
  }
  .col-sm-12 {
    flex: 0 0 auto;
    width: 100%;
  }
  .offset-sm-0 {
    margin-left: 0;
  }
  .offset-sm-1 {
    margin-left: 8.33333333%;
  }
  .offset-sm-2 {
    margin-left: 16.66666667%;
  }
  .offset-sm-3 {
    margin-left: 25%;
  }
  .offset-sm-4 {
    margin-left: 33.33333333%;
  }
  .offset-sm-5 {
    margin-left: 41.66666667%;
  }
  .offset-sm-6 {
    margin-left: 50%;
  }
  .offset-sm-7 {
    margin-left: 58.33333333%;
  }
  .offset-sm-8 {
    margin-left: 66.66666667%;
  }
  .offset-sm-9 {
    margin-left: 75%;
  }
  .offset-sm-10 {
    margin-left: 83.33333333%;
  }
  .offset-sm-11 {
    margin-left: 91.66666667%;
  }
  .g-sm-0,
  .gx-sm-0 {
    --bs-gutter-x: 0;
  }
  .g-sm-0,
  .gy-sm-0 {
    --bs-gutter-y: 0;
  }
  .g-sm-1,
  .gx-sm-1 {
    --bs-gutter-x: 0.25rem;
  }
  .g-sm-1,
  .gy-sm-1 {
    --bs-gutter-y: 0.25rem;
  }
  .g-sm-2,
  .gx-sm-2 {
    --bs-gutter-x: 0.5rem;
  }
  .g-sm-2,
  .gy-sm-2 {
    --bs-gutter-y: 0.5rem;
  }
  .g-sm-3,
  .gx-sm-3 {
    --bs-gutter-x: 1rem;
  }
  .g-sm-3,
  .gy-sm-3 {
    --bs-gutter-y: 1rem;
  }
  .g-sm-4,
  .gx-sm-4 {
    --bs-gutter-x: 1.5rem;
  }
  .g-sm-4,
  .gy-sm-4 {
    --bs-gutter-y: 1.5rem;
  }
  .g-sm-5,
  .gx-sm-5 {
    --bs-gutter-x: 3rem;
  }
  .g-sm-5,
  .gy-sm-5 {
    --bs-gutter-y: 3rem;
  }
  .g-sm-12,
  .gx-sm-12 {
    --bs-gutter-x: 0.75rem;
  }
  .g-sm-12,
  .gy-sm-12 {
    --bs-gutter-y: 0.75rem;
  }
  .g-sm-18,
  .gx-sm-18 {
    --bs-gutter-x: 1.125rem;
  }
  .g-sm-18,
  .gy-sm-18 {
    --bs-gutter-y: 1.125rem;
  }
  .g-sm-20,
  .gx-sm-20 {
    --bs-gutter-x: 1.25rem;
  }
  .g-sm-20,
  .gy-sm-20 {
    --bs-gutter-y: 1.25rem;
  }
  .g-sm-30,
  .gx-sm-30 {
    --bs-gutter-x: 1.875rem;
  }
  .g-sm-30,
  .gy-sm-30 {
    --bs-gutter-y: 1.875rem;
  }
  .g-sm-40,
  .gx-sm-40 {
    --bs-gutter-x: 2.5rem;
  }
  .g-sm-40,
  .gy-sm-40 {
    --bs-gutter-y: 2.5rem;
  }
}
@media (min-width: 768px) {
  .col-md {
    flex: 1 0 0%;
  }
  .row-cols-md-auto > * {
    flex: 0 0 auto;
    width: auto;
  }
  .row-cols-md-1 > * {
    flex: 0 0 auto;
    width: 100%;
  }
  .row-cols-md-2 > * {
    flex: 0 0 auto;
    width: 50%;
  }
  .row-cols-md-3 > * {
    flex: 0 0 auto;
    width: 33.3333333333%;
  }
  .row-cols-md-4 > * {
    flex: 0 0 auto;
    width: 25%;
  }
  .row-cols-md-5 > * {
    flex: 0 0 auto;
    width: 20%;
  }
  .row-cols-md-6 > * {
    flex: 0 0 auto;
    width: 16.6666666667%;
  }
  .col-md-auto {
    flex: 0 0 auto;
    width: auto;
  }
  .col-md-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }
  .col-md-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }
  .col-md-3 {
    flex: 0 0 auto;
    width: 25%;
  }
  .col-md-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }
  .col-md-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }
  .col-md-6 {
    flex: 0 0 auto;
    width: 50%;
  }
  .col-md-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }
  .col-md-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  }
  .col-md-9 {
    flex: 0 0 auto;
    width: 75%;
  }
  .col-md-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  }
  .col-md-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
  }
  .col-md-12 {
    flex: 0 0 auto;
    width: 100%;
  }
  .offset-md-0 {
    margin-left: 0;
  }
  .offset-md-1 {
    margin-left: 8.33333333%;
  }
  .offset-md-2 {
    margin-left: 16.66666667%;
  }
  .offset-md-3 {
    margin-left: 25%;
  }
  .offset-md-4 {
    margin-left: 33.33333333%;
  }
  .offset-md-5 {
    margin-left: 41.66666667%;
  }
  .offset-md-6 {
    margin-left: 50%;
  }
  .offset-md-7 {
    margin-left: 58.33333333%;
  }
  .offset-md-8 {
    margin-left: 66.66666667%;
  }
  .offset-md-9 {
    margin-left: 75%;
  }
  .offset-md-10 {
    margin-left: 83.33333333%;
  }
  .offset-md-11 {
    margin-left: 91.66666667%;
  }
  .g-md-0,
  .gx-md-0 {
    --bs-gutter-x: 0;
  }
  .g-md-0,
  .gy-md-0 {
    --bs-gutter-y: 0;
  }
  .g-md-1,
  .gx-md-1 {
    --bs-gutter-x: 0.25rem;
  }
  .g-md-1,
  .gy-md-1 {
    --bs-gutter-y: 0.25rem;
  }
  .g-md-2,
  .gx-md-2 {
    --bs-gutter-x: 0.5rem;
  }
  .g-md-2,
  .gy-md-2 {
    --bs-gutter-y: 0.5rem;
  }
  .g-md-3,
  .gx-md-3 {
    --bs-gutter-x: 1rem;
  }
  .g-md-3,
  .gy-md-3 {
    --bs-gutter-y: 1rem;
  }
  .g-md-4,
  .gx-md-4 {
    --bs-gutter-x: 1.5rem;
  }
  .g-md-4,
  .gy-md-4 {
    --bs-gutter-y: 1.5rem;
  }
  .g-md-5,
  .gx-md-5 {
    --bs-gutter-x: 3rem;
  }
  .g-md-5,
  .gy-md-5 {
    --bs-gutter-y: 3rem;
  }
  .g-md-12,
  .gx-md-12 {
    --bs-gutter-x: 0.75rem;
  }
  .g-md-12,
  .gy-md-12 {
    --bs-gutter-y: 0.75rem;
  }
  .g-md-18,
  .gx-md-18 {
    --bs-gutter-x: 1.125rem;
  }
  .g-md-18,
  .gy-md-18 {
    --bs-gutter-y: 1.125rem;
  }
  .g-md-20,
  .gx-md-20 {
    --bs-gutter-x: 1.25rem;
  }
  .g-md-20,
  .gy-md-20 {
    --bs-gutter-y: 1.25rem;
  }
  .g-md-30,
  .gx-md-30 {
    --bs-gutter-x: 1.875rem;
  }
  .g-md-30,
  .gy-md-30 {
    --bs-gutter-y: 1.875rem;
  }
  .g-md-40,
  .gx-md-40 {
    --bs-gutter-x: 2.5rem;
  }
  .g-md-40,
  .gy-md-40 {
    --bs-gutter-y: 2.5rem;
  }
}
@media (min-width: 992px) {
  .col-lg {
    flex: 1 0 0%;
  }
  .row-cols-lg-auto > * {
    flex: 0 0 auto;
    width: auto;
  }
  .row-cols-lg-1 > * {
    flex: 0 0 auto;
    width: 100%;
  }
  .row-cols-lg-2 > * {
    flex: 0 0 auto;
    width: 50%;
  }
  .row-cols-lg-3 > * {
    flex: 0 0 auto;
    width: 33.3333333333%;
  }
  .row-cols-lg-4 > * {
    flex: 0 0 auto;
    width: 25%;
  }
  .row-cols-lg-5 > * {
    flex: 0 0 auto;
    width: 20%;
  }
  .row-cols-lg-6 > * {
    flex: 0 0 auto;
    width: 16.6666666667%;
  }
  .col-lg-auto {
    flex: 0 0 auto;
    width: auto;
  }
  .col-lg-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }
  .col-lg-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }
  .col-lg-3 {
    flex: 0 0 auto;
    width: 25%;
  }
  .col-lg-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }
  .col-lg-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }
  .col-lg-6 {
    flex: 0 0 auto;
    width: 50%;
  }
  .col-lg-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }
  .col-lg-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  }
  .col-lg-9 {
    flex: 0 0 auto;
    width: 75%;
  }
  .col-lg-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  }
  .col-lg-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
  }
  .col-lg-12 {
    flex: 0 0 auto;
    width: 100%;
  }
  .offset-lg-0 {
    margin-left: 0;
  }
  .offset-lg-1 {
    margin-left: 8.33333333%;
  }
  .offset-lg-2 {
    margin-left: 16.66666667%;
  }
  .offset-lg-3 {
    margin-left: 25%;
  }
  .offset-lg-4 {
    margin-left: 33.33333333%;
  }
  .offset-lg-5 {
    margin-left: 41.66666667%;
  }
  .offset-lg-6 {
    margin-left: 50%;
  }
  .offset-lg-7 {
    margin-left: 58.33333333%;
  }
  .offset-lg-8 {
    margin-left: 66.66666667%;
  }
  .offset-lg-9 {
    margin-left: 75%;
  }
  .offset-lg-10 {
    margin-left: 83.33333333%;
  }
  .offset-lg-11 {
    margin-left: 91.66666667%;
  }
  .g-lg-0,
  .gx-lg-0 {
    --bs-gutter-x: 0;
  }
  .g-lg-0,
  .gy-lg-0 {
    --bs-gutter-y: 0;
  }
  .g-lg-1,
  .gx-lg-1 {
    --bs-gutter-x: 0.25rem;
  }
  .g-lg-1,
  .gy-lg-1 {
    --bs-gutter-y: 0.25rem;
  }
  .g-lg-2,
  .gx-lg-2 {
    --bs-gutter-x: 0.5rem;
  }
  .g-lg-2,
  .gy-lg-2 {
    --bs-gutter-y: 0.5rem;
  }
  .g-lg-3,
  .gx-lg-3 {
    --bs-gutter-x: 1rem;
  }
  .g-lg-3,
  .gy-lg-3 {
    --bs-gutter-y: 1rem;
  }
  .g-lg-4,
  .gx-lg-4 {
    --bs-gutter-x: 1.5rem;
  }
  .g-lg-4,
  .gy-lg-4 {
    --bs-gutter-y: 1.5rem;
  }
  .g-lg-5,
  .gx-lg-5 {
    --bs-gutter-x: 3rem;
  }
  .g-lg-5,
  .gy-lg-5 {
    --bs-gutter-y: 3rem;
  }
  .g-lg-12,
  .gx-lg-12 {
    --bs-gutter-x: 0.75rem;
  }
  .g-lg-12,
  .gy-lg-12 {
    --bs-gutter-y: 0.75rem;
  }
  .g-lg-18,
  .gx-lg-18 {
    --bs-gutter-x: 1.125rem;
  }
  .g-lg-18,
  .gy-lg-18 {
    --bs-gutter-y: 1.125rem;
  }
  .g-lg-20,
  .gx-lg-20 {
    --bs-gutter-x: 1.25rem;
  }
  .g-lg-20,
  .gy-lg-20 {
    --bs-gutter-y: 1.25rem;
  }
  .g-lg-30,
  .gx-lg-30 {
    --bs-gutter-x: 1.875rem;
  }
  .g-lg-30,
  .gy-lg-30 {
    --bs-gutter-y: 1.875rem;
  }
  .g-lg-40,
  .gx-lg-40 {
    --bs-gutter-x: 2.5rem;
  }
  .g-lg-40,
  .gy-lg-40 {
    --bs-gutter-y: 2.5rem;
  }
}
@media (min-width: 1200px) {
  .col-xl {
    flex: 1 0 0%;
  }
  .row-cols-xl-auto > * {
    flex: 0 0 auto;
    width: auto;
  }
  .row-cols-xl-1 > * {
    flex: 0 0 auto;
    width: 100%;
  }
  .row-cols-xl-2 > * {
    flex: 0 0 auto;
    width: 50%;
  }
  .row-cols-xl-3 > * {
    flex: 0 0 auto;
    width: 33.3333333333%;
  }
  .row-cols-xl-4 > * {
    flex: 0 0 auto;
    width: 25%;
  }
  .row-cols-xl-5 > * {
    flex: 0 0 auto;
    width: 20%;
  }
  .row-cols-xl-6 > * {
    flex: 0 0 auto;
    width: 16.6666666667%;
  }
  .col-xl-auto {
    flex: 0 0 auto;
    width: auto;
  }
  .col-xl-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }
  .col-xl-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }
  .col-xl-3 {
    flex: 0 0 auto;
    width: 25%;
  }
  .col-xl-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }
  .col-xl-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }
  .col-xl-6 {
    flex: 0 0 auto;
    width: 50%;
  }
  .col-xl-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }
  .col-xl-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  }
  .col-xl-9 {
    flex: 0 0 auto;
    width: 75%;
  }
  .col-xl-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  }
  .col-xl-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
  }
  .col-xl-12 {
    flex: 0 0 auto;
    width: 100%;
  }
  .offset-xl-0 {
    margin-left: 0;
  }
  .offset-xl-1 {
    margin-left: 8.33333333%;
  }
  .offset-xl-2 {
    margin-left: 16.66666667%;
  }
  .offset-xl-3 {
    margin-left: 25%;
  }
  .offset-xl-4 {
    margin-left: 33.33333333%;
  }
  .offset-xl-5 {
    margin-left: 41.66666667%;
  }
  .offset-xl-6 {
    margin-left: 50%;
  }
  .offset-xl-7 {
    margin-left: 58.33333333%;
  }
  .offset-xl-8 {
    margin-left: 66.66666667%;
  }
  .offset-xl-9 {
    margin-left: 75%;
  }
  .offset-xl-10 {
    margin-left: 83.33333333%;
  }
  .offset-xl-11 {
    margin-left: 91.66666667%;
  }
  .g-xl-0,
  .gx-xl-0 {
    --bs-gutter-x: 0;
  }
  .g-xl-0,
  .gy-xl-0 {
    --bs-gutter-y: 0;
  }
  .g-xl-1,
  .gx-xl-1 {
    --bs-gutter-x: 0.25rem;
  }
  .g-xl-1,
  .gy-xl-1 {
    --bs-gutter-y: 0.25rem;
  }
  .g-xl-2,
  .gx-xl-2 {
    --bs-gutter-x: 0.5rem;
  }
  .g-xl-2,
  .gy-xl-2 {
    --bs-gutter-y: 0.5rem;
  }
  .g-xl-3,
  .gx-xl-3 {
    --bs-gutter-x: 1rem;
  }
  .g-xl-3,
  .gy-xl-3 {
    --bs-gutter-y: 1rem;
  }
  .g-xl-4,
  .gx-xl-4 {
    --bs-gutter-x: 1.5rem;
  }
  .g-xl-4,
  .gy-xl-4 {
    --bs-gutter-y: 1.5rem;
  }
  .g-xl-5,
  .gx-xl-5 {
    --bs-gutter-x: 3rem;
  }
  .g-xl-5,
  .gy-xl-5 {
    --bs-gutter-y: 3rem;
  }
  .g-xl-12,
  .gx-xl-12 {
    --bs-gutter-x: 0.75rem;
  }
  .g-xl-12,
  .gy-xl-12 {
    --bs-gutter-y: 0.75rem;
  }
  .g-xl-18,
  .gx-xl-18 {
    --bs-gutter-x: 1.125rem;
  }
  .g-xl-18,
  .gy-xl-18 {
    --bs-gutter-y: 1.125rem;
  }
  .g-xl-20,
  .gx-xl-20 {
    --bs-gutter-x: 1.25rem;
  }
  .g-xl-20,
  .gy-xl-20 {
    --bs-gutter-y: 1.25rem;
  }
  .g-xl-30,
  .gx-xl-30 {
    --bs-gutter-x: 1.875rem;
  }
  .g-xl-30,
  .gy-xl-30 {
    --bs-gutter-y: 1.875rem;
  }
  .g-xl-40,
  .gx-xl-40 {
    --bs-gutter-x: 2.5rem;
  }
  .g-xl-40,
  .gy-xl-40 {
    --bs-gutter-y: 2.5rem;
  }
}
@media (min-width: 1400px) {
  .col-xxl {
    flex: 1 0 0%;
  }
  .row-cols-xxl-auto > * {
    flex: 0 0 auto;
    width: auto;
  }
  .row-cols-xxl-1 > * {
    flex: 0 0 auto;
    width: 100%;
  }
  .row-cols-xxl-2 > * {
    flex: 0 0 auto;
    width: 50%;
  }
  .row-cols-xxl-3 > * {
    flex: 0 0 auto;
    width: 33.3333333333%;
  }
  .row-cols-xxl-4 > * {
    flex: 0 0 auto;
    width: 25%;
  }
  .row-cols-xxl-5 > * {
    flex: 0 0 auto;
    width: 20%;
  }
  .row-cols-xxl-6 > * {
    flex: 0 0 auto;
    width: 16.6666666667%;
  }
  .col-xxl-auto {
    flex: 0 0 auto;
    width: auto;
  }
  .col-xxl-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }
  .col-xxl-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }
  .col-xxl-3 {
    flex: 0 0 auto;
    width: 25%;
  }
  .col-xxl-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }
  .col-xxl-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }
  .col-xxl-6 {
    flex: 0 0 auto;
    width: 50%;
  }
  .col-xxl-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }
  .col-xxl-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  }
  .col-xxl-9 {
    flex: 0 0 auto;
    width: 75%;
  }
  .col-xxl-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  }
  .col-xxl-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
  }
  .col-xxl-12 {
    flex: 0 0 auto;
    width: 100%;
  }
  .offset-xxl-0 {
    margin-left: 0;
  }
  .offset-xxl-1 {
    margin-left: 8.33333333%;
  }
  .offset-xxl-2 {
    margin-left: 16.66666667%;
  }
  .offset-xxl-3 {
    margin-left: 25%;
  }
  .offset-xxl-4 {
    margin-left: 33.33333333%;
  }
  .offset-xxl-5 {
    margin-left: 41.66666667%;
  }
  .offset-xxl-6 {
    margin-left: 50%;
  }
  .offset-xxl-7 {
    margin-left: 58.33333333%;
  }
  .offset-xxl-8 {
    margin-left: 66.66666667%;
  }
  .offset-xxl-9 {
    margin-left: 75%;
  }
  .offset-xxl-10 {
    margin-left: 83.33333333%;
  }
  .offset-xxl-11 {
    margin-left: 91.66666667%;
  }
  .g-xxl-0,
  .gx-xxl-0 {
    --bs-gutter-x: 0;
  }
  .g-xxl-0,
  .gy-xxl-0 {
    --bs-gutter-y: 0;
  }
  .g-xxl-1,
  .gx-xxl-1 {
    --bs-gutter-x: 0.25rem;
  }
  .g-xxl-1,
  .gy-xxl-1 {
    --bs-gutter-y: 0.25rem;
  }
  .g-xxl-2,
  .gx-xxl-2 {
    --bs-gutter-x: 0.5rem;
  }
  .g-xxl-2,
  .gy-xxl-2 {
    --bs-gutter-y: 0.5rem;
  }
  .g-xxl-3,
  .gx-xxl-3 {
    --bs-gutter-x: 1rem;
  }
  .g-xxl-3,
  .gy-xxl-3 {
    --bs-gutter-y: 1rem;
  }
  .g-xxl-4,
  .gx-xxl-4 {
    --bs-gutter-x: 1.5rem;
  }
  .g-xxl-4,
  .gy-xxl-4 {
    --bs-gutter-y: 1.5rem;
  }
  .g-xxl-5,
  .gx-xxl-5 {
    --bs-gutter-x: 3rem;
  }
  .g-xxl-5,
  .gy-xxl-5 {
    --bs-gutter-y: 3rem;
  }
  .g-xxl-12,
  .gx-xxl-12 {
    --bs-gutter-x: 0.75rem;
  }
  .g-xxl-12,
  .gy-xxl-12 {
    --bs-gutter-y: 0.75rem;
  }
  .g-xxl-18,
  .gx-xxl-18 {
    --bs-gutter-x: 1.125rem;
  }
  .g-xxl-18,
  .gy-xxl-18 {
    --bs-gutter-y: 1.125rem;
  }
  .g-xxl-20,
  .gx-xxl-20 {
    --bs-gutter-x: 1.25rem;
  }
  .g-xxl-20,
  .gy-xxl-20 {
    --bs-gutter-y: 1.25rem;
  }
  .g-xxl-30,
  .gx-xxl-30 {
    --bs-gutter-x: 1.875rem;
  }
  .g-xxl-30,
  .gy-xxl-30 {
    --bs-gutter-y: 1.875rem;
  }
  .g-xxl-40,
  .gx-xxl-40 {
    --bs-gutter-x: 2.5rem;
  }
  .g-xxl-40,
  .gy-xxl-40 {
    --bs-gutter-y: 2.5rem;
  }
}
.table {
  --bs-table-color: var(--bs-body-color);
  --bs-table-bg: transparent;
  --bs-table-border-color: var(--bs-border-color);
  --bs-table-accent-bg: transparent;
  --bs-table-striped-color: var(--bs-body-color);
  --bs-table-striped-bg: rgba(0, 0, 0, 0.05);
  --bs-table-active-color: var(--bs-body-color);
  --bs-table-active-bg: rgba(0, 0, 0, 0.1);
  --bs-table-hover-color: var(--bs-body-color);
  --bs-table-hover-bg: rgba(0, 0, 0, 0.075);
  width: 100%;
  margin-bottom: 1rem;
  color: var(--bs-table-color);
  vertical-align: top;
  border-color: var(--bs-table-border-color);
}
.table > :not(caption) > * > * {
  padding: 0.5rem 0.5rem;
  background-color: var(--bs-table-bg);
  border-bottom-width: 1px;
  box-shadow: inset 0 0 0 9999px var(--bs-table-accent-bg);
}
.table > tbody {
  vertical-align: inherit;
}
.table > thead {
  vertical-align: bottom;
}

.table-group-divider {
  border-top: 2px solid currentcolor;
}

.caption-top {
  caption-side: top;
}

.table-sm > :not(caption) > * > * {
  padding: 0.25rem 0.25rem;
}

.table-bordered > :not(caption) > * {
  border-width: 1px 0;
}
.table-bordered > :not(caption) > * > * {
  border-width: 0 1px;
}

.table-borderless > :not(caption) > * > * {
  border-bottom-width: 0;
}
.table-borderless > :not(:first-child) {
  border-top-width: 0;
}

.table-striped > tbody > tr:nth-of-type(odd) > * {
  --bs-table-accent-bg: var(--bs-table-striped-bg);
  color: var(--bs-table-striped-color);
}

.table-striped-columns > :not(caption) > tr > :nth-child(even) {
  --bs-table-accent-bg: var(--bs-table-striped-bg);
  color: var(--bs-table-striped-color);
}

.table-active {
  --bs-table-accent-bg: var(--bs-table-active-bg);
  color: var(--bs-table-active-color);
}

.table-hover > tbody > tr:hover > * {
  --bs-table-accent-bg: var(--bs-table-hover-bg);
  color: var(--bs-table-hover-color);
}

.table-primary {
  --bs-table-color: #000000;
  --bs-table-bg: #d9dcf9;
  --bs-table-border-color: #c3c6e0;
  --bs-table-striped-bg: #ced1ed;
  --bs-table-striped-color: #000000;
  --bs-table-active-bg: #c3c6e0;
  --bs-table-active-color: #000000;
  --bs-table-hover-bg: #c9cce6;
  --bs-table-hover-color: #000000;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color);
}

.table-secondary {
  --bs-table-color: #000000;
  --bs-table-bg: #d9d9d9;
  --bs-table-border-color: #c3c3c3;
  --bs-table-striped-bg: #cecece;
  --bs-table-striped-color: #000000;
  --bs-table-active-bg: #c3c3c3;
  --bs-table-active-color: #000000;
  --bs-table-hover-bg: #c9c9c9;
  --bs-table-hover-color: #000000;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color);
}

.table-success {
  --bs-table-color: #000000;
  --bs-table-bg: #d9f5e1;
  --bs-table-border-color: #c3ddcb;
  --bs-table-striped-bg: #cee9d6;
  --bs-table-striped-color: #000000;
  --bs-table-active-bg: #c3ddcb;
  --bs-table-active-color: #000000;
  --bs-table-hover-bg: #c9e3d0;
  --bs-table-hover-color: #000000;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color);
}

.table-info {
  --bs-table-color: #000000;
  --bs-table-bg: #ccecf1;
  --bs-table-border-color: #b8d4d9;
  --bs-table-striped-bg: #c2e0e5;
  --bs-table-striped-color: #000000;
  --bs-table-active-bg: #b8d4d9;
  --bs-table-active-color: #000000;
  --bs-table-hover-bg: #bddadf;
  --bs-table-hover-color: #000000;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color);
}

.table-warning {
  --bs-table-color: #000000;
  --bs-table-bg: #fff1d6;
  --bs-table-border-color: #e6d9c1;
  --bs-table-striped-bg: #f2e5cb;
  --bs-table-striped-color: #000000;
  --bs-table-active-bg: #e6d9c1;
  --bs-table-active-color: #000000;
  --bs-table-hover-bg: #ecdfc6;
  --bs-table-hover-color: #000000;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color);
}

.table-danger {
  --bs-table-color: #000000;
  --bs-table-bg: #ffd3d3;
  --bs-table-border-color: #e6bebe;
  --bs-table-striped-bg: #f2c8c8;
  --bs-table-striped-color: #000000;
  --bs-table-active-bg: #e6bebe;
  --bs-table-active-color: #000000;
  --bs-table-hover-bg: #ecc3c3;
  --bs-table-hover-color: #000000;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color);
}

.table-light {
  --bs-table-color: #000000;
  --bs-table-bg: #ffffff;
  --bs-table-border-color: #e6e6e6;
  --bs-table-striped-bg: #f2f2f2;
  --bs-table-striped-color: #000000;
  --bs-table-active-bg: #e6e6e6;
  --bs-table-active-color: #000000;
  --bs-table-hover-bg: #ececec;
  --bs-table-hover-color: #000000;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color);
}

.table-dark {
  --bs-table-color: #ffffff;
  --bs-table-bg: #111120;
  --bs-table-border-color: #292936;
  --bs-table-striped-bg: #1d1d2b;
  --bs-table-striped-color: #ffffff;
  --bs-table-active-bg: #292936;
  --bs-table-active-color: #ffffff;
  --bs-table-hover-bg: #232331;
  --bs-table-hover-color: #ffffff;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color);
}

.table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

@media (max-width: 575.98px) {
  .table-responsive-sm {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}
@media (max-width: 767.98px) {
  .table-responsive-md {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}
@media (max-width: 991.98px) {
  .table-responsive-lg {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}
@media (max-width: 1199.98px) {
  .table-responsive-xl {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}
@media (max-width: 1399.98px) {
  .table-responsive-xxl {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}
.form-label {
  margin-bottom: 0.5rem;
}

.col-form-label {
  padding-top: calc(0.375rem + 1px);
  padding-bottom: calc(0.375rem + 1px);
  margin-bottom: 0;
  font-size: inherit;
  line-height: 1.5;
}

.col-form-label-lg {
  padding-top: calc(0.5rem + 1px);
  padding-bottom: calc(0.5rem + 1px);
  font-size: 1.25rem;
}

.col-form-label-sm {
  padding-top: calc(0.25rem + 1px);
  padding-bottom: calc(0.25rem + 1px);
  font-size: 0.875rem;
}

.form-text {
  margin-top: 0.25rem;
  font-size: 0.875em;
  color: #6c757d;
}

.form-control {
  display: block;
  width: 100%;
  padding: 0.375rem 0.75rem;
  font-size: 15px;
  font-weight: 400;
  line-height: 1.5;
  color: #212529;
  background-color: #ffffff;
  background-clip: padding-box;
  border: 1px solid #d7dde9;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  border-radius: 0.375rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-control {
    transition: none;
  }
}
.form-control[type=file] {
  overflow: hidden;
}
.form-control[type=file]:not(:disabled):not([readonly]) {
  cursor: pointer;
}
.form-control:focus {
  color: #212529;
  background-color: #ffffff;
  border-color: #9fa9f1;
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(63, 82, 227, 0.25);
}
.form-control::-webkit-date-and-time-value {
  height: 1.5em;
}
.form-control::-moz-placeholder {
  color: #6c757d;
  opacity: 1;
}
.form-control::placeholder {
  color: #6c757d;
  opacity: 1;
}
.form-control:disabled {
  background-color: #e9ecef;
  opacity: 1;
}
.form-control::file-selector-button {
  padding: 0.375rem 0.75rem;
  margin: -0.375rem -0.75rem;
  margin-inline-end: 0.75rem;
  color: #212529;
  background-color: #e9ecef;
  pointer-events: none;
  border-color: inherit;
  border-style: solid;
  border-width: 0;
  border-inline-end-width: 1px;
  border-radius: 0;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-control::file-selector-button {
    transition: none;
  }
}
.form-control:hover:not(:disabled):not([readonly])::file-selector-button {
  background-color: #dde0e3;
}

.form-control-plaintext {
  display: block;
  width: 100%;
  padding: 0.375rem 0;
  margin-bottom: 0;
  line-height: 1.5;
  color: #212529;
  background-color: transparent;
  border: solid transparent;
  border-width: 1px 0;
}
.form-control-plaintext:focus {
  outline: 0;
}
.form-control-plaintext.form-control-sm, .form-control-plaintext.form-control-lg {
  padding-right: 0;
  padding-left: 0;
}

.form-control-sm {
  min-height: calc(1.5em + 0.5rem + 2px);
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  border-radius: 0.25rem;
}
.form-control-sm::file-selector-button {
  padding: 0.25rem 0.5rem;
  margin: -0.25rem -0.5rem;
  margin-inline-end: 0.5rem;
}

.form-control-lg {
  min-height: calc(1.5em + 1rem + 2px);
  padding: 0.5rem 1rem;
  font-size: 1.25rem;
  border-radius: 0.5rem;
}
.form-control-lg::file-selector-button {
  padding: 0.5rem 1rem;
  margin: -0.5rem -1rem;
  margin-inline-end: 1rem;
}

textarea.form-control {
  min-height: calc(1.5em + 0.75rem + 2px);
}
textarea.form-control-sm {
  min-height: calc(1.5em + 0.5rem + 2px);
}
textarea.form-control-lg {
  min-height: calc(1.5em + 1rem + 2px);
}

.form-control-color {
  width: 3rem;
  height: calc(1.5em + 0.75rem + 2px);
  padding: 0.375rem;
}
.form-control-color:not(:disabled):not([readonly]) {
  cursor: pointer;
}
.form-control-color::-moz-color-swatch {
  border: 0 !important;
  border-radius: 0.375rem;
}
.form-control-color::-webkit-color-swatch {
  border-radius: 0.375rem;
}
.form-control-color.form-control-sm {
  height: calc(1.5em + 0.5rem + 2px);
}
.form-control-color.form-control-lg {
  height: calc(1.5em + 1rem + 2px);
}

.form-select {
  display: block;
  width: 100%;
  padding: 0.375rem 2.25rem 0.375rem 0.75rem;
  -moz-padding-start: calc(0.75rem - 3px);
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #212529;
  background-color: #ffffff;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 16px 12px;
  border: 1px solid #d7dde9;
  border-radius: 0.375rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
@media (prefers-reduced-motion: reduce) {
  .form-select {
    transition: none;
  }
}
.form-select:focus {
  border-color: #9fa9f1;
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(63, 82, 227, 0.25);
}
.form-select[multiple], .form-select[size]:not([size="1"]) {
  padding-right: 0.75rem;
  background-image: none;
}
.form-select:disabled {
  background-color: #e9ecef;
}
.form-select:-moz-focusring {
  color: transparent;
  text-shadow: 0 0 0 #212529;
}

.form-select-sm {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  padding-left: 0.5rem;
  font-size: 0.875rem;
  border-radius: 0.25rem;
}

.form-select-lg {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 1rem;
  font-size: 1.25rem;
  border-radius: 0.5rem;
}

.form-check {
  display: block;
  min-height: 1.5rem;
  padding-left: 1.5em;
  margin-bottom: 0.125rem;
}
.form-check .form-check-input {
  float: left;
  margin-left: -1.5em;
}

.form-check-reverse {
  padding-right: 1.5em;
  padding-left: 0;
  text-align: right;

}
.form-check-reverse .form-check-input {
  float: right;
  margin-right: -1.5em;
  margin-left: 0;

}

.form-check-input {
  width: 1em;
  height: 1em;
  margin-top: 0.25em;
  vertical-align: top;
  background-color: #ffffff;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  border: 1px solid rgba(0, 0, 0, 0.25);
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
}
.form-check-input[type=checkbox] {
  border-radius: 0.25em;
}
.form-check-input[type=radio] {
  border-radius: 50%;
}
.form-check-input:active {
  filter: brightness(90%);
}
.form-check-input:focus {
  border-color: #9fa9f1;
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(63, 82, 227, 0.25);
}
.form-check-input:checked {
  background-color: #2fabf7;
  border-color: #2fabf7;
}
.form-check-input:checked[type=checkbox] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/%3e%3c/svg%3e");
}
.form-check-input:checked[type=radio] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23ffffff'/%3e%3c/svg%3e");
}
.form-check-input[type=checkbox]:indeterminate {
  background-color: #2fabf7;
  border-color: #2fabf7;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/%3e%3c/svg%3e");
}
.form-check-input:disabled {
  pointer-events: none;
  filter: none;
  opacity: 0.5;
}
.form-check-input[disabled] ~ .form-check-label, .form-check-input:disabled ~ .form-check-label {
  cursor: default;
  opacity: 0.5;
}

.form-switch {
  padding-left: 2.5em;
}
.form-switch .form-check-input {
  width: 2em;
  margin-left: -2.5em;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%280, 0, 0, 0.25%29'/%3e%3c/svg%3e");
  background-position: left center;
  border-radius: 2em;
  transition: background-position 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-switch .form-check-input {
    transition: none;
  }
}
.form-switch .form-check-input:focus {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%239fa9f1'/%3e%3c/svg%3e");
}
.form-switch .form-check-input:checked {
  background-position: right center;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23ffffff'/%3e%3c/svg%3e");
}
.form-switch.form-check-reverse {
  padding-right: 2.5em;
  padding-left: 0;
}
.form-switch.form-check-reverse .form-check-input {
  margin-right: -2.5em;
  margin-left: 0;
}

.form-check-inline {
  display: inline-block;
  margin-right: 1rem;
}

.btn-check {
  position: absolute;
  clip: rect(0, 0, 0, 0);
  pointer-events: none;
}
.btn-check[disabled] + .btn, .btn-check:disabled + .btn {
  pointer-events: none;
  filter: none;
  opacity: 0.65;
}

.form-range {
  width: 100%;
  height: 1.5rem;
  padding: 0;
  background-color: transparent;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
.form-range:focus {
  outline: 0;
}
.form-range:focus::-webkit-slider-thumb {
  box-shadow: 0 0 0 1px #ffffff, 0 0 0 0.25rem rgba(63, 82, 227, 0.25);
}
.form-range:focus::-moz-range-thumb {
  box-shadow: 0 0 0 1px #ffffff, 0 0 0 0.25rem rgba(63, 82, 227, 0.25);
}
.form-range::-moz-focus-outer {
  border: 0;
}
.form-range::-webkit-slider-thumb {
  width: 1rem;
  height: 1rem;
  margin-top: -0.25rem;
  background-color: #2fabf7;
  border: 0;
  border-radius: 1rem;
  -webkit-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  -webkit-appearance: none;
          appearance: none;
}
@media (prefers-reduced-motion: reduce) {
  .form-range::-webkit-slider-thumb {
    -webkit-transition: none;
    transition: none;
  }
}
.form-range::-webkit-slider-thumb:active {
  background-color: #c5cbf7;
}
.form-range::-webkit-slider-runnable-track {
  width: 100%;
  height: 0.5rem;
  color: transparent;
  cursor: pointer;
  background-color: #dee2e6;
  border-color: transparent;
  border-radius: 1rem;
}
.form-range::-moz-range-thumb {
  width: 1rem;
  height: 1rem;
  background-color: #2fabf7;
  border: 0;
  border-radius: 1rem;
  -moz-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  -moz-appearance: none;
       appearance: none;
}
@media (prefers-reduced-motion: reduce) {
  .form-range::-moz-range-thumb {
    -moz-transition: none;
    transition: none;
  }
}
.form-range::-moz-range-thumb:active {
  background-color: #c5cbf7;
}
.form-range::-moz-range-track {
  width: 100%;
  height: 0.5rem;
  color: transparent;
  cursor: pointer;
  background-color: #dee2e6;
  border-color: transparent;
  border-radius: 1rem;
}
.form-range:disabled {
  pointer-events: none;
}
.form-range:disabled::-webkit-slider-thumb {
  background-color: #adb5bd;
}
.form-range:disabled::-moz-range-thumb {
  background-color: #adb5bd;
}

.form-floating {
  position: relative;
}
.form-floating > .form-control,
.form-floating > .form-control-plaintext,
.form-floating > .form-select {
  height: calc(3.5rem + 2px);
  line-height: 1.25;
}
.form-floating > label {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 1rem 0.75rem;
  overflow: hidden;
  text-align: start;
  text-overflow: ellipsis;
  white-space: nowrap;
  pointer-events: none;
  border: 1px solid transparent;
  transform-origin: 0 0;
  transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-floating > label {
    transition: none;
  }
}
.form-floating > .form-control,
.form-floating > .form-control-plaintext {
  padding: 1rem 0.75rem;
}
.form-floating > .form-control::-moz-placeholder, .form-floating > .form-control-plaintext::-moz-placeholder {
  color: transparent;
}
.form-floating > .form-control::placeholder,
.form-floating > .form-control-plaintext::placeholder {
  color: transparent;
}
.form-floating > .form-control:not(:-moz-placeholder-shown), .form-floating > .form-control-plaintext:not(:-moz-placeholder-shown) {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}
.form-floating > .form-control:focus, .form-floating > .form-control:not(:placeholder-shown),
.form-floating > .form-control-plaintext:focus,
.form-floating > .form-control-plaintext:not(:placeholder-shown) {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}
.form-floating > .form-control:-webkit-autofill,
.form-floating > .form-control-plaintext:-webkit-autofill {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}
.form-floating > .form-select {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}
.form-floating > .form-control:not(:-moz-placeholder-shown) ~ label {
  opacity: 0.65;
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}
.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label,
.form-floating > .form-control-plaintext ~ label,
.form-floating > .form-select ~ label {
  opacity: 0.65;
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}
.form-floating > .form-control:-webkit-autofill ~ label {
  opacity: 0.65;
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}
.form-floating > .form-control-plaintext ~ label {
  border-width: 1px 0;
}

.input-group {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;
  width: 100%;
}
.input-group > .form-control,
.input-group > .form-select,
.input-group > .form-floating {
  position: relative;
  flex: 1 1 auto;
  width: 1%;
  min-width: 0;
}
.input-group > .form-control:focus,
.input-group > .form-select:focus,
.input-group > .form-floating:focus-within {
  z-index: 5;
}
.input-group .btn {
  position: relative;
  z-index: 2;
}
.input-group .btn:focus {
  z-index: 5;
}

.input-group-text {
  display: flex;
  align-items: center;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #212529;
  text-align: center;
  white-space: nowrap;
  background-color: #e9ecef;
  border: 1px solid #d7dde9;
  border-radius: 0.375rem;
}

.input-group-lg > .form-control,
.input-group-lg > .form-select,
.input-group-lg > .input-group-text,
.input-group-lg > .btn {
  padding: 0.5rem 1rem;
  font-size: 1.25rem;
  border-radius: 0.5rem;
}

.input-group-sm > .form-control,
.input-group-sm > .form-select,
.input-group-sm > .input-group-text,
.input-group-sm > .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  border-radius: 0.25rem;
}

.input-group-lg > .form-select,
.input-group-sm > .form-select {
  padding-right: 3rem;
}

.input-group:not(.has-validation) > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu):not(.form-floating),
.input-group:not(.has-validation) > .dropdown-toggle:nth-last-child(n+3),
.input-group:not(.has-validation) > .form-floating:not(:last-child) > .form-control,
.input-group:not(.has-validation) > .form-floating:not(:last-child) > .form-select {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.input-group.has-validation > :nth-last-child(n+3):not(.dropdown-toggle):not(.dropdown-menu):not(.form-floating),
.input-group.has-validation > .dropdown-toggle:nth-last-child(n+4),
.input-group.has-validation > .form-floating:nth-last-child(n+3) > .form-control,
.input-group.has-validation > .form-floating:nth-last-child(n+3) > .form-select {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
  margin-left: -1px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.input-group > .form-floating:not(:first-child) > .form-control,
.input-group > .form-floating:not(:first-child) > .form-select {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.valid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875em;
  color: #41cc68;
}

.valid-tooltip {
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.25rem 0.5rem;
  margin-top: 0.1rem;
  font-size: 0.875rem;
  color: #000000;
  background-color: rgba(65, 204, 104, 0.9);
  border-radius: 0.375rem;
}

.was-validated :valid ~ .valid-feedback,
.was-validated :valid ~ .valid-tooltip,
.is-valid ~ .valid-feedback,
.is-valid ~ .valid-tooltip {
  display: block;
}

.was-validated .form-control:valid, .form-control.is-valid {
  border-color: #41cc68;
  padding-right: calc(1.5em + 0.75rem);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2341cc68' d='M2.3 6.73.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}
.was-validated .form-control:valid:focus, .form-control.is-valid:focus {
  border-color: #41cc68;
  box-shadow: 0 0 0 0.25rem rgba(65, 204, 104, 0.25);
}

.was-validated textarea.form-control:valid, textarea.form-control.is-valid {
  padding-right: calc(1.5em + 0.75rem);
  background-position: top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem);
}

.was-validated .form-select:valid, .form-select.is-valid {
  border-color: #41cc68;
}
.was-validated .form-select:valid:not([multiple]):not([size]), .was-validated .form-select:valid:not([multiple])[size="1"], .form-select.is-valid:not([multiple]):not([size]), .form-select.is-valid:not([multiple])[size="1"] {
  padding-right: 4.125rem;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e"), url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2341cc68' d='M2.3 6.73.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
  background-position: right 0.75rem center, center right 2.25rem;
  background-size: 16px 12px, calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}
.was-validated .form-select:valid:focus, .form-select.is-valid:focus {
  border-color: #41cc68;
  box-shadow: 0 0 0 0.25rem rgba(65, 204, 104, 0.25);
}

.was-validated .form-control-color:valid, .form-control-color.is-valid {
  width: calc(3rem + calc(1.5em + 0.75rem));
}

.was-validated .form-check-input:valid, .form-check-input.is-valid {
  border-color: #41cc68;
}
.was-validated .form-check-input:valid:checked, .form-check-input.is-valid:checked {
  background-color: #41cc68;
}
.was-validated .form-check-input:valid:focus, .form-check-input.is-valid:focus {
  box-shadow: 0 0 0 0.25rem rgba(65, 204, 104, 0.25);
}
.was-validated .form-check-input:valid ~ .form-check-label, .form-check-input.is-valid ~ .form-check-label {
  color: #41cc68;
}

.form-check-inline .form-check-input ~ .valid-feedback {
  margin-left: 0.5em;
}

.was-validated .input-group > .form-control:not(:focus):valid, .input-group > .form-control:not(:focus).is-valid,
.was-validated .input-group > .form-select:not(:focus):valid,
.input-group > .form-select:not(:focus).is-valid,
.was-validated .input-group > .form-floating:not(:focus-within):valid,
.input-group > .form-floating:not(:focus-within).is-valid {
  z-index: 3;
}

.invalid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875em;
  color: #ff2424;
}

.invalid-tooltip {
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.25rem 0.5rem;
  margin-top: 0.1rem;
  font-size: 0.875rem;
  color: #000000;
  background-color: rgba(255, 36, 36, 0.9);
  border-radius: 0.375rem;
}

.was-validated :invalid ~ .invalid-feedback,
.was-validated :invalid ~ .invalid-tooltip,
.is-invalid ~ .invalid-feedback,
.is-invalid ~ .invalid-tooltip {
  display: block;
}

.was-validated .form-control:invalid, .form-control.is-invalid {
  border-color: #ff2424;
  padding-right: calc(1.5em + 0.75rem);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23ff2424'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23ff2424' stroke='none'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}
.was-validated .form-control:invalid:focus, .form-control.is-invalid:focus {
  border-color: #ff2424;
  box-shadow: 0 0 0 0.25rem rgba(255, 36, 36, 0.25);
}

.was-validated textarea.form-control:invalid, textarea.form-control.is-invalid {
  padding-right: calc(1.5em + 0.75rem);
  background-position: top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem);
}

.was-validated .form-select:invalid, .form-select.is-invalid {
  border-color: #ff2424;
}
.was-validated .form-select:invalid:not([multiple]):not([size]), .was-validated .form-select:invalid:not([multiple])[size="1"], .form-select.is-invalid:not([multiple]):not([size]), .form-select.is-invalid:not([multiple])[size="1"] {
  padding-right: 4.125rem;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e"), url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23ff2424'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23ff2424' stroke='none'/%3e%3c/svg%3e");
  background-position: right 0.75rem center, center right 2.25rem;
  background-size: 16px 12px, calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}
.was-validated .form-select:invalid:focus, .form-select.is-invalid:focus {
  border-color: #ff2424;
  box-shadow: 0 0 0 0.25rem rgba(255, 36, 36, 0.25);
}

.was-validated .form-control-color:invalid, .form-control-color.is-invalid {
  width: calc(3rem + calc(1.5em + 0.75rem));
}

.was-validated .form-check-input:invalid, .form-check-input.is-invalid {
  border-color: #ff2424;
}
.was-validated .form-check-input:invalid:checked, .form-check-input.is-invalid:checked {
  background-color: #ff2424;
}
.was-validated .form-check-input:invalid:focus, .form-check-input.is-invalid:focus {
  box-shadow: 0 0 0 0.25rem rgba(255, 36, 36, 0.25);
}
.was-validated .form-check-input:invalid ~ .form-check-label, .form-check-input.is-invalid ~ .form-check-label {
  color: #ff2424;
}

.form-check-inline .form-check-input ~ .invalid-feedback {
  margin-left: 0.5em;
}

.was-validated .input-group > .form-control:not(:focus):invalid, .input-group > .form-control:not(:focus).is-invalid,
.was-validated .input-group > .form-select:not(:focus):invalid,
.input-group > .form-select:not(:focus).is-invalid,
.was-validated .input-group > .form-floating:not(:focus-within):invalid,
.input-group > .form-floating:not(:focus-within).is-invalid {
  z-index: 4;
}

.btn {
  --bs-btn-padding-x: 20px;
  --bs-btn-padding-y: 10px;
  --bs-btn-font-family: ;
  --bs-btn-font-size: 0.875rem;
  --bs-btn-font-weight: 400;
  --bs-btn-line-height: 1.5;
  --bs-btn-color: #212529;
  --bs-btn-bg: transparent;
  --bs-btn-border-width: 1px;
  --bs-btn-border-color: transparent;
  --bs-btn-border-radius: 0.375rem;
  --bs-btn-hover-border-color: transparent;
  --bs-btn-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
  --bs-btn-disabled-opacity: 0.65;
  --bs-btn-focus-box-shadow: 0 0 0 0.25rem rgba(var(--bs-btn-focus-shadow-rgb), .5);
  display: inline-block;
  padding: var(--bs-btn-padding-y) var(--bs-btn-padding-x);
  font-family: var(--bs-btn-font-family);
  font-size: var(--bs-btn-font-size);
  font-weight: var(--bs-btn-font-weight);
  line-height: var(--bs-btn-line-height);
  color: var(--bs-btn-color);
  text-align: center;
  text-decoration: none;
  vertical-align: middle;
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  border: var(--bs-btn-border-width) solid var(--bs-btn-border-color);
  border-radius: var(--bs-btn-border-radius);
  background-color: var(--bs-btn-bg);
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .btn {
    transition: none;
  }
}
.btn:hover {
  color: var(--bs-btn-hover-color);
  background-color: var(--bs-btn-hover-bg);
  border-color: var(--bs-btn-hover-border-color);
}
.btn-check + .btn:hover {
  color: var(--bs-btn-color);
  background-color: var(--bs-btn-bg);
  border-color: var(--bs-btn-border-color);
}
.btn:focus-visible {
  color: var(--bs-btn-hover-color);
  background-color: var(--bs-btn-hover-bg);
  border-color: var(--bs-btn-hover-border-color);
  outline: 0;
  box-shadow: var(--bs-btn-focus-box-shadow);
}
.btn-check:focus-visible + .btn {
  border-color: var(--bs-btn-hover-border-color);
  outline: 0;
  box-shadow: var(--bs-btn-focus-box-shadow);
}
.btn-check:checked + .btn, :not(.btn-check) + .btn:active, .btn:first-child:active, .btn.active, .btn.show {
  color: var(--bs-btn-active-color);
  background-color: var(--bs-btn-active-bg);
  border-color: var(--bs-btn-active-border-color);
}
.btn-check:checked + .btn:focus-visible, :not(.btn-check) + .btn:active:focus-visible, .btn:first-child:active:focus-visible, .btn.active:focus-visible, .btn.show:focus-visible {
  box-shadow: var(--bs-btn-focus-box-shadow);
}
.btn:disabled, .btn.disabled, fieldset:disabled .btn {
  color: var(--bs-btn-disabled-color);
  pointer-events: none;
  background-color: var(--bs-btn-disabled-bg);
  border-color: var(--bs-btn-disabled-border-color);
  opacity: var(--bs-btn-disabled-opacity);
}

.btn-primary {
  --bs-btn-color: #ffffff;
  --bs-btn-bg: #2fabf7;
  --bs-btn-border-color: #2fabf7;
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: #3646c1;
  --bs-btn-hover-border-color: #3242b6;
  --bs-btn-focus-shadow-rgb: 92, 108, 231;
  --bs-btn-active-color: #ffffff;
  --bs-btn-active-bg: #3242b6;
  --bs-btn-active-border-color: #2f3eaa;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #ffffff;
  --bs-btn-disabled-bg: #2fabf7;
  --bs-btn-disabled-border-color: #2fabf7;
}

.btn-secondary {
  --bs-btn-color: #ffffff;
  --bs-btn-bg: #434343;
  --bs-btn-border-color: #434343;
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: #393939;
  --bs-btn-hover-border-color: #363636;
  --bs-btn-focus-shadow-rgb: 95, 95, 95;
  --bs-btn-active-color: #ffffff;
  --bs-btn-active-bg: #363636;
  --bs-btn-active-border-color: #323232;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #ffffff;
  --bs-btn-disabled-bg: #434343;
  --bs-btn-disabled-border-color: #434343;
}

.btn-success {
  --bs-btn-color: #000000;
  --bs-btn-bg: #41cc68;
  --bs-btn-border-color: #41cc68;
  --bs-btn-hover-color: #000000;
  --bs-btn-hover-bg: #5ed47f;
  --bs-btn-hover-border-color: #54d177;
  --bs-btn-focus-shadow-rgb: 55, 173, 88;
  --bs-btn-active-color: #000000;
  --bs-btn-active-bg: #67d686;
  --bs-btn-active-border-color: #54d177;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000000;
  --bs-btn-disabled-bg: #41cc68;
  --bs-btn-disabled-border-color: #41cc68;
}

.btn-info {
  --bs-btn-color: #000000;
  --bs-btn-bg: #02a2b9;
  --bs-btn-border-color: #02a2b9;
  --bs-btn-hover-color: #000000;
  --bs-btn-hover-bg: #28b0c4;
  --bs-btn-hover-border-color: #1babc0;
  --bs-btn-focus-shadow-rgb: 2, 138, 157;
  --bs-btn-active-color: #000000;
  --bs-btn-active-bg: #35b5c7;
  --bs-btn-active-border-color: #1babc0;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000000;
  --bs-btn-disabled-bg: #02a2b9;
  --bs-btn-disabled-border-color: #02a2b9;
}

.btn-warning {
  --bs-btn-color: #000000;
  --bs-btn-bg: #ffbb33;
  --bs-btn-border-color: #ffbb33;
  --bs-btn-hover-color: #000000;
  --bs-btn-hover-bg: #ffc552;
  --bs-btn-hover-border-color: #ffc247;
  --bs-btn-focus-shadow-rgb: 217, 159, 43;
  --bs-btn-active-color: #000000;
  --bs-btn-active-bg: #ffc95c;
  --bs-btn-active-border-color: #ffc247;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000000;
  --bs-btn-disabled-bg: #ffbb33;
  --bs-btn-disabled-border-color: #ffbb33;
}

.btn-danger {
  --bs-btn-color: #000000;
  --bs-btn-bg: #ff2424;
  --bs-btn-border-color: #ff2424;
  --bs-btn-hover-color: #000000;
  --bs-btn-hover-bg: #ff4545;
  --bs-btn-hover-border-color: #ff3a3a;
  --bs-btn-focus-shadow-rgb: 217, 31, 31;
  --bs-btn-active-color: #000000;
  --bs-btn-active-bg: #ff5050;
  --bs-btn-active-border-color: #ff3a3a;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000000;
  --bs-btn-disabled-bg: #ff2424;
  --bs-btn-disabled-border-color: #ff2424;
}

.btn-light {
  --bs-btn-color: #000000;
  --bs-btn-bg: #ffffff;
  --bs-btn-border-color: #ffffff;
  --bs-btn-hover-color: #000000;
  --bs-btn-hover-bg: #d9d9d9;
  --bs-btn-hover-border-color: #cccccc;
  --bs-btn-focus-shadow-rgb: 217, 217, 217;
  --bs-btn-active-color: #000000;
  --bs-btn-active-bg: #cccccc;
  --bs-btn-active-border-color: #bfbfbf;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000000;
  --bs-btn-disabled-bg: #ffffff;
  --bs-btn-disabled-border-color: #ffffff;
}

.btn-dark {
  --bs-btn-color: #ffffff;
  --bs-btn-bg: #111120;
  --bs-btn-border-color: #111120;
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: #353541;
  --bs-btn-hover-border-color: #292936;
  --bs-btn-focus-shadow-rgb: 53, 53, 65;
  --bs-btn-active-color: #ffffff;
  --bs-btn-active-bg: #41414d;
  --bs-btn-active-border-color: #292936;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #ffffff;
  --bs-btn-disabled-bg: #111120;
  --bs-btn-disabled-border-color: #111120;
}

.btn-gray-ligth {
  --bs-btn-color: #000000;
  --bs-btn-bg: #d7dde9;
  --bs-btn-border-color: #d7dde9;
  --bs-btn-hover-color: #000000;
  --bs-btn-hover-bg: #dde2ec;
  --bs-btn-hover-border-color: #dbe0eb;
  --bs-btn-focus-shadow-rgb: 183, 188, 198;
  --bs-btn-active-color: #000000;
  --bs-btn-active-bg: #dfe4ed;
  --bs-btn-active-border-color: #dbe0eb;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000000;
  --bs-btn-disabled-bg: #d7dde9;
  --bs-btn-disabled-border-color: #d7dde9;
}

.btn-outline-primary {
  --bs-btn-color: #2fabf7;
  --bs-btn-border-color: #2fabf7;
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: #2fabf7;
  --bs-btn-hover-border-color: #2fabf7;
  --bs-btn-focus-shadow-rgb: 63, 82, 227;
  --bs-btn-active-color: #ffffff;
  --bs-btn-active-bg: #2fabf7;
  --bs-btn-active-border-color: #2fabf7;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #2fabf7;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #2fabf7;
  --bs-gradient: none;
}

.btn-outline-secondary {
  --bs-btn-color: #434343;
  --bs-btn-border-color: #434343;
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: #434343;
  --bs-btn-hover-border-color: #434343;
  --bs-btn-focus-shadow-rgb: 67, 67, 67;
  --bs-btn-active-color: #ffffff;
  --bs-btn-active-bg: #434343;
  --bs-btn-active-border-color: #434343;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #434343;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #434343;
  --bs-gradient: none;
}

.btn-outline-success {
  --bs-btn-color: #41cc68;
  --bs-btn-border-color: #41cc68;
  --bs-btn-hover-color: #000000;
  --bs-btn-hover-bg: #41cc68;
  --bs-btn-hover-border-color: #41cc68;
  --bs-btn-focus-shadow-rgb: 65, 204, 104;
  --bs-btn-active-color: #000000;
  --bs-btn-active-bg: #41cc68;
  --bs-btn-active-border-color: #41cc68;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #41cc68;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #41cc68;
  --bs-gradient: none;
}

.btn-outline-info {
  --bs-btn-color: #02a2b9;
  --bs-btn-border-color: #02a2b9;
  --bs-btn-hover-color: #000000;
  --bs-btn-hover-bg: #02a2b9;
  --bs-btn-hover-border-color: #02a2b9;
  --bs-btn-focus-shadow-rgb: 2, 162, 185;
  --bs-btn-active-color: #000000;
  --bs-btn-active-bg: #02a2b9;
  --bs-btn-active-border-color: #02a2b9;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #02a2b9;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #02a2b9;
  --bs-gradient: none;
}

.btn-outline-warning {
  --bs-btn-color: #ffbb33;
  --bs-btn-border-color: #ffbb33;
  --bs-btn-hover-color: #000000;
  --bs-btn-hover-bg: #ffbb33;
  --bs-btn-hover-border-color: #ffbb33;
  --bs-btn-focus-shadow-rgb: 255, 187, 51;
  --bs-btn-active-color: #000000;
  --bs-btn-active-bg: #ffbb33;
  --bs-btn-active-border-color: #ffbb33;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #ffbb33;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #ffbb33;
  --bs-gradient: none;
}

.btn-outline-danger {
  --bs-btn-color: #ff2424;
  --bs-btn-border-color: #ff2424;
  --bs-btn-hover-color: #000000;
  --bs-btn-hover-bg: #ff2424;
  --bs-btn-hover-border-color: #ff2424;
  --bs-btn-focus-shadow-rgb: 255, 36, 36;
  --bs-btn-active-color: #000000;
  --bs-btn-active-bg: #ff2424;
  --bs-btn-active-border-color: #ff2424;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #ff2424;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #ff2424;
  --bs-gradient: none;
}

.btn-outline-light {
  --bs-btn-color: #ffffff;
  --bs-btn-border-color: #ffffff;
  --bs-btn-hover-color: #000000;
  --bs-btn-hover-bg: #ffffff;
  --bs-btn-hover-border-color: #ffffff;
  --bs-btn-focus-shadow-rgb: 255, 255, 255;
  --bs-btn-active-color: #000000;
  --bs-btn-active-bg: #ffffff;
  --bs-btn-active-border-color: #ffffff;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #ffffff;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #ffffff;
  --bs-gradient: none;
}

.btn-outline-dark {
  --bs-btn-color: #111120;
  --bs-btn-border-color: #111120;
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: #111120;
  --bs-btn-hover-border-color: #111120;
  --bs-btn-focus-shadow-rgb: 17, 17, 32;
  --bs-btn-active-color: #ffffff;
  --bs-btn-active-bg: #111120;
  --bs-btn-active-border-color: #111120;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #111120;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #111120;
  --bs-gradient: none;
}

.btn-outline-gray-ligth {
  --bs-btn-color: #d7dde9;
  --bs-btn-border-color: #d7dde9;
  --bs-btn-hover-color: #000000;
  --bs-btn-hover-bg: #d7dde9;
  --bs-btn-hover-border-color: #d7dde9;
  --bs-btn-focus-shadow-rgb: 215, 221, 233;
  --bs-btn-active-color: #000000;
  --bs-btn-active-bg: #d7dde9;
  --bs-btn-active-border-color: #d7dde9;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #d7dde9;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #d7dde9;
  --bs-gradient: none;
}

.btn-link {
  --bs-btn-font-weight: 400;
  --bs-btn-color: var(--bs-link-color);
  --bs-btn-bg: transparent;
  --bs-btn-border-color: transparent;
  --bs-btn-hover-color: var(--bs-link-hover-color);
  --bs-btn-hover-border-color: transparent;
  --bs-btn-active-color: var(--bs-link-hover-color);
  --bs-btn-active-border-color: transparent;
  --bs-btn-disabled-color: #6c757d;
  --bs-btn-disabled-border-color: transparent;
  --bs-btn-box-shadow: none;
  --bs-btn-focus-shadow-rgb: 92, 108, 231;
  text-decoration: underline;
}
.btn-link:focus-visible {
  color: var(--bs-btn-color);
}
.btn-link:hover {
  color: var(--bs-btn-hover-color);
}

.btn-lg, .btn-group-lg > .btn {
  --bs-btn-padding-y: 13px;
  --bs-btn-padding-x: 30px;
  --bs-btn-font-size: 1rem;
  --bs-btn-border-radius: 0.5rem;
}

.btn-sm, .btn-group-sm > .btn {
  --bs-btn-padding-y: 5px;
  --bs-btn-padding-x: 15px;
  --bs-btn-font-size: 0.875rem;
  --bs-btn-border-radius: 0.25rem;
}

.fade {
  transition: opacity 0.15s linear;
}
@media (prefers-reduced-motion: reduce) {
  .fade {
    transition: none;
  }
}
.fade:not(.show) {
  opacity: 0;
}

.collapse:not(.show) {
  display: none;
}

.collapsing {
  height: 0;
  overflow: hidden;
  transition: height 0.35s ease;
}
@media (prefers-reduced-motion: reduce) {
  .collapsing {
    transition: none;
  }
}
.collapsing.collapse-horizontal {
  width: 0;
  height: auto;
  transition: width 0.35s ease;
}
@media (prefers-reduced-motion: reduce) {
  .collapsing.collapse-horizontal {
    transition: none;
  }
}

.dropup,
.dropend,
.dropdown,
.dropstart,
.dropup-center,
.dropdown-center {
  position: relative;
}

.dropdown-toggle {
  white-space: nowrap;
}
.dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid;
  border-right: 0.3em solid transparent;
  border-bottom: 0;
  border-left: 0.3em solid transparent;
}
.dropdown-toggle:empty::after {
  margin-left: 0;
}

.dropdown-menu {
  --bs-dropdown-zindex: 1000;
  --bs-dropdown-min-width: 10rem;
  --bs-dropdown-padding-x: 0;
  --bs-dropdown-padding-y: 0.5rem;
  --bs-dropdown-spacer: 0.125rem;
  --bs-dropdown-font-size: 1rem;
  --bs-dropdown-color: #212529;
  --bs-dropdown-bg: #ffffff;
  --bs-dropdown-border-color: var(--bs-border-color-translucent);
  --bs-dropdown-border-radius: 0.375rem;
  --bs-dropdown-border-width: 1px;
  --bs-dropdown-inner-border-radius: calc(0.375rem - 1px);
  --bs-dropdown-divider-bg: var(--bs-border-color-translucent);
  --bs-dropdown-divider-margin-y: 0.5rem;
  --bs-dropdown-box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  --bs-dropdown-link-color: #212529;
  --bs-dropdown-link-hover-color: #1e2125;
  --bs-dropdown-link-hover-bg: #e9ecef;
  --bs-dropdown-link-active-color: #ffffff;
  --bs-dropdown-link-active-bg: #3f52e3;
  --bs-dropdown-link-disabled-color: #adb5bd;
  --bs-dropdown-item-padding-x: 1rem;
  --bs-dropdown-item-padding-y: 0.25rem;
  --bs-dropdown-header-color: #6c757d;
  --bs-dropdown-header-padding-x: 1rem;
  --bs-dropdown-header-padding-y: 0.5rem;
  position: absolute;
  z-index: var(--bs-dropdown-zindex);
  display: none;
  min-width: var(--bs-dropdown-min-width);
  padding: var(--bs-dropdown-padding-y) var(--bs-dropdown-padding-x);
  margin: 0;
  font-size: var(--bs-dropdown-font-size);
  color: var(--bs-dropdown-color);
  text-align: left;
  list-style: none;
  background-color: var(--bs-dropdown-bg);
  background-clip: padding-box;
  border: var(--bs-dropdown-border-width) solid var(--bs-dropdown-border-color);
  border-radius: var(--bs-dropdown-border-radius);
}
.dropdown-menu[data-bs-popper] {
  top: 100%;
  left: 0;
  margin-top: var(--bs-dropdown-spacer);
}

.dropdown-menu-start {
  --bs-position: start;
}
.dropdown-menu-start[data-bs-popper] {
  right: auto;
  left: 0;
}

.dropdown-menu-end {
  --bs-position: end;
}
.dropdown-menu-end[data-bs-popper] {
  right: 0;
  left: auto;
}

@media (min-width: 576px) {
  .dropdown-menu-sm-start {
    --bs-position: start;
  }
  .dropdown-menu-sm-start[data-bs-popper] {
    right: auto;
    left: 0;
  }
  .dropdown-menu-sm-end {
    --bs-position: end;
  }
  .dropdown-menu-sm-end[data-bs-popper] {
    right: 0;
    left: auto;
  }
}
@media (min-width: 768px) {
  .dropdown-menu-md-start {
    --bs-position: start;
  }
  .dropdown-menu-md-start[data-bs-popper] {
    right: auto;
    left: 0;
  }
  .dropdown-menu-md-end {
    --bs-position: end;
  }
  .dropdown-menu-md-end[data-bs-popper] {
    right: 0;
    left: auto;
  }
}
@media (min-width: 992px) {
  .dropdown-menu-lg-start {
    --bs-position: start;
  }
  .dropdown-menu-lg-start[data-bs-popper] {
    right: auto;
    left: 0;
  }
  .dropdown-menu-lg-end {
    --bs-position: end;
  }
  .dropdown-menu-lg-end[data-bs-popper] {
    right: 0;
    left: auto;
  }
}
@media (min-width: 1200px) {
  .dropdown-menu-xl-start {
    --bs-position: start;
  }
  .dropdown-menu-xl-start[data-bs-popper] {
    right: auto;
    left: 0;
  }
  .dropdown-menu-xl-end {
    --bs-position: end;
  }
  .dropdown-menu-xl-end[data-bs-popper] {
    right: 0;
    left: auto;
  }
}
@media (min-width: 1400px) {
  .dropdown-menu-xxl-start {
    --bs-position: start;
  }
  .dropdown-menu-xxl-start[data-bs-popper] {
    right: auto;
    left: 0;
  }
  .dropdown-menu-xxl-end {
    --bs-position: end;
  }
  .dropdown-menu-xxl-end[data-bs-popper] {
    right: 0;
    left: auto;
  }
}
.dropup .dropdown-menu[data-bs-popper] {
  top: auto;
  bottom: 100%;
  margin-top: 0;
  margin-bottom: var(--bs-dropdown-spacer);
}
.dropup .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0;
  border-right: 0.3em solid transparent;
  border-bottom: 0.3em solid;
  border-left: 0.3em solid transparent;
}
.dropup .dropdown-toggle:empty::after {
  margin-left: 0;
}

.dropend .dropdown-menu[data-bs-popper] {
  top: 0;
  right: auto;
  left: 100%;
  margin-top: 0;
  margin-left: var(--bs-dropdown-spacer);
}
.dropend .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid transparent;
  border-right: 0;
  border-bottom: 0.3em solid transparent;
  border-left: 0.3em solid;
}
.dropend .dropdown-toggle:empty::after {
  margin-left: 0;
}
.dropend .dropdown-toggle::after {
  vertical-align: 0;
}

.dropstart .dropdown-menu[data-bs-popper] {
  top: 0;
  right: 100%;
  left: auto;
  margin-top: 0;
  margin-right: var(--bs-dropdown-spacer);
}
.dropstart .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
}
.dropstart .dropdown-toggle::after {
  display: none;
}
.dropstart .dropdown-toggle::before {
  display: inline-block;
  margin-right: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid transparent;
  border-right: 0.3em solid;
  border-bottom: 0.3em solid transparent;
}
.dropstart .dropdown-toggle:empty::after {
  margin-left: 0;
}
.dropstart .dropdown-toggle::before {
  vertical-align: 0;
}

.dropdown-divider {
  height: 0;
  margin: var(--bs-dropdown-divider-margin-y) 0;
  overflow: hidden;
  border-top: 1px solid var(--bs-dropdown-divider-bg);
  opacity: 1;
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: var(--bs-dropdown-item-padding-y) var(--bs-dropdown-item-padding-x);
  clear: both;
  font-weight: 400;
  color: var(--bs-dropdown-link-color);
  text-align: inherit;
  text-decoration: none;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
}
.dropdown-item:hover, .dropdown-item:focus {
  color: var(--bs-dropdown-link-hover-color);
  background-color: var(--bs-dropdown-link-hover-bg);
}
.dropdown-item.active, .dropdown-item:active {
  color: var(--bs-dropdown-link-active-color);
  text-decoration: none;
  background-color: var(--bs-dropdown-link-active-bg);
}
.dropdown-item.disabled, .dropdown-item:disabled {
  color: var(--bs-dropdown-link-disabled-color);
  pointer-events: none;
  background-color: transparent;
}

.dropdown-menu.show {
  display: block;
}

.dropdown-header {
  display: block;
  padding: var(--bs-dropdown-header-padding-y) var(--bs-dropdown-header-padding-x);
  margin-bottom: 0;
  font-size: 0.875rem;
  color: var(--bs-dropdown-header-color);
  white-space: nowrap;
}

.dropdown-item-text {
  display: block;
  padding: var(--bs-dropdown-item-padding-y) var(--bs-dropdown-item-padding-x);
  color: var(--bs-dropdown-link-color);
}

.dropdown-menu-dark {
  --bs-dropdown-color: #dee2e6;
  --bs-dropdown-bg: #343a40;
  --bs-dropdown-border-color: var(--bs-border-color-translucent);
  --bs-dropdown-box-shadow: ;
  --bs-dropdown-link-color: #dee2e6;
  --bs-dropdown-link-hover-color: #ffffff;
  --bs-dropdown-divider-bg: var(--bs-border-color-translucent);
  --bs-dropdown-link-hover-bg: rgba(255, 255, 255, 0.15);
  --bs-dropdown-link-active-color: #ffffff;
  --bs-dropdown-link-active-bg: #3f52e3;
  --bs-dropdown-link-disabled-color: #adb5bd;
  --bs-dropdown-header-color: #adb5bd;
}

.btn-group,
.btn-group-vertical {
  position: relative;
  display: inline-flex;
  vertical-align: middle;
}
.btn-group > .btn,
.btn-group-vertical > .btn {
  position: relative;
  flex: 1 1 auto;
}
.btn-group > .btn-check:checked + .btn,
.btn-group > .btn-check:focus + .btn,
.btn-group > .btn:hover,
.btn-group > .btn:focus,
.btn-group > .btn:active,
.btn-group > .btn.active,
.btn-group-vertical > .btn-check:checked + .btn,
.btn-group-vertical > .btn-check:focus + .btn,
.btn-group-vertical > .btn:hover,
.btn-group-vertical > .btn:focus,
.btn-group-vertical > .btn:active,
.btn-group-vertical > .btn.active {
  z-index: 1;
}

.btn-toolbar {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}
.btn-toolbar .input-group {
  width: auto;
}

.btn-group {
  border-radius: 0.375rem;
}
.btn-group > :not(.btn-check:first-child) + .btn,
.btn-group > .btn-group:not(:first-child) {
  margin-left: -1px;
}
.btn-group > .btn:not(:last-child):not(.dropdown-toggle),
.btn-group > .btn.dropdown-toggle-split:first-child,
.btn-group > .btn-group:not(:last-child) > .btn {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.btn-group > .btn:nth-child(n+3),
.btn-group > :not(.btn-check) + .btn,
.btn-group > .btn-group:not(:first-child) > .btn {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.dropdown-toggle-split {
  padding-right: 15px;
  padding-left: 15px;
}
.dropdown-toggle-split::after, .dropup .dropdown-toggle-split::after, .dropend .dropdown-toggle-split::after {
  margin-left: 0;
}
.dropstart .dropdown-toggle-split::before {
  margin-right: 0;
}

.btn-sm + .dropdown-toggle-split, .btn-group-sm > .btn + .dropdown-toggle-split {
  padding-right: 11.25px;
  padding-left: 11.25px;
}

.btn-lg + .dropdown-toggle-split, .btn-group-lg > .btn + .dropdown-toggle-split {
  padding-right: 22.5px;
  padding-left: 22.5px;
}

.btn-group-vertical {
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
}
.btn-group-vertical > .btn,
.btn-group-vertical > .btn-group {
  width: 100%;
}
.btn-group-vertical > .btn:not(:first-child),
.btn-group-vertical > .btn-group:not(:first-child) {
  margin-top: -1px;
}
.btn-group-vertical > .btn:not(:last-child):not(.dropdown-toggle),
.btn-group-vertical > .btn-group:not(:last-child) > .btn {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.btn-group-vertical > .btn ~ .btn,
.btn-group-vertical > .btn-group:not(:first-child) > .btn {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.nav {
  --bs-nav-link-padding-x: 1rem;
  --bs-nav-link-padding-y: 0.5rem;
  --bs-nav-link-font-weight: ;
  --bs-nav-link-color: var(--bs-link-color);
  --bs-nav-link-hover-color: var(--bs-link-hover-color);
  --bs-nav-link-disabled-color: #6c757d;
  display: flex;
  flex-wrap: wrap;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}

.nav-link {
  display: block;
  padding: var(--bs-nav-link-padding-y) var(--bs-nav-link-padding-x);
  font-size: var(--bs-nav-link-font-size);
  font-weight: var(--bs-nav-link-font-weight);
  color: var(--bs-nav-link-color);
  text-decoration: none;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .nav-link {
    transition: none;
  }
}
.nav-link:hover, .nav-link:focus {
  color: var(--bs-nav-link-hover-color);
}
.nav-link.disabled {
  color: var(--bs-nav-link-disabled-color);
  pointer-events: none;
  cursor: default;
}

.nav-tabs {
  --bs-nav-tabs-border-width: 1px;
  --bs-nav-tabs-border-color: #dee2e6;
  --bs-nav-tabs-border-radius: 0.375rem;
  --bs-nav-tabs-link-hover-border-color: #e9ecef #e9ecef #dee2e6;
  --bs-nav-tabs-link-active-color: #495057;
  --bs-nav-tabs-link-active-bg: #ffffff;
  --bs-nav-tabs-link-active-border-color: #dee2e6 #dee2e6 #ffffff;
  border-bottom: var(--bs-nav-tabs-border-width) solid var(--bs-nav-tabs-border-color);
}
.nav-tabs .nav-link {
  margin-bottom: calc(-1 * var(--bs-nav-tabs-border-width));
  background: none;
  border: var(--bs-nav-tabs-border-width) solid transparent;
  border-top-left-radius: var(--bs-nav-tabs-border-radius);
  border-top-right-radius: var(--bs-nav-tabs-border-radius);
}
.nav-tabs .nav-link:hover, .nav-tabs .nav-link:focus {
  isolation: isolate;
  border-color: var(--bs-nav-tabs-link-hover-border-color);
}
.nav-tabs .nav-link.disabled, .nav-tabs .nav-link:disabled {
  color: var(--bs-nav-link-disabled-color);
  background-color: transparent;
  border-color: transparent;
}
.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
  color: var(--bs-nav-tabs-link-active-color);
  background-color: var(--bs-nav-tabs-link-active-bg);
  border-color: var(--bs-nav-tabs-link-active-border-color);
}
.nav-tabs .dropdown-menu {
  margin-top: calc(-1 * var(--bs-nav-tabs-border-width));
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.nav-pills {
  --bs-nav-pills-border-radius: 0.375rem;
  --bs-nav-pills-link-active-color: #ffffff;
  --bs-nav-pills-link-active-bg: #3f52e3;
}
.nav-pills .nav-link {
  background: none;
  border: 0;
  border-radius: var(--bs-nav-pills-border-radius);
}
.nav-pills .nav-link:disabled {
  color: var(--bs-nav-link-disabled-color);
  background-color: transparent;
  border-color: transparent;
}
.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  color: var(--bs-nav-pills-link-active-color);
  background-color: var(--bs-nav-pills-link-active-bg);
}

.nav-fill > .nav-link,
.nav-fill .nav-item {
  flex: 1 1 auto;
  text-align: center;
}

.nav-justified > .nav-link,
.nav-justified .nav-item {
  flex-basis: 0;
  flex-grow: 1;
  text-align: center;
}

.nav-fill .nav-item .nav-link,
.nav-justified .nav-item .nav-link {
  width: 100%;
}

.tab-content > .tab-pane {
  display: none;
}
.tab-content > .active {
  display: block;
}

.navbar {
  --bs-navbar-padding-x: 0;
  --bs-navbar-padding-y: 0.5rem;
  --bs-navbar-color: rgba(0, 0, 0, 0.55);
  --bs-navbar-hover-color: rgba(0, 0, 0, 0.7);
  --bs-navbar-disabled-color: rgba(0, 0, 0, 0.3);
  --bs-navbar-active-color: rgba(0, 0, 0, 0.9);
  --bs-navbar-brand-padding-y: 0.3125rem;
  --bs-navbar-brand-margin-end: 1rem;
  --bs-navbar-brand-font-size: 1.25rem;
  --bs-navbar-brand-color: rgba(0, 0, 0, 0.9);
  --bs-navbar-brand-hover-color: rgba(0, 0, 0, 0.9);
  --bs-navbar-nav-link-padding-x: 0.5rem;
  --bs-navbar-toggler-padding-y: 0.25rem;
  --bs-navbar-toggler-padding-x: 0.75rem;
  --bs-navbar-toggler-font-size: 1.25rem;
  --bs-navbar-toggler-icon-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%280, 0, 0, 0.55%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
  --bs-navbar-toggler-border-color: rgba(0, 0, 0, 0.1);
  --bs-navbar-toggler-border-radius: 0.375rem;
  --bs-navbar-toggler-focus-width: 0.25rem;
  --bs-navbar-toggler-transition: box-shadow 0.15s ease-in-out;
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  padding: var(--bs-navbar-padding-y) var(--bs-navbar-padding-x);
}
.navbar > .container,
.navbar > .container-fluid,
.navbar > .container-sm,
.navbar > .container-md,
.navbar > .container-lg,
.navbar > .container-xl,
.navbar > .container-xxl,
.navbar > .container-xxxl {
  display: flex;
  flex-wrap: inherit;
  align-items: center;
  justify-content: space-between;
}
.navbar-brand {
  padding-top: var(--bs-navbar-brand-padding-y);
  padding-bottom: var(--bs-navbar-brand-padding-y);
  margin-right: var(--bs-navbar-brand-margin-end);
  font-size: var(--bs-navbar-brand-font-size);
  color: var(--bs-navbar-brand-color);
  text-decoration: none;
  white-space: nowrap;
}
.navbar-brand:hover, .navbar-brand:focus {
  color: var(--bs-navbar-brand-hover-color);
}

.navbar-nav {
  --bs-nav-link-padding-x: 0;
  --bs-nav-link-padding-y: 0.5rem;
  --bs-nav-link-font-weight: ;
  --bs-nav-link-color: var(--bs-navbar-color);
  --bs-nav-link-hover-color: var(--bs-navbar-hover-color);
  --bs-nav-link-disabled-color: var(--bs-navbar-disabled-color);
  display: flex;
  flex-direction: column;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}
.navbar-nav .show > .nav-link,
.navbar-nav .nav-link.active {
  color: var(--bs-navbar-active-color);
}
.navbar-nav .dropdown-menu {
  position: static;
}

.navbar-text {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  color: var(--bs-navbar-color);
}
.navbar-text a,
.navbar-text a:hover,
.navbar-text a:focus {
  color: var(--bs-navbar-active-color);
}

.navbar-collapse {
  flex-basis: 100%;
  flex-grow: 1;
  align-items: center;
}

.navbar-toggler {
  padding: var(--bs-navbar-toggler-padding-y) var(--bs-navbar-toggler-padding-x);
  font-size: var(--bs-navbar-toggler-font-size);
  line-height: 1;
  color: var(--bs-navbar-color);
  background-color: transparent;
  border: var(--bs-border-width) solid var(--bs-navbar-toggler-border-color);
  border-radius: var(--bs-navbar-toggler-border-radius);
  transition: var(--bs-navbar-toggler-transition);
}
@media (prefers-reduced-motion: reduce) {
  .navbar-toggler {
    transition: none;
  }
}
.navbar-toggler:hover {
  text-decoration: none;
}
.navbar-toggler:focus {
  text-decoration: none;
  outline: 0;
  box-shadow: 0 0 0 var(--bs-navbar-toggler-focus-width);
}

.navbar-toggler-icon {
  display: inline-block;
  width: 1.5em;
  height: 1.5em;
  vertical-align: middle;
  background-image: var(--bs-navbar-toggler-icon-bg);
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100%;
}

.navbar-nav-scroll {
  max-height: var(--bs-scroll-height, 75vh);
  overflow-y: auto;
}

@media (min-width: 576px) {
  .navbar-expand-sm {
    flex-wrap: nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-sm .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-sm .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-sm .navbar-nav .nav-link {
    padding-right: var(--bs-navbar-nav-link-padding-x);
    padding-left: var(--bs-navbar-nav-link-padding-x);
  }
  .navbar-expand-sm .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-sm .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-sm .navbar-toggler {
    display: none;
  }
  .navbar-expand-sm .offcanvas {
    position: static;
    z-index: auto;
    flex-grow: 1;
    width: auto !important;
    height: auto !important;
    visibility: visible !important;
    background-color: transparent !important;
    border: 0 !important;
    transform: none !important;
    transition: none;
  }
  .navbar-expand-sm .offcanvas .offcanvas-header {
    display: none;
  }
  .navbar-expand-sm .offcanvas .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
}
@media (min-width: 768px) {
  .navbar-expand-md {
    flex-wrap: nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-md .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-md .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-md .navbar-nav .nav-link {
    padding-right: var(--bs-navbar-nav-link-padding-x);
    padding-left: var(--bs-navbar-nav-link-padding-x);
  }
  .navbar-expand-md .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-md .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-md .navbar-toggler {
    display: none;
  }
  .navbar-expand-md .offcanvas {
    position: static;
    z-index: auto;
    flex-grow: 1;
    width: auto !important;
    height: auto !important;
    visibility: visible !important;
    background-color: transparent !important;
    border: 0 !important;
    transform: none !important;
    transition: none;
  }
  .navbar-expand-md .offcanvas .offcanvas-header {
    display: none;
  }
  .navbar-expand-md .offcanvas .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
}
@media (min-width: 992px) {
  .navbar-expand-lg {
    flex-wrap: nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-lg .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-lg .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-lg .navbar-nav .nav-link {
    padding-right: var(--bs-navbar-nav-link-padding-x);
    padding-left: var(--bs-navbar-nav-link-padding-x);
  }
  .navbar-expand-lg .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-lg .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-lg .navbar-toggler {
    display: none;
  }
  .navbar-expand-lg .offcanvas {
    position: static;
    z-index: auto;
    flex-grow: 1;
    width: auto !important;
    height: auto !important;
    visibility: visible !important;
    background-color: transparent !important;
    border: 0 !important;
    transform: none !important;
    transition: none;
  }
  .navbar-expand-lg .offcanvas .offcanvas-header {
    display: none;
  }
  .navbar-expand-lg .offcanvas .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
}
@media (min-width: 1200px) {
  .navbar-expand-xl {
    flex-wrap: nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-xl .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-xl .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-xl .navbar-nav .nav-link {
    padding-right: var(--bs-navbar-nav-link-padding-x);
    padding-left: var(--bs-navbar-nav-link-padding-x);
  }
  .navbar-expand-xl .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-xl .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-xl .navbar-toggler {
    display: none;
  }
  .navbar-expand-xl .offcanvas {
    position: static;
    z-index: auto;
    flex-grow: 1;
    width: auto !important;
    height: auto !important;
    visibility: visible !important;
    background-color: transparent !important;
    border: 0 !important;
    transform: none !important;
    transition: none;
  }
  .navbar-expand-xl .offcanvas .offcanvas-header {
    display: none;
  }
  .navbar-expand-xl .offcanvas .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
}
@media (min-width: 1400px) {
  .navbar-expand-xxl {
    flex-wrap: nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-xxl .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-xxl .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-xxl .navbar-nav .nav-link {
    padding-right: var(--bs-navbar-nav-link-padding-x);
    padding-left: var(--bs-navbar-nav-link-padding-x);
  }
  .navbar-expand-xxl .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-xxl .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-xxl .navbar-toggler {
    display: none;
  }
  .navbar-expand-xxl .offcanvas {
    position: static;
    z-index: auto;
    flex-grow: 1;
    width: auto !important;
    height: auto !important;
    visibility: visible !important;
    background-color: transparent !important;
    border: 0 !important;
    transform: none !important;
    transition: none;
  }
  .navbar-expand-xxl .offcanvas .offcanvas-header {
    display: none;
  }
  .navbar-expand-xxl .offcanvas .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
}
.navbar-expand {
  flex-wrap: nowrap;
  justify-content: flex-start;
}
.navbar-expand .navbar-nav {
  flex-direction: row;
}
.navbar-expand .navbar-nav .dropdown-menu {
  position: absolute;
}
.navbar-expand .navbar-nav .nav-link {
  padding-right: var(--bs-navbar-nav-link-padding-x);
  padding-left: var(--bs-navbar-nav-link-padding-x);
}
.navbar-expand .navbar-nav-scroll {
  overflow: visible;
}
.navbar-expand .navbar-collapse {
  display: flex !important;
  flex-basis: auto;
}
.navbar-expand .navbar-toggler {
  display: none;
}
.navbar-expand .offcanvas {
  position: static;
  z-index: auto;
  flex-grow: 1;
  width: auto !important;
  height: auto !important;
  visibility: visible !important;
  background-color: transparent !important;
  border: 0 !important;
  transform: none !important;
  transition: none;
}
.navbar-expand .offcanvas .offcanvas-header {
  display: none;
}
.navbar-expand .offcanvas .offcanvas-body {
  display: flex;
  flex-grow: 0;
  padding: 0;
  overflow-y: visible;
}

.navbar-dark {
  --bs-navbar-color: rgba(255, 255, 255, 0.55);
  --bs-navbar-hover-color: rgba(255, 255, 255, 0.75);
  --bs-navbar-disabled-color: rgba(255, 255, 255, 0.25);
  --bs-navbar-active-color: #ffffff;
  --bs-navbar-brand-color: #ffffff;
  --bs-navbar-brand-hover-color: #ffffff;
  --bs-navbar-toggler-border-color: rgba(255, 255, 255, 0.1);
  --bs-navbar-toggler-icon-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.55%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

.card {
  --bs-card-spacer-y: 1rem;
  --bs-card-spacer-x: 1rem;
  --bs-card-title-spacer-y: 0.5rem;
  --bs-card-border-width: 1px;
  --bs-card-border-color: var(--bs-border-color-translucent);
  --bs-card-border-radius: 0.375rem;
  --bs-card-box-shadow: ;
  --bs-card-inner-border-radius: calc(0.375rem - 1px);
  --bs-card-cap-padding-y: 0.5rem;
  --bs-card-cap-padding-x: 1rem;
  --bs-card-cap-bg: rgba(0, 0, 0, 0.03);
  --bs-card-cap-color: ;
  --bs-card-height: ;
  --bs-card-color: ;
  --bs-card-bg: #ffffff;
  --bs-card-img-overlay-padding: 1rem;
  --bs-card-group-margin: 0.75rem;
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 0;
  height: var(--bs-card-height);
  word-wrap: break-word;
  background-color: var(--bs-card-bg);
  background-clip: border-box;
  border: var(--bs-card-border-width) solid var(--bs-card-border-color);
  border-radius: var(--bs-card-border-radius);
}
.card > hr {
  margin-right: 0;
  margin-left: 0;
}
.card > .list-group {
  border-top: inherit;
  border-bottom: inherit;
}
.card > .list-group:first-child {
  border-top-width: 0;
  border-top-left-radius: var(--bs-card-inner-border-radius);
  border-top-right-radius: var(--bs-card-inner-border-radius);
}
.card > .list-group:last-child {
  border-bottom-width: 0;
  border-bottom-right-radius: var(--bs-card-inner-border-radius);
  border-bottom-left-radius: var(--bs-card-inner-border-radius);
}
.card > .card-header + .list-group,
.card > .list-group + .card-footer {
  border-top: 0;
}

.card-body {
  flex: 1 1 auto;
  padding: var(--bs-card-spacer-y) var(--bs-card-spacer-x);
  color: var(--bs-card-color);
}

.card-title {
  margin-bottom: var(--bs-card-title-spacer-y);
}

.card-subtitle {
  margin-top: calc(-0.5 * var(--bs-card-title-spacer-y));
  margin-bottom: 0;
}

.card-text:last-child {
  margin-bottom: 0;
}

.card-link + .card-link {
  margin-left: var(--bs-card-spacer-x);
}

.card-header {
  padding: var(--bs-card-cap-padding-y) var(--bs-card-cap-padding-x);
  margin-bottom: 0;
  color: var(--bs-card-cap-color);
  background-color: var(--bs-card-cap-bg);
  border-bottom: var(--bs-card-border-width) solid var(--bs-card-border-color);
}
.card-header:first-child {
  border-radius: var(--bs-card-inner-border-radius) var(--bs-card-inner-border-radius) 0 0;
}

.card-footer {
  padding: var(--bs-card-cap-padding-y) var(--bs-card-cap-padding-x);
  color: var(--bs-card-cap-color);
  background-color: var(--bs-card-cap-bg);
  border-top: var(--bs-card-border-width) solid var(--bs-card-border-color);
}
.card-footer:last-child {
  border-radius: 0 0 var(--bs-card-inner-border-radius) var(--bs-card-inner-border-radius);
}

.card-header-tabs {
  margin-right: calc(-0.5 * var(--bs-card-cap-padding-x));
  margin-bottom: calc(-1 * var(--bs-card-cap-padding-y));
  margin-left: calc(-0.5 * var(--bs-card-cap-padding-x));
  border-bottom: 0;
}
.card-header-tabs .nav-link.active {
  background-color: var(--bs-card-bg);
  border-bottom-color: var(--bs-card-bg);
}

.card-header-pills {
  margin-right: calc(-0.5 * var(--bs-card-cap-padding-x));
  margin-left: calc(-0.5 * var(--bs-card-cap-padding-x));
}

.card-img-overlay {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  padding: var(--bs-card-img-overlay-padding);
  border-radius: var(--bs-card-inner-border-radius);
}

.card-img,
.card-img-top,
.card-img-bottom {
  width: 100%;
}

.card-img,
.card-img-top {
  border-top-left-radius: var(--bs-card-inner-border-radius);
  border-top-right-radius: var(--bs-card-inner-border-radius);
}

.card-img,
.card-img-bottom {
  border-bottom-right-radius: var(--bs-card-inner-border-radius);
  border-bottom-left-radius: var(--bs-card-inner-border-radius);
}

.card-group > .card {
  margin-bottom: var(--bs-card-group-margin);
}
@media (min-width: 576px) {
  .card-group {
    display: flex;
    flex-flow: row wrap;
  }
  .card-group > .card {
    flex: 1 0 0%;
    margin-bottom: 0;
  }
  .card-group > .card + .card {
    margin-left: 0;
    border-left: 0;
  }
  .card-group > .card:not(:last-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
  .card-group > .card:not(:last-child) .card-img-top,
  .card-group > .card:not(:last-child) .card-header {
    border-top-right-radius: 0;
  }
  .card-group > .card:not(:last-child) .card-img-bottom,
  .card-group > .card:not(:last-child) .card-footer {
    border-bottom-right-radius: 0;
  }
  .card-group > .card:not(:first-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
  .card-group > .card:not(:first-child) .card-img-top,
  .card-group > .card:not(:first-child) .card-header {
    border-top-left-radius: 0;
  }
  .card-group > .card:not(:first-child) .card-img-bottom,
  .card-group > .card:not(:first-child) .card-footer {
    border-bottom-left-radius: 0;
  }
}

.accordion {
  --bs-accordion-color: #212529;
  --bs-accordion-bg: #ffffff;
  --bs-accordion-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, border-radius 0.15s ease;
  --bs-accordion-border-color: var(--bs-border-color);
  --bs-accordion-border-width: 1px;
  --bs-accordion-border-radius: 0.375rem;
  --bs-accordion-inner-border-radius: calc(0.375rem - 1px);
  --bs-accordion-btn-padding-x: 1.25rem;
  --bs-accordion-btn-padding-y: 1rem;
  --bs-accordion-btn-color: #212529;
  --bs-accordion-btn-bg: var(--bs-accordion-bg);
  --bs-accordion-btn-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23212529'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
  --bs-accordion-btn-icon-width: 1.25rem;
  --bs-accordion-btn-icon-transform: rotate(-180deg);
  --bs-accordion-btn-icon-transition: transform 0.2s ease-in-out;
  --bs-accordion-btn-active-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23394acc'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
  --bs-accordion-btn-focus-border-color: #9fa9f1;
  --bs-accordion-btn-focus-box-shadow: 0 0 0 0.25rem rgba(63, 82, 227, 0.25);
  --bs-accordion-body-padding-x: 1.25rem;
  --bs-accordion-body-padding-y: 1rem;
  --bs-accordion-active-color: #394acc;
  --bs-accordion-active-bg: #eceefc;
}

.accordion-button {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  padding: var(--bs-accordion-btn-padding-y) var(--bs-accordion-btn-padding-x);
  font-size: 1rem;
  color: var(--bs-accordion-btn-color);
  text-align: left;
  background-color: var(--bs-accordion-btn-bg);
  border: 0;
  border-radius: 0;
  overflow-anchor: none;
  transition: var(--bs-accordion-transition);
}
@media (prefers-reduced-motion: reduce) {
  .accordion-button {
    transition: none;
  }
}
.accordion-button:not(.collapsed) {
  color: var(--bs-accordion-active-color);
  background-color: var(--bs-accordion-active-bg);
  box-shadow: inset 0 calc(-1 * var(--bs-accordion-border-width)) 0 var(--bs-accordion-border-color);
}
.accordion-button:not(.collapsed)::after {
  background-image: var(--bs-accordion-btn-active-icon);
  transform: var(--bs-accordion-btn-icon-transform);
}
.accordion-button::after {
  flex-shrink: 0;
  width: var(--bs-accordion-btn-icon-width);
  height: var(--bs-accordion-btn-icon-width);
  margin-left: auto;
  content: "";
  background-image: var(--bs-accordion-btn-icon);
  background-repeat: no-repeat;
  background-size: var(--bs-accordion-btn-icon-width);
  transition: var(--bs-accordion-btn-icon-transition);
}
@media (prefers-reduced-motion: reduce) {
  .accordion-button::after {
    transition: none;
  }
}
.accordion-button:hover {
  z-index: 2;
}
.accordion-button:focus {
  z-index: 3;
  border-color: var(--bs-accordion-btn-focus-border-color);
  outline: 0;
  box-shadow: var(--bs-accordion-btn-focus-box-shadow);
}

.accordion-header {
  margin-bottom: 0;
}

.accordion-item {
  color: var(--bs-accordion-color);
  background-color: var(--bs-accordion-bg);
  border: var(--bs-accordion-border-width) solid var(--bs-accordion-border-color);
}
.accordion-item:first-of-type {
  border-top-left-radius: var(--bs-accordion-border-radius);
  border-top-right-radius: var(--bs-accordion-border-radius);
}
.accordion-item:first-of-type .accordion-button {
  border-top-left-radius: var(--bs-accordion-inner-border-radius);
  border-top-right-radius: var(--bs-accordion-inner-border-radius);
}
.accordion-item:not(:first-of-type) {
  border-top: 0;
}
.accordion-item:last-of-type {
  border-bottom-right-radius: var(--bs-accordion-border-radius);
  border-bottom-left-radius: var(--bs-accordion-border-radius);
}
.accordion-item:last-of-type .accordion-button.collapsed {
  border-bottom-right-radius: var(--bs-accordion-inner-border-radius);
  border-bottom-left-radius: var(--bs-accordion-inner-border-radius);
}
.accordion-item:last-of-type .accordion-collapse {
  border-bottom-right-radius: var(--bs-accordion-border-radius);
  border-bottom-left-radius: var(--bs-accordion-border-radius);
}

.accordion-body {
  padding: var(--bs-accordion-body-padding-y) var(--bs-accordion-body-padding-x);
}

.accordion-flush .accordion-collapse {
  border-width: 0;
}
.accordion-flush .accordion-item {
  border-right: 0;
  border-left: 0;
  border-radius: 0;
}
.accordion-flush .accordion-item:first-child {
  border-top: 0;
}
.accordion-flush .accordion-item:last-child {
  border-bottom: 0;
}
.accordion-flush .accordion-item .accordion-button, .accordion-flush .accordion-item .accordion-button.collapsed {
  border-radius: 0;
}

.breadcrumb {
  --bs-breadcrumb-padding-x: 0;
  --bs-breadcrumb-padding-y: 0;
  --bs-breadcrumb-margin-bottom: 1rem;
  --bs-breadcrumb-bg: ;
  --bs-breadcrumb-border-radius: ;
  --bs-breadcrumb-divider-color: #6c757d;
  --bs-breadcrumb-item-padding-x: 0.5rem;
  --bs-breadcrumb-item-active-color: #6c757d;
  display: flex;
  flex-wrap: wrap;
  padding: var(--bs-breadcrumb-padding-y) var(--bs-breadcrumb-padding-x);
  margin-bottom: var(--bs-breadcrumb-margin-bottom);
  font-size: var(--bs-breadcrumb-font-size);
  list-style: none;
  background-color: var(--bs-breadcrumb-bg);
  border-radius: var(--bs-breadcrumb-border-radius);
}

.breadcrumb-item + .breadcrumb-item {
  padding-left: var(--bs-breadcrumb-item-padding-x);
}
.breadcrumb-item + .breadcrumb-item::before {
  float: left;
  padding-right: var(--bs-breadcrumb-item-padding-x);
  color: var(--bs-breadcrumb-divider-color);
  content: var(--bs-breadcrumb-divider, "/") /* rtl: var(--bs-breadcrumb-divider, "/") */;
}
.breadcrumb-item.active {
  color: var(--bs-breadcrumb-item-active-color);
}

.pagination {
  --bs-pagination-padding-x: 0.75rem;
  --bs-pagination-padding-y: 0.375rem;
  --bs-pagination-font-size: 1rem;
  --bs-pagination-color: var(--bs-link-color);
  --bs-pagination-bg: #ffffff;
  --bs-pagination-border-width: 1px;
  --bs-pagination-border-color: #dee2e6;
  --bs-pagination-border-radius: 0.375rem;
  --bs-pagination-hover-color: var(--bs-link-hover-color);
  --bs-pagination-hover-bg: #e9ecef;
  --bs-pagination-hover-border-color: #dee2e6;
  --bs-pagination-focus-color: var(--bs-link-hover-color);
  --bs-pagination-focus-bg: #e9ecef;
  --bs-pagination-focus-box-shadow: 0 0 0 0.25rem rgba(63, 82, 227, 0.25);
  --bs-pagination-active-color: #ffffff;
  --bs-pagination-active-bg: #3f52e3;
  --bs-pagination-active-border-color: #3f52e3;
  --bs-pagination-disabled-color: #6c757d;
  --bs-pagination-disabled-bg: #ffffff;
  --bs-pagination-disabled-border-color: #dee2e6;
  display: flex;
  padding-left: 0;
  list-style: none;
}

.page-link {
  position: relative;
  display: block;
  padding: var(--bs-pagination-padding-y) var(--bs-pagination-padding-x);
  font-size: var(--bs-pagination-font-size);
  color: var(--bs-pagination-color);
  text-decoration: none;
  background-color: var(--bs-pagination-bg);
  border: var(--bs-pagination-border-width) solid var(--bs-pagination-border-color);
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .page-link {
    transition: none;
  }
}
.page-link:hover {
  z-index: 2;
  color: var(--bs-pagination-hover-color);
  background-color: var(--bs-pagination-hover-bg);
  border-color: var(--bs-pagination-hover-border-color);
}
.page-link:focus {
  z-index: 3;
  color: var(--bs-pagination-focus-color);
  background-color: var(--bs-pagination-focus-bg);
  outline: 0;
  box-shadow: var(--bs-pagination-focus-box-shadow);
}
.page-link.active, .active > .page-link {
  z-index: 3;
  color: var(--bs-pagination-active-color);
  background-color: var(--bs-pagination-active-bg);
  border-color: var(--bs-pagination-active-border-color);
}
.page-link.disabled, .disabled > .page-link {
  color: var(--bs-pagination-disabled-color);
  pointer-events: none;
  background-color: var(--bs-pagination-disabled-bg);
  border-color: var(--bs-pagination-disabled-border-color);
}

.page-item:not(:first-child) .page-link {
  margin-left: -1px;
}
.page-item:first-child .page-link {
  border-top-left-radius: var(--bs-pagination-border-radius);
  border-bottom-left-radius: var(--bs-pagination-border-radius);
}
.page-item:last-child .page-link {
  border-top-right-radius: var(--bs-pagination-border-radius);
  border-bottom-right-radius: var(--bs-pagination-border-radius);
}

.pagination-lg {
  --bs-pagination-padding-x: 1.5rem;
  --bs-pagination-padding-y: 0.75rem;
  --bs-pagination-font-size: 1.25rem;
  --bs-pagination-border-radius: 0.5rem;
}

.pagination-sm {
  --bs-pagination-padding-x: 0.5rem;
  --bs-pagination-padding-y: 0.25rem;
  --bs-pagination-font-size: 0.875rem;
  --bs-pagination-border-radius: 0.25rem;
}

.badge {
  --bs-badge-padding-x: 0.65em;
  --bs-badge-padding-y: 0.35em;
  --bs-badge-font-size: 0.75em;
  --bs-badge-font-weight: 700;
  --bs-badge-color: #ffffff;
  --bs-badge-border-radius: 0.375rem;
  display: inline-block;
  padding: var(--bs-badge-padding-y) var(--bs-badge-padding-x);
  font-size: var(--bs-badge-font-size);
  font-weight: var(--bs-badge-font-weight);
  line-height: 1;
  color: var(--bs-badge-color);
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: var(--bs-badge-border-radius);
}
.badge:empty {
  display: none;
}

.btn .badge {
  position: relative;
  top: -1px;
}

.alert {
  --bs-alert-bg: transparent;
  --bs-alert-padding-x: 1rem;
  --bs-alert-padding-y: 1rem;
  --bs-alert-margin-bottom: 1rem;
  --bs-alert-color: inherit;
  --bs-alert-border-color: transparent;
  --bs-alert-border: 1px solid var(--bs-alert-border-color);
  --bs-alert-border-radius: 0.375rem;
  position: relative;
  padding: var(--bs-alert-padding-y) var(--bs-alert-padding-x);
  margin-bottom: var(--bs-alert-margin-bottom);
  color: var(--bs-alert-color);
  background-color: var(--bs-alert-bg);
  border: var(--bs-alert-border);
  border-radius: var(--bs-alert-border-radius);
}

.alert-heading {
  color: inherit;
}

.alert-link {
  font-weight: 700;
}

.alert-dismissible {
  padding-right: 3rem;
}
.alert-dismissible .btn-close {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 2;
  padding: 1.25rem 1rem;
}

.alert-primary {
  --bs-alert-color: #263188;
  --bs-alert-bg: #d9dcf9;
  --bs-alert-border-color: #c5cbf7;
}
.alert-primary .alert-link {
  color: #1e276d;
}

.alert-secondary {
  --bs-alert-color: #282828;
  --bs-alert-bg: #d9d9d9;
  --bs-alert-border-color: #c7c7c7;
}
.alert-secondary .alert-link {
  color: #202020;
}

.alert-success {
  --bs-alert-color: #277a3e;
  --bs-alert-bg: #d9f5e1;
  --bs-alert-border-color: #c6f0d2;
}
.alert-success .alert-link {
  color: #1f6232;
}

.alert-info {
  --bs-alert-color: #01616f;
  --bs-alert-bg: #ccecf1;
  --bs-alert-border-color: #b3e3ea;
}
.alert-info .alert-link {
  color: #014e59;
}

.alert-warning {
  --bs-alert-color: #664b14;
  --bs-alert-bg: #fff1d6;
  --bs-alert-border-color: #ffebc2;
}
.alert-warning .alert-link {
  color: #523c10;
}

.alert-danger {
  --bs-alert-color: #991616;
  --bs-alert-bg: #ffd3d3;
  --bs-alert-border-color: #ffbdbd;
}
.alert-danger .alert-link {
  color: #7a1212;
}

.alert-light {
  --bs-alert-color: #666666;
  --bs-alert-bg: white;
  --bs-alert-border-color: white;
}
.alert-light .alert-link {
  color: #525252;
}

.alert-dark {
  --bs-alert-color: #0a0a13;
  --bs-alert-bg: #cfcfd2;
  --bs-alert-border-color: #b8b8bc;
}
.alert-dark .alert-link {
  color: #08080f;
}

.alert-gray-ligth {
  --bs-alert-color: #56585d;
  --bs-alert-bg: #f7f8fb;
  --bs-alert-border-color: #f3f5f8;
}
.alert-gray-ligth .alert-link {
  color: #45464a;
}

@keyframes progress-bar-stripes {
  0% {
    background-position-x: 1rem;
  }
}
.progress {
  --bs-progress-height: 1rem;
  --bs-progress-font-size: 0.75rem;
  --bs-progress-bg: #e9ecef;
  --bs-progress-border-radius: 0.375rem;
  --bs-progress-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.075);
  --bs-progress-bar-color: #ffffff;
  --bs-progress-bar-bg: #3f52e3;
  --bs-progress-bar-transition: width 0.6s ease;
  display: flex;
  height: var(--bs-progress-height);
  overflow: hidden;
  font-size: var(--bs-progress-font-size);
  background-color: var(--bs-progress-bg);
  border-radius: var(--bs-progress-border-radius);
}

.progress-bar {
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
  color: var(--bs-progress-bar-color);
  text-align: center;
  white-space: nowrap;
  background-color: var(--bs-progress-bar-bg);
  transition: var(--bs-progress-bar-transition);
}
@media (prefers-reduced-motion: reduce) {
  .progress-bar {
    transition: none;
  }
}

.progress-bar-striped {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: var(--bs-progress-height) var(--bs-progress-height);
}

.progress-bar-animated {
  animation: 1s linear infinite progress-bar-stripes;
}
@media (prefers-reduced-motion: reduce) {
  .progress-bar-animated {
    animation: none;
  }
}

.list-group {
  --bs-list-group-color: #212529;
  --bs-list-group-bg: #ffffff;
  --bs-list-group-border-color: rgba(0, 0, 0, 0.125);
  --bs-list-group-border-width: 1px;
  --bs-list-group-border-radius: 0.375rem;
  --bs-list-group-item-padding-x: 1rem;
  --bs-list-group-item-padding-y: 0.5rem;
  --bs-list-group-action-color: #495057;
  --bs-list-group-action-hover-color: #495057;
  --bs-list-group-action-hover-bg: #f8f9fa;
  --bs-list-group-action-active-color: #212529;
  --bs-list-group-action-active-bg: #e9ecef;
  --bs-list-group-disabled-color: #6c757d;
  --bs-list-group-disabled-bg: #ffffff;
  --bs-list-group-active-color: #ffffff;
  --bs-list-group-active-bg: #3f52e3;
  --bs-list-group-active-border-color: #3f52e3;
  display: flex;
  flex-direction: column;
  padding-left: 0;
  margin-bottom: 0;
  border-radius: var(--bs-list-group-border-radius);
}

.list-group-numbered {
  list-style-type: none;
  counter-reset: section;
}
.list-group-numbered > .list-group-item::before {
  content: counters(section, ".") ". ";
  counter-increment: section;
}

.list-group-item-action {
  width: 100%;
  color: var(--bs-list-group-action-color);
  text-align: inherit;
}
.list-group-item-action:hover, .list-group-item-action:focus {
  z-index: 1;
  color: var(--bs-list-group-action-hover-color);
  text-decoration: none;
  background-color: var(--bs-list-group-action-hover-bg);
}
.list-group-item-action:active {
  color: var(--bs-list-group-action-active-color);
  background-color: var(--bs-list-group-action-active-bg);
}

.list-group-item {
  position: relative;
  display: block;
  padding: var(--bs-list-group-item-padding-y) var(--bs-list-group-item-padding-x);
  color: var(--bs-list-group-color);
  text-decoration: none;
  background-color: var(--bs-list-group-bg);
  border: var(--bs-list-group-border-width) solid var(--bs-list-group-border-color);
}
.list-group-item:first-child {
  border-top-left-radius: inherit;
  border-top-right-radius: inherit;
}
.list-group-item:last-child {
  border-bottom-right-radius: inherit;
  border-bottom-left-radius: inherit;
}
.list-group-item.disabled, .list-group-item:disabled {
  color: var(--bs-list-group-disabled-color);
  pointer-events: none;
  background-color: var(--bs-list-group-disabled-bg);
}
.list-group-item.active {
  z-index: 2;
  color: var(--bs-list-group-active-color);
  background-color: var(--bs-list-group-active-bg);
  border-color: var(--bs-list-group-active-border-color);
}
.list-group-item + .list-group-item {
  border-top-width: 0;
}
.list-group-item + .list-group-item.active {
  margin-top: calc(-1 * var(--bs-list-group-border-width));
  border-top-width: var(--bs-list-group-border-width);
}

.list-group-horizontal {
  flex-direction: row;
}
.list-group-horizontal > .list-group-item:first-child:not(:last-child) {
  border-bottom-left-radius: var(--bs-list-group-border-radius);
  border-top-right-radius: 0;
}
.list-group-horizontal > .list-group-item:last-child:not(:first-child) {
  border-top-right-radius: var(--bs-list-group-border-radius);
  border-bottom-left-radius: 0;
}
.list-group-horizontal > .list-group-item.active {
  margin-top: 0;
}
.list-group-horizontal > .list-group-item + .list-group-item {
  border-top-width: var(--bs-list-group-border-width);
  border-left-width: 0;
}
.list-group-horizontal > .list-group-item + .list-group-item.active {
  margin-left: calc(-1 * var(--bs-list-group-border-width));
  border-left-width: var(--bs-list-group-border-width);
}

@media (min-width: 576px) {
  .list-group-horizontal-sm {
    flex-direction: row;
  }
  .list-group-horizontal-sm > .list-group-item:first-child:not(:last-child) {
    border-bottom-left-radius: var(--bs-list-group-border-radius);
    border-top-right-radius: 0;
  }
  .list-group-horizontal-sm > .list-group-item:last-child:not(:first-child) {
    border-top-right-radius: var(--bs-list-group-border-radius);
    border-bottom-left-radius: 0;
  }
  .list-group-horizontal-sm > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-sm > .list-group-item + .list-group-item {
    border-top-width: var(--bs-list-group-border-width);
    border-left-width: 0;
  }
  .list-group-horizontal-sm > .list-group-item + .list-group-item.active {
    margin-left: calc(-1 * var(--bs-list-group-border-width));
    border-left-width: var(--bs-list-group-border-width);
  }
}
@media (min-width: 768px) {
  .list-group-horizontal-md {
    flex-direction: row;
  }
  .list-group-horizontal-md > .list-group-item:first-child:not(:last-child) {
    border-bottom-left-radius: var(--bs-list-group-border-radius);
    border-top-right-radius: 0;
  }
  .list-group-horizontal-md > .list-group-item:last-child:not(:first-child) {
    border-top-right-radius: var(--bs-list-group-border-radius);
    border-bottom-left-radius: 0;
  }
  .list-group-horizontal-md > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-md > .list-group-item + .list-group-item {
    border-top-width: var(--bs-list-group-border-width);
    border-left-width: 0;
  }
  .list-group-horizontal-md > .list-group-item + .list-group-item.active {
    margin-left: calc(-1 * var(--bs-list-group-border-width));
    border-left-width: var(--bs-list-group-border-width);
  }
}
@media (min-width: 992px) {
  .list-group-horizontal-lg {
    flex-direction: row;
  }
  .list-group-horizontal-lg > .list-group-item:first-child:not(:last-child) {
    border-bottom-left-radius: var(--bs-list-group-border-radius);
    border-top-right-radius: 0;
  }
  .list-group-horizontal-lg > .list-group-item:last-child:not(:first-child) {
    border-top-right-radius: var(--bs-list-group-border-radius);
    border-bottom-left-radius: 0;
  }
  .list-group-horizontal-lg > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-lg > .list-group-item + .list-group-item {
    border-top-width: var(--bs-list-group-border-width);
    border-left-width: 0;
  }
  .list-group-horizontal-lg > .list-group-item + .list-group-item.active {
    margin-left: calc(-1 * var(--bs-list-group-border-width));
    border-left-width: var(--bs-list-group-border-width);
  }
}
@media (min-width: 1200px) {
  .list-group-horizontal-xl {
    flex-direction: row;
  }
  .list-group-horizontal-xl > .list-group-item:first-child:not(:last-child) {
    border-bottom-left-radius: var(--bs-list-group-border-radius);
    border-top-right-radius: 0;
  }
  .list-group-horizontal-xl > .list-group-item:last-child:not(:first-child) {
    border-top-right-radius: var(--bs-list-group-border-radius);
    border-bottom-left-radius: 0;
  }
  .list-group-horizontal-xl > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-xl > .list-group-item + .list-group-item {
    border-top-width: var(--bs-list-group-border-width);
    border-left-width: 0;
  }
  .list-group-horizontal-xl > .list-group-item + .list-group-item.active {
    margin-left: calc(-1 * var(--bs-list-group-border-width));
    border-left-width: var(--bs-list-group-border-width);
  }
}
@media (min-width: 1400px) {
  .list-group-horizontal-xxl {
    flex-direction: row;
  }
  .list-group-horizontal-xxl > .list-group-item:first-child:not(:last-child) {
    border-bottom-left-radius: var(--bs-list-group-border-radius);
    border-top-right-radius: 0;
  }
  .list-group-horizontal-xxl > .list-group-item:last-child:not(:first-child) {
    border-top-right-radius: var(--bs-list-group-border-radius);
    border-bottom-left-radius: 0;
  }
  .list-group-horizontal-xxl > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-xxl > .list-group-item + .list-group-item {
    border-top-width: var(--bs-list-group-border-width);
    border-left-width: 0;
  }
  .list-group-horizontal-xxl > .list-group-item + .list-group-item.active {
    margin-left: calc(-1 * var(--bs-list-group-border-width));
    border-left-width: var(--bs-list-group-border-width);
  }
}
.list-group-flush {
  border-radius: 0;
}
.list-group-flush > .list-group-item {
  border-width: 0 0 var(--bs-list-group-border-width);
}
.list-group-flush > .list-group-item:last-child {
  border-bottom-width: 0;
}

.list-group-item-primary {
  color: #263188;
  background-color: #d9dcf9;
}
.list-group-item-primary.list-group-item-action:hover, .list-group-item-primary.list-group-item-action:focus {
  color: #263188;
  background-color: #c3c6e0;
}
.list-group-item-primary.list-group-item-action.active {
  color: #ffffff;
  background-color: #263188;
  border-color: #263188;
}

.list-group-item-secondary {
  color: #282828;
  background-color: #d9d9d9;
}
.list-group-item-secondary.list-group-item-action:hover, .list-group-item-secondary.list-group-item-action:focus {
  color: #282828;
  background-color: #c3c3c3;
}
.list-group-item-secondary.list-group-item-action.active {
  color: #ffffff;
  background-color: #282828;
  border-color: #282828;
}

.list-group-item-success {
  color: #277a3e;
  background-color: #d9f5e1;
}
.list-group-item-success.list-group-item-action:hover, .list-group-item-success.list-group-item-action:focus {
  color: #277a3e;
  background-color: #c3ddcb;
}
.list-group-item-success.list-group-item-action.active {
  color: #ffffff;
  background-color: #277a3e;
  border-color: #277a3e;
}

.list-group-item-info {
  color: #01616f;
  background-color: #ccecf1;
}
.list-group-item-info.list-group-item-action:hover, .list-group-item-info.list-group-item-action:focus {
  color: #01616f;
  background-color: #b8d4d9;
}
.list-group-item-info.list-group-item-action.active {
  color: #ffffff;
  background-color: #01616f;
  border-color: #01616f;
}

.list-group-item-warning {
  color: #664b14;
  background-color: #fff1d6;
}
.list-group-item-warning.list-group-item-action:hover, .list-group-item-warning.list-group-item-action:focus {
  color: #664b14;
  background-color: #e6d9c1;
}
.list-group-item-warning.list-group-item-action.active {
  color: #ffffff;
  background-color: #664b14;
  border-color: #664b14;
}

.list-group-item-danger {
  color: #991616;
  background-color: #ffd3d3;
}
.list-group-item-danger.list-group-item-action:hover, .list-group-item-danger.list-group-item-action:focus {
  color: #991616;
  background-color: #e6bebe;
}
.list-group-item-danger.list-group-item-action.active {
  color: #ffffff;
  background-color: #991616;
  border-color: #991616;
}

.list-group-item-light {
  color: #666666;
  background-color: white;
}
.list-group-item-light.list-group-item-action:hover, .list-group-item-light.list-group-item-action:focus {
  color: #666666;
  background-color: #e6e6e6;
}
.list-group-item-light.list-group-item-action.active {
  color: #ffffff;
  background-color: #666666;
  border-color: #666666;
}

.list-group-item-dark {
  color: #0a0a13;
  background-color: #cfcfd2;
}
.list-group-item-dark.list-group-item-action:hover, .list-group-item-dark.list-group-item-action:focus {
  color: #0a0a13;
  background-color: #bababd;
}
.list-group-item-dark.list-group-item-action.active {
  color: #ffffff;
  background-color: #0a0a13;
  border-color: #0a0a13;
}

.list-group-item-gray-ligth {
  color: #56585d;
  background-color: #f7f8fb;
}
.list-group-item-gray-ligth.list-group-item-action:hover, .list-group-item-gray-ligth.list-group-item-action:focus {
  color: #56585d;
  background-color: #dedfe2;
}
.list-group-item-gray-ligth.list-group-item-action.active {
  color: #ffffff;
  background-color: #56585d;
  border-color: #56585d;
}

.btn-close {
  box-sizing: content-box;
  width: 1em;
  height: 1em;
  padding: 0.25em 0.25em;
  color: #000000;
  background: transparent url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23000000'%3e%3cpath d='M.293.293a1 1 0 0 1 1.414 0L8 6.586 14.293.293a1 1 0 1 1 1.414 1.414L9.414 8l6.293 6.293a1 1 0 0 1-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 0 1-1.414-1.414L6.586 8 .293 1.707a1 1 0 0 1 0-1.414z'/%3e%3c/svg%3e") center/1em auto no-repeat;
  border: 0;
  border-radius: 0.375rem;
  opacity: 0.5;
}
.btn-close:hover {
  color: #000000;
  text-decoration: none;
  opacity: 0.75;
}
.btn-close:focus {
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(63, 82, 227, 0.25);
  opacity: 1;
}
.btn-close:disabled, .btn-close.disabled {
  pointer-events: none;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  opacity: 0.25;
}

.btn-close-white {
  filter: invert(1) grayscale(100%) brightness(200%);
}

.toast {
  --bs-toast-zindex: 1090;
  --bs-toast-padding-x: 0.75rem;
  --bs-toast-padding-y: 0.5rem;
  --bs-toast-spacing: 1.5rem;
  --bs-toast-max-width: 350px;
  --bs-toast-font-size: 0.875rem;
  --bs-toast-color: ;
  --bs-toast-bg: rgba(255, 255, 255, 0.85);
  --bs-toast-border-width: 1px;
  --bs-toast-border-color: var(--bs-border-color-translucent);
  --bs-toast-border-radius: 0.375rem;
  --bs-toast-box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  --bs-toast-header-color: #6c757d;
  --bs-toast-header-bg: rgba(255, 255, 255, 0.85);
  --bs-toast-header-border-color: rgba(0, 0, 0, 0.05);
  width: var(--bs-toast-max-width);
  max-width: 100%;
  font-size: var(--bs-toast-font-size);
  color: var(--bs-toast-color);
  pointer-events: auto;
  background-color: var(--bs-toast-bg);
  background-clip: padding-box;
  border: var(--bs-toast-border-width) solid var(--bs-toast-border-color);
  box-shadow: var(--bs-toast-box-shadow);
  border-radius: var(--bs-toast-border-radius);
}
.toast.showing {
  opacity: 0;
}
.toast:not(.show) {
  display: none;
}

.toast-container {
  --bs-toast-zindex: 1090;
  position: absolute;
  z-index: var(--bs-toast-zindex);
  width: -moz-max-content;
  width: max-content;
  max-width: 100%;
  pointer-events: none;
}
.toast-container > :not(:last-child) {
  margin-bottom: var(--bs-toast-spacing);
}

.toast-header {
  display: flex;
  align-items: center;
  padding: var(--bs-toast-padding-y) var(--bs-toast-padding-x);
  color: var(--bs-toast-header-color);
  background-color: var(--bs-toast-header-bg);
  background-clip: padding-box;
  border-bottom: var(--bs-toast-border-width) solid var(--bs-toast-header-border-color);
  border-top-left-radius: calc(var(--bs-toast-border-radius) - var(--bs-toast-border-width));
  border-top-right-radius: calc(var(--bs-toast-border-radius) - var(--bs-toast-border-width));
}
.toast-header .btn-close {
  margin-right: calc(-0.5 * var(--bs-toast-padding-x));
  margin-left: var(--bs-toast-padding-x);
}

.toast-body {
  padding: var(--bs-toast-padding-x);
  word-wrap: break-word;
}

.modal {
  --bs-modal-zindex: 1055;
  --bs-modal-width: 500px;
  --bs-modal-padding: 1rem;
  --bs-modal-margin: 0.5rem;
  --bs-modal-color: ;
  --bs-modal-bg: #ffffff;
  --bs-modal-border-color: var(--bs-border-color-translucent);
  --bs-modal-border-width: 1px;
  --bs-modal-border-radius: 0.5rem;
  --bs-modal-box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  --bs-modal-inner-border-radius: calc(0.5rem - 1px);
  --bs-modal-header-padding-x: 1rem;
  --bs-modal-header-padding-y: 1rem;
  --bs-modal-header-padding: 1rem 1rem;
  --bs-modal-header-border-color: var(--bs-border-color);
  --bs-modal-header-border-width: 1px;
  --bs-modal-title-line-height: 1.5;
  --bs-modal-footer-gap: 0.5rem;
  --bs-modal-footer-bg: ;
  --bs-modal-footer-border-color: var(--bs-border-color);
  --bs-modal-footer-border-width: 1px;
  position: fixed;
  top: 0;
  left: 0;
  z-index: var(--bs-modal-zindex);
  display: none;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  outline: 0;
}

.modal-dialog {
  position: relative;
  width: auto;
  margin: var(--bs-modal-margin);
  pointer-events: none;
}
.modal.fade .modal-dialog {
  transition: transform 0.3s ease-out;
  transform: translate(0, -50px);
}
@media (prefers-reduced-motion: reduce) {
  .modal.fade .modal-dialog {
    transition: none;
  }
}
.modal.show .modal-dialog {
  transform: none;
}
.modal.modal-static .modal-dialog {
  transform: scale(1.02);
}

.modal-dialog-scrollable {
  height: calc(100% - var(--bs-modal-margin) * 2);
}
.modal-dialog-scrollable .modal-content {
  max-height: 100%;
  overflow: hidden;
}
.modal-dialog-scrollable .modal-body {
  overflow-y: auto;
}

.modal-dialog-centered {
  display: flex;
  align-items: center;
  min-height: calc(100% - var(--bs-modal-margin) * 2);
}

.modal-content {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  color: var(--bs-modal-color);
  pointer-events: auto;
  background-color: var(--bs-modal-bg);
  background-clip: padding-box;
  border: var(--bs-modal-border-width) solid var(--bs-modal-border-color);
  border-radius: var(--bs-modal-border-radius);
  outline: 0;
}

.modal-backdrop {
  --bs-backdrop-zindex: 1050;
  --bs-backdrop-bg: #000000;
  --bs-backdrop-opacity: 0.5;
  position: fixed;
  top: 0;
  left: 0;
  z-index: var(--bs-backdrop-zindex);
  width: 100vw;
  height: 100vh;
  background-color: var(--bs-backdrop-bg);
}
.modal-backdrop.fade {
  opacity: 0;
}
.modal-backdrop.show {
  opacity: var(--bs-backdrop-opacity);
}

.modal-header {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: space-between;
  padding: var(--bs-modal-header-padding);
  border-bottom: var(--bs-modal-header-border-width) solid var(--bs-modal-header-border-color);
  border-top-left-radius: var(--bs-modal-inner-border-radius);
  border-top-right-radius: var(--bs-modal-inner-border-radius);
}
.modal-header .btn-close {
  padding: calc(var(--bs-modal-header-padding-y) * 0.5) calc(var(--bs-modal-header-padding-x) * 0.5);
  margin: calc(-0.5 * var(--bs-modal-header-padding-y)) calc(-0.5 * var(--bs-modal-header-padding-x)) calc(-0.5 * var(--bs-modal-header-padding-y)) auto;
}

.modal-title {
  margin-bottom: 0;
  line-height: var(--bs-modal-title-line-height);
}

.modal-body {
  position: relative;
  flex: 1 1 auto;
  padding: var(--bs-modal-padding);
}

.modal-footer {
  display: flex;
  flex-shrink: 0;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-end;
  padding: calc(var(--bs-modal-padding) - var(--bs-modal-footer-gap) * 0.5);
  background-color: var(--bs-modal-footer-bg);
  border-top: var(--bs-modal-footer-border-width) solid var(--bs-modal-footer-border-color);
  border-bottom-right-radius: var(--bs-modal-inner-border-radius);
  border-bottom-left-radius: var(--bs-modal-inner-border-radius);
}
.modal-footer > * {
  margin: calc(var(--bs-modal-footer-gap) * 0.5);
}

@media (min-width: 576px) {
  .modal {
    --bs-modal-margin: 1.75rem;
    --bs-modal-box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  }
  .modal-dialog {
    max-width: var(--bs-modal-width);
    margin-right: auto;
    margin-left: auto;
  }
  .modal-sm {
    --bs-modal-width: 300px;
  }
}
@media (min-width: 992px) {
  .modal-lg,
  .modal-xl {
    --bs-modal-width: 800px;
  }
}
@media (min-width: 1200px) {
  .modal-xl {
    --bs-modal-width: 1140px;
  }
}
.modal-fullscreen {
  width: 100vw;
  max-width: none;
  height: 100%;
  margin: 0;
}
.modal-fullscreen .modal-content {
  height: 100%;
  border: 0;
  border-radius: 0;
}
.modal-fullscreen .modal-header,
.modal-fullscreen .modal-footer {
  border-radius: 0;
}
.modal-fullscreen .modal-body {
  overflow-y: auto;
}

@media (max-width: 575.98px) {
  .modal-fullscreen-sm-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen-sm-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen-sm-down .modal-header,
  .modal-fullscreen-sm-down .modal-footer {
    border-radius: 0;
  }
  .modal-fullscreen-sm-down .modal-body {
    overflow-y: auto;
  }
}
@media (max-width: 767.98px) {
  .modal-fullscreen-md-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen-md-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen-md-down .modal-header,
  .modal-fullscreen-md-down .modal-footer {
    border-radius: 0;
  }
  .modal-fullscreen-md-down .modal-body {
    overflow-y: auto;
  }
}
@media (max-width: 991.98px) {
  .modal-fullscreen-lg-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen-lg-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen-lg-down .modal-header,
  .modal-fullscreen-lg-down .modal-footer {
    border-radius: 0;
  }
  .modal-fullscreen-lg-down .modal-body {
    overflow-y: auto;
  }
}
@media (max-width: 1199.98px) {
  .modal-fullscreen-xl-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen-xl-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen-xl-down .modal-header,
  .modal-fullscreen-xl-down .modal-footer {
    border-radius: 0;
  }
  .modal-fullscreen-xl-down .modal-body {
    overflow-y: auto;
  }
}
@media (max-width: 1399.98px) {
  .modal-fullscreen-xxl-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen-xxl-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen-xxl-down .modal-header,
  .modal-fullscreen-xxl-down .modal-footer {
    border-radius: 0;
  }
  .modal-fullscreen-xxl-down .modal-body {
    overflow-y: auto;
  }
}
.tooltip {
  --bs-tooltip-zindex: 1080;
  --bs-tooltip-max-width: 200px;
  --bs-tooltip-padding-x: 0.5rem;
  --bs-tooltip-padding-y: 0.25rem;
  --bs-tooltip-margin: ;
  --bs-tooltip-font-size: 0.875rem;
  --bs-tooltip-color: #ffffff;
  --bs-tooltip-bg: #000000;
  --bs-tooltip-border-radius: 0.375rem;
  --bs-tooltip-opacity: 0.9;
  --bs-tooltip-arrow-width: 0.8rem;
  --bs-tooltip-arrow-height: 0.4rem;
  z-index: var(--bs-tooltip-zindex);
  display: block;
  padding: var(--bs-tooltip-arrow-height);
  margin: var(--bs-tooltip-margin);
  font-family: var(--bs-font-sans-serif);
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  white-space: normal;
  word-spacing: normal;
  line-break: auto;
  font-size: var(--bs-tooltip-font-size);
  word-wrap: break-word;
  opacity: 0;
}
.tooltip.show {
  opacity: var(--bs-tooltip-opacity);
}
.tooltip .tooltip-arrow {
  display: block;
  width: var(--bs-tooltip-arrow-width);
  height: var(--bs-tooltip-arrow-height);
}
.tooltip .tooltip-arrow::before {
  position: absolute;
  content: "";
  border-color: transparent;
  border-style: solid;
}

.bs-tooltip-top .tooltip-arrow, .bs-tooltip-auto[data-popper-placement^=top] .tooltip-arrow {
  bottom: 0;
}
.bs-tooltip-top .tooltip-arrow::before, .bs-tooltip-auto[data-popper-placement^=top] .tooltip-arrow::before {
  top: -1px;
  border-width: var(--bs-tooltip-arrow-height) calc(var(--bs-tooltip-arrow-width) * 0.5) 0;
  border-top-color: var(--bs-tooltip-bg);
}

/* rtl:begin:ignore */
.bs-tooltip-end .tooltip-arrow, .bs-tooltip-auto[data-popper-placement^=right] .tooltip-arrow {
  left: 0;
  width: var(--bs-tooltip-arrow-height);
  height: var(--bs-tooltip-arrow-width);
}
.bs-tooltip-end .tooltip-arrow::before, .bs-tooltip-auto[data-popper-placement^=right] .tooltip-arrow::before {
  right: -1px;
  border-width: calc(var(--bs-tooltip-arrow-width) * 0.5) var(--bs-tooltip-arrow-height) calc(var(--bs-tooltip-arrow-width) * 0.5) 0;
  border-right-color: var(--bs-tooltip-bg);
}

/* rtl:end:ignore */
.bs-tooltip-bottom .tooltip-arrow, .bs-tooltip-auto[data-popper-placement^=bottom] .tooltip-arrow {
  top: 0;
}
.bs-tooltip-bottom .tooltip-arrow::before, .bs-tooltip-auto[data-popper-placement^=bottom] .tooltip-arrow::before {
  bottom: -1px;
  border-width: 0 calc(var(--bs-tooltip-arrow-width) * 0.5) var(--bs-tooltip-arrow-height);
  border-bottom-color: var(--bs-tooltip-bg);
}

/* rtl:begin:ignore */
.bs-tooltip-start .tooltip-arrow, .bs-tooltip-auto[data-popper-placement^=left] .tooltip-arrow {
  right: 0;
  width: var(--bs-tooltip-arrow-height);
  height: var(--bs-tooltip-arrow-width);
}
.bs-tooltip-start .tooltip-arrow::before, .bs-tooltip-auto[data-popper-placement^=left] .tooltip-arrow::before {
  left: -1px;
  border-width: calc(var(--bs-tooltip-arrow-width) * 0.5) 0 calc(var(--bs-tooltip-arrow-width) * 0.5) var(--bs-tooltip-arrow-height);
  border-left-color: var(--bs-tooltip-bg);
}

/* rtl:end:ignore */
.tooltip-inner {
  max-width: var(--bs-tooltip-max-width);
  padding: var(--bs-tooltip-padding-y) var(--bs-tooltip-padding-x);
  color: var(--bs-tooltip-color);
  text-align: center;
  background-color: var(--bs-tooltip-bg);
  border-radius: var(--bs-tooltip-border-radius);
}

.popover {
  --bs-popover-zindex: 1070;
  --bs-popover-max-width: 276px;
  --bs-popover-font-size: 0.875rem;
  --bs-popover-bg: #ffffff;
  --bs-popover-border-width: 1px;
  --bs-popover-border-color: var(--bs-border-color-translucent);
  --bs-popover-border-radius: 0.5rem;
  --bs-popover-inner-border-radius: calc(0.5rem - 1px);
  --bs-popover-box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  --bs-popover-header-padding-x: 1rem;
  --bs-popover-header-padding-y: 0.5rem;
  --bs-popover-header-font-size: 1rem;
  --bs-popover-header-color: ;
  --bs-popover-header-bg: #f0f0f0;
  --bs-popover-body-padding-x: 1rem;
  --bs-popover-body-padding-y: 1rem;
  --bs-popover-body-color: #212529;
  --bs-popover-arrow-width: 1rem;
  --bs-popover-arrow-height: 0.5rem;
  --bs-popover-arrow-border: var(--bs-popover-border-color);
  z-index: var(--bs-popover-zindex);
  display: block;
  max-width: var(--bs-popover-max-width);
  font-family: var(--bs-font-sans-serif);
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  white-space: normal;
  word-spacing: normal;
  line-break: auto;
  font-size: var(--bs-popover-font-size);
  word-wrap: break-word;
  background-color: var(--bs-popover-bg);
  background-clip: padding-box;
  border: var(--bs-popover-border-width) solid var(--bs-popover-border-color);
  border-radius: var(--bs-popover-border-radius);
}
.popover .popover-arrow {
  display: block;
  width: var(--bs-popover-arrow-width);
  height: var(--bs-popover-arrow-height);
}
.popover .popover-arrow::before, .popover .popover-arrow::after {
  position: absolute;
  display: block;
  content: "";
  border-color: transparent;
  border-style: solid;
  border-width: 0;
}

.bs-popover-top > .popover-arrow, .bs-popover-auto[data-popper-placement^=top] > .popover-arrow {
  bottom: calc(-1 * (var(--bs-popover-arrow-height)) - var(--bs-popover-border-width));
}
.bs-popover-top > .popover-arrow::before, .bs-popover-auto[data-popper-placement^=top] > .popover-arrow::before, .bs-popover-top > .popover-arrow::after, .bs-popover-auto[data-popper-placement^=top] > .popover-arrow::after {
  border-width: var(--bs-popover-arrow-height) calc(var(--bs-popover-arrow-width) * 0.5) 0;
}
.bs-popover-top > .popover-arrow::before, .bs-popover-auto[data-popper-placement^=top] > .popover-arrow::before {
  bottom: 0;
  border-top-color: var(--bs-popover-arrow-border);
}
.bs-popover-top > .popover-arrow::after, .bs-popover-auto[data-popper-placement^=top] > .popover-arrow::after {
  bottom: var(--bs-popover-border-width);
  border-top-color: var(--bs-popover-bg);
}

/* rtl:begin:ignore */
.bs-popover-end > .popover-arrow, .bs-popover-auto[data-popper-placement^=right] > .popover-arrow {
  left: calc(-1 * (var(--bs-popover-arrow-height)) - var(--bs-popover-border-width));
  width: var(--bs-popover-arrow-height);
  height: var(--bs-popover-arrow-width);
}
.bs-popover-end > .popover-arrow::before, .bs-popover-auto[data-popper-placement^=right] > .popover-arrow::before, .bs-popover-end > .popover-arrow::after, .bs-popover-auto[data-popper-placement^=right] > .popover-arrow::after {
  border-width: calc(var(--bs-popover-arrow-width) * 0.5) var(--bs-popover-arrow-height) calc(var(--bs-popover-arrow-width) * 0.5) 0;
}
.bs-popover-end > .popover-arrow::before, .bs-popover-auto[data-popper-placement^=right] > .popover-arrow::before {
  left: 0;
  border-right-color: var(--bs-popover-arrow-border);
}
.bs-popover-end > .popover-arrow::after, .bs-popover-auto[data-popper-placement^=right] > .popover-arrow::after {
  left: var(--bs-popover-border-width);
  border-right-color: var(--bs-popover-bg);
}

/* rtl:end:ignore */
.bs-popover-bottom > .popover-arrow, .bs-popover-auto[data-popper-placement^=bottom] > .popover-arrow {
  top: calc(-1 * (var(--bs-popover-arrow-height)) - var(--bs-popover-border-width));
}
.bs-popover-bottom > .popover-arrow::before, .bs-popover-auto[data-popper-placement^=bottom] > .popover-arrow::before, .bs-popover-bottom > .popover-arrow::after, .bs-popover-auto[data-popper-placement^=bottom] > .popover-arrow::after {
  border-width: 0 calc(var(--bs-popover-arrow-width) * 0.5) var(--bs-popover-arrow-height);
}
.bs-popover-bottom > .popover-arrow::before, .bs-popover-auto[data-popper-placement^=bottom] > .popover-arrow::before {
  top: 0;
  border-bottom-color: var(--bs-popover-arrow-border);
}
.bs-popover-bottom > .popover-arrow::after, .bs-popover-auto[data-popper-placement^=bottom] > .popover-arrow::after {
  top: var(--bs-popover-border-width);
  border-bottom-color: var(--bs-popover-bg);
}
.bs-popover-bottom .popover-header::before, .bs-popover-auto[data-popper-placement^=bottom] .popover-header::before {
  position: absolute;
  top: 0;
  left: 50%;
  display: block;
  width: var(--bs-popover-arrow-width);
  margin-left: calc(-0.5 * var(--bs-popover-arrow-width));
  content: "";
  border-bottom: var(--bs-popover-border-width) solid var(--bs-popover-header-bg);
}

/* rtl:begin:ignore */
.bs-popover-start > .popover-arrow, .bs-popover-auto[data-popper-placement^=left] > .popover-arrow {
  right: calc(-1 * (var(--bs-popover-arrow-height)) - var(--bs-popover-border-width));
  width: var(--bs-popover-arrow-height);
  height: var(--bs-popover-arrow-width);
}
.bs-popover-start > .popover-arrow::before, .bs-popover-auto[data-popper-placement^=left] > .popover-arrow::before, .bs-popover-start > .popover-arrow::after, .bs-popover-auto[data-popper-placement^=left] > .popover-arrow::after {
  border-width: calc(var(--bs-popover-arrow-width) * 0.5) 0 calc(var(--bs-popover-arrow-width) * 0.5) var(--bs-popover-arrow-height);
}
.bs-popover-start > .popover-arrow::before, .bs-popover-auto[data-popper-placement^=left] > .popover-arrow::before {
  right: 0;
  border-left-color: var(--bs-popover-arrow-border);
}
.bs-popover-start > .popover-arrow::after, .bs-popover-auto[data-popper-placement^=left] > .popover-arrow::after {
  right: var(--bs-popover-border-width);
  border-left-color: var(--bs-popover-bg);
}

/* rtl:end:ignore */
.popover-header {
  padding: var(--bs-popover-header-padding-y) var(--bs-popover-header-padding-x);
  margin-bottom: 0;
  font-size: var(--bs-popover-header-font-size);
  color: var(--bs-popover-header-color);
  background-color: var(--bs-popover-header-bg);
  border-bottom: var(--bs-popover-border-width) solid var(--bs-popover-border-color);
  border-top-left-radius: var(--bs-popover-inner-border-radius);
  border-top-right-radius: var(--bs-popover-inner-border-radius);
}
.popover-header:empty {
  display: none;
}

.popover-body {
  padding: var(--bs-popover-body-padding-y) var(--bs-popover-body-padding-x);
  color: var(--bs-popover-body-color);
}

.carousel {
  position: relative;
}

.carousel.pointer-event {
  touch-action: pan-y;
}

.carousel-inner {
  position: relative;
  width: 100%;
  overflow: hidden;
}
.carousel-inner::after {
  display: block;
  clear: both;
  content: "";
}

.carousel-item {
  position: relative;
  display: none;
  float: left;
  width: 100%;
  margin-right: -100%;
  backface-visibility: hidden;
  transition: transform 0.6s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-item {
    transition: none;
  }
}

.carousel-item.active,
.carousel-item-next,
.carousel-item-prev {
  display: block;
}

.carousel-item-next:not(.carousel-item-start),
.active.carousel-item-end {
  transform: translateX(100%);
}

.carousel-item-prev:not(.carousel-item-end),
.active.carousel-item-start {
  transform: translateX(-100%);
}

.carousel-fade .carousel-item {
  opacity: 0;
  transition-property: opacity;
  transform: none;
}
.carousel-fade .carousel-item.active,
.carousel-fade .carousel-item-next.carousel-item-start,
.carousel-fade .carousel-item-prev.carousel-item-end {
  z-index: 1;
  opacity: 1;
}
.carousel-fade .active.carousel-item-start,
.carousel-fade .active.carousel-item-end {
  z-index: 0;
  opacity: 0;
  transition: opacity 0s 0.6s;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-fade .active.carousel-item-start,
  .carousel-fade .active.carousel-item-end {
    transition: none;
  }
}

.carousel-control-prev,
.carousel-control-next {
  position: absolute;
  top: 0;
  bottom: 0;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 15%;
  padding: 0;
  color: #ffffff;
  text-align: center;
  background: none;
  border: 0;
  opacity: 0.5;
  transition: opacity 0.15s ease;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-control-prev,
  .carousel-control-next {
    transition: none;
  }
}
.carousel-control-prev:hover, .carousel-control-prev:focus,
.carousel-control-next:hover,
.carousel-control-next:focus {
  color: #ffffff;
  text-decoration: none;
  outline: 0;
  opacity: 0.9;
}

.carousel-control-prev {
  left: 0;
}

.carousel-control-next {
  right: 0;
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  background-repeat: no-repeat;
  background-position: 50%;
  background-size: 100% 100%;
}

/* rtl:options: {
  "autoRename": true,
  "stringMap":[ {
    "name"    : "prev-next",
    "search"  : "prev",
    "replace" : "next"
  } ]
} */
.carousel-control-prev-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23ffffff'%3e%3cpath d='M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z'/%3e%3c/svg%3e");
}

.carousel-control-next-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23ffffff'%3e%3cpath d='M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}

.carousel-indicators {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 2;
  display: flex;
  justify-content: center;
  padding: 0;
  margin-right: 15%;
  margin-bottom: 1rem;
  margin-left: 15%;
  list-style: none;
}
.carousel-indicators [data-bs-target] {
  box-sizing: content-box;
  flex: 0 1 auto;
  width: 30px;
  height: 3px;
  padding: 0;
  margin-right: 3px;
  margin-left: 3px;
  text-indent: -999px;
  cursor: pointer;
  background-color: #ffffff;
  background-clip: padding-box;
  border: 0;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  opacity: 0.5;
  transition: opacity 0.6s ease;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-indicators [data-bs-target] {
    transition: none;
  }
}
.carousel-indicators .active {
  opacity: 1;
}

.carousel-caption {
  position: absolute;
  right: 15%;
  bottom: 1.25rem;
  left: 15%;
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
  color: #ffffff;
  text-align: center;
}

.carousel-dark .carousel-control-prev-icon,
.carousel-dark .carousel-control-next-icon {
  filter: invert(1) grayscale(100);
}
.carousel-dark .carousel-indicators [data-bs-target] {
  background-color: #000000;
}
.carousel-dark .carousel-caption {
  color: #000000;
}

.spinner-grow,
.spinner-border {
  display: inline-block;
  width: var(--bs-spinner-width);
  height: var(--bs-spinner-height);
  vertical-align: var(--bs-spinner-vertical-align);
  border-radius: 50%;
  animation: var(--bs-spinner-animation-speed) linear infinite var(--bs-spinner-animation-name);
}

@keyframes spinner-border {
  to {
    transform: rotate(360deg) /* rtl:ignore */;
  }
}
.spinner-border {
  --bs-spinner-width: 2rem;
  --bs-spinner-height: 2rem;
  --bs-spinner-vertical-align: -0.125em;
  --bs-spinner-border-width: 0.25em;
  --bs-spinner-animation-speed: 0.75s;
  --bs-spinner-animation-name: spinner-border;
  border: var(--bs-spinner-border-width) solid currentcolor;
  border-right-color: transparent;
}

.spinner-border-sm {
  --bs-spinner-width: 1rem;
  --bs-spinner-height: 1rem;
  --bs-spinner-border-width: 0.2em;
}

@keyframes spinner-grow {
  0% {
    transform: scale(0);
  }
  50% {
    opacity: 1;
    transform: none;
  }
}
.spinner-grow {
  --bs-spinner-width: 2rem;
  --bs-spinner-height: 2rem;
  --bs-spinner-vertical-align: -0.125em;
  --bs-spinner-animation-speed: 0.75s;
  --bs-spinner-animation-name: spinner-grow;
  background-color: currentcolor;
  opacity: 0;
}

.spinner-grow-sm {
  --bs-spinner-width: 1rem;
  --bs-spinner-height: 1rem;
}

@media (prefers-reduced-motion: reduce) {
  .spinner-border,
  .spinner-grow {
    --bs-spinner-animation-speed: 1.5s;
  }
}
.offcanvas, .offcanvas-xxl, .offcanvas-xl, .offcanvas-lg, .offcanvas-md, .offcanvas-sm {
  --bs-offcanvas-zindex: 1045;
  --bs-offcanvas-width: 400px;
  --bs-offcanvas-height: 30vh;
  --bs-offcanvas-padding-x: 1rem;
  --bs-offcanvas-padding-y: 1rem;
  --bs-offcanvas-color: ;
  --bs-offcanvas-bg: #ffffff;
  --bs-offcanvas-border-width: 1px;
  --bs-offcanvas-border-color: var(--bs-border-color-translucent);
  --bs-offcanvas-box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

@media (max-width: 575.98px) {
  .offcanvas-sm {
    position: fixed;
    bottom: 0;
    z-index: var(--bs-offcanvas-zindex);
    display: flex;
    flex-direction: column;
    max-width: 100%;
    color: var(--bs-offcanvas-color);
    visibility: hidden;
    background-color: var(--bs-offcanvas-bg);
    background-clip: padding-box;
    outline: 0;
    transition: transform 0.3s ease-in-out;
  }
}
@media (max-width: 575.98px) and (prefers-reduced-motion: reduce) {
  .offcanvas-sm {
    transition: none;
  }
}
@media (max-width: 575.98px) {
  .offcanvas-sm.offcanvas-start {
    top: 0;
    left: 0;
    width: var(--bs-offcanvas-width);
    border-right: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(-100%);
  }
  .offcanvas-sm.offcanvas-end {
    top: 0;
    right: 0;
    width: var(--bs-offcanvas-width);
    border-left: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(100%);
  }
  .offcanvas-sm.offcanvas-top {
    top: 0;
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-bottom: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(-100%);
  }
  .offcanvas-sm.offcanvas-bottom {
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-top: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(100%);
  }
  .offcanvas-sm.showing, .offcanvas-sm.show:not(.hiding) {
    transform: none;
  }
  .offcanvas-sm.showing, .offcanvas-sm.hiding, .offcanvas-sm.show {
    visibility: visible;
  }
}
@media (min-width: 576px) {
  .offcanvas-sm {
    --bs-offcanvas-height: auto;
    --bs-offcanvas-border-width: 0;
    background-color: transparent !important;
  }
  .offcanvas-sm .offcanvas-header {
    display: none;
  }
  .offcanvas-sm .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
    background-color: transparent !important;
  }
}

@media (max-width: 767.98px) {
  .offcanvas-md {
    position: fixed;
    bottom: 0;
    z-index: var(--bs-offcanvas-zindex);
    display: flex;
    flex-direction: column;
    max-width: 100%;
    color: var(--bs-offcanvas-color);
    visibility: hidden;
    background-color: var(--bs-offcanvas-bg);
    background-clip: padding-box;
    outline: 0;
    transition: transform 0.3s ease-in-out;
  }
}
@media (max-width: 767.98px) and (prefers-reduced-motion: reduce) {
  .offcanvas-md {
    transition: none;
  }
}
@media (max-width: 767.98px) {
  .offcanvas-md.offcanvas-start {
    top: 0;
    left: 0;
    width: var(--bs-offcanvas-width);
    border-right: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(-100%);
  }
  .offcanvas-md.offcanvas-end {
    top: 0;
    right: 0;
    width: var(--bs-offcanvas-width);
    border-left: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(100%);
  }
  .offcanvas-md.offcanvas-top {
    top: 0;
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-bottom: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(-100%);
  }
  .offcanvas-md.offcanvas-bottom {
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-top: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(100%);
  }
  .offcanvas-md.showing, .offcanvas-md.show:not(.hiding) {
    transform: none;
  }
  .offcanvas-md.showing, .offcanvas-md.hiding, .offcanvas-md.show {
    visibility: visible;
  }
}
@media (min-width: 768px) {
  .offcanvas-md {
    --bs-offcanvas-height: auto;
    --bs-offcanvas-border-width: 0;
    background-color: transparent !important;
  }
  .offcanvas-md .offcanvas-header {
    display: none;
  }
  .offcanvas-md .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
    background-color: transparent !important;
  }
}

@media (max-width: 991.98px) {
  .offcanvas-lg {
    position: fixed;
    bottom: 0;
    z-index: var(--bs-offcanvas-zindex);
    display: flex;
    flex-direction: column;
    max-width: 100%;
    color: var(--bs-offcanvas-color);
    visibility: hidden;
    background-color: var(--bs-offcanvas-bg);
    background-clip: padding-box;
    outline: 0;
    transition: transform 0.3s ease-in-out;
  }
}
@media (max-width: 991.98px) and (prefers-reduced-motion: reduce) {
  .offcanvas-lg {
    transition: none;
  }
}
@media (max-width: 991.98px) {
  .offcanvas-lg.offcanvas-start {
    top: 0;
    left: 0;
    width: var(--bs-offcanvas-width);
    border-right: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(-100%);
  }
  .offcanvas-lg.offcanvas-end {
    top: 0;
    right: 0;
    width: var(--bs-offcanvas-width);
    border-left: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(100%);
  }
  .offcanvas-lg.offcanvas-top {
    top: 0;
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-bottom: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(-100%);
  }
  .offcanvas-lg.offcanvas-bottom {
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-top: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(100%);
  }
  .offcanvas-lg.showing, .offcanvas-lg.show:not(.hiding) {
    transform: none;
  }
  .offcanvas-lg.showing, .offcanvas-lg.hiding, .offcanvas-lg.show {
    visibility: visible;
  }
}
@media (min-width: 992px) {
  .offcanvas-lg {
    --bs-offcanvas-height: auto;
    --bs-offcanvas-border-width: 0;
    background-color: transparent !important;
  }
  .offcanvas-lg .offcanvas-header {
    display: none;
  }
  .offcanvas-lg .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
    background-color: transparent !important;
  }
}

@media (max-width: 1199.98px) {
  .offcanvas-xl {
    position: fixed;
    bottom: 0;
    z-index: var(--bs-offcanvas-zindex);
    display: flex;
    flex-direction: column;
    max-width: 100%;
    color: var(--bs-offcanvas-color);
    visibility: hidden;
    background-color: var(--bs-offcanvas-bg);
    background-clip: padding-box;
    outline: 0;
    transition: transform 0.3s ease-in-out;
  }
}
@media (max-width: 1199.98px) and (prefers-reduced-motion: reduce) {
  .offcanvas-xl {
    transition: none;
  }
}
@media (max-width: 1199.98px) {
  .offcanvas-xl.offcanvas-start {
    top: 0;
    left: 0;
    width: var(--bs-offcanvas-width);
    border-right: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(-100%);
  }
  .offcanvas-xl.offcanvas-end {
    top: 0;
    right: 0;
    width: var(--bs-offcanvas-width);
    border-left: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(100%);
  }
  .offcanvas-xl.offcanvas-top {
    top: 0;
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-bottom: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(-100%);
  }
  .offcanvas-xl.offcanvas-bottom {
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-top: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(100%);
  }
  .offcanvas-xl.showing, .offcanvas-xl.show:not(.hiding) {
    transform: none;
  }
  .offcanvas-xl.showing, .offcanvas-xl.hiding, .offcanvas-xl.show {
    visibility: visible;
  }
}
@media (min-width: 1200px) {
  .offcanvas-xl {
    --bs-offcanvas-height: auto;
    --bs-offcanvas-border-width: 0;
    background-color: transparent !important;
  }
  .offcanvas-xl .offcanvas-header {
    display: none;
  }
  .offcanvas-xl .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
    background-color: transparent !important;
  }
}

@media (max-width: 1399.98px) {
  .offcanvas-xxl {
    position: fixed;
    bottom: 0;
    z-index: var(--bs-offcanvas-zindex);
    display: flex;
    flex-direction: column;
    max-width: 100%;
    color: var(--bs-offcanvas-color);
    visibility: hidden;
    background-color: var(--bs-offcanvas-bg);
    background-clip: padding-box;
    outline: 0;
    transition: transform 0.3s ease-in-out;
  }
}
@media (max-width: 1399.98px) and (prefers-reduced-motion: reduce) {
  .offcanvas-xxl {
    transition: none;
  }
}
@media (max-width: 1399.98px) {
  .offcanvas-xxl.offcanvas-start {
    top: 0;
    left: 0;
    width: var(--bs-offcanvas-width);
    border-right: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(-100%);
  }
  .offcanvas-xxl.offcanvas-end {
    top: 0;
    right: 0;
    width: var(--bs-offcanvas-width);
    border-left: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(100%);
  }
  .offcanvas-xxl.offcanvas-top {
    top: 0;
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-bottom: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(-100%);
  }
  .offcanvas-xxl.offcanvas-bottom {
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-top: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(100%);
  }
  .offcanvas-xxl.showing, .offcanvas-xxl.show:not(.hiding) {
    transform: none;
  }
  .offcanvas-xxl.showing, .offcanvas-xxl.hiding, .offcanvas-xxl.show {
    visibility: visible;
  }
}
@media (min-width: 1400px) {
  .offcanvas-xxl {
    --bs-offcanvas-height: auto;
    --bs-offcanvas-border-width: 0;
    background-color: transparent !important;
  }
  .offcanvas-xxl .offcanvas-header {
    display: none;
  }
  .offcanvas-xxl .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
    background-color: transparent !important;
  }
}

.offcanvas {
  position: fixed;
  bottom: 0;
  z-index: var(--bs-offcanvas-zindex);
  display: flex;
  flex-direction: column;
  max-width: 100%;
  color: var(--bs-offcanvas-color);
  visibility: hidden;
  background-color: var(--bs-offcanvas-bg);
  background-clip: padding-box;
  outline: 0;
  transition: transform 0.3s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .offcanvas {
    transition: none;
  }
}
.offcanvas.offcanvas-start {
  top: 0;
  left: 0;
  width: var(--bs-offcanvas-width);
  border-right: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
  transform: translateX(-100%);
}
.offcanvas.offcanvas-end {
  top: 0;
  right: 0;
  width: var(--bs-offcanvas-width);
  border-left: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
  transform: translateX(100%);
}
.offcanvas.offcanvas-top {
  top: 0;
  right: 0;
  left: 0;
  height: var(--bs-offcanvas-height);
  max-height: 100%;
  border-bottom: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
  transform: translateY(-100%);
}
.offcanvas.offcanvas-bottom {
  right: 0;
  left: 0;
  height: var(--bs-offcanvas-height);
  max-height: 100%;
  border-top: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
  transform: translateY(100%);
}
.offcanvas.showing, .offcanvas.show:not(.hiding) {
  transform: none;
}
.offcanvas.showing, .offcanvas.hiding, .offcanvas.show {
  visibility: visible;
}

.offcanvas-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1040;
  width: 100vw;
  height: 100vh;
  background-color: #000000;
}
.offcanvas-backdrop.fade {
  opacity: 0;
}
.offcanvas-backdrop.show {
  opacity: 0.5;
}

.offcanvas-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--bs-offcanvas-padding-y) var(--bs-offcanvas-padding-x);
}
.offcanvas-header .btn-close {
  padding: calc(var(--bs-offcanvas-padding-y) * 0.5) calc(var(--bs-offcanvas-padding-x) * 0.5);
  margin-top: calc(-0.5 * var(--bs-offcanvas-padding-y));
  margin-right: calc(-0.5 * var(--bs-offcanvas-padding-x));
  margin-bottom: calc(-0.5 * var(--bs-offcanvas-padding-y));
}

.offcanvas-title {
  margin-bottom: 0;
  line-height: 1.5;
}

.offcanvas-body {
  flex-grow: 1;
  padding: var(--bs-offcanvas-padding-y) var(--bs-offcanvas-padding-x);
  overflow-y: auto;
}

.placeholder {
  display: inline-block;
  min-height: 1em;
  vertical-align: middle;
  cursor: wait;
  background-color: currentcolor;
  opacity: 0.5;
}
.placeholder.btn::before {
  display: inline-block;
  content: "";
}

.placeholder-xs {
  min-height: 0.6em;
}

.placeholder-sm {
  min-height: 0.8em;
}

.placeholder-lg {
  min-height: 1.2em;
}

.placeholder-glow .placeholder {
  animation: placeholder-glow 2s ease-in-out infinite;
}

@keyframes placeholder-glow {
  50% {
    opacity: 0.2;
  }
}
.placeholder-wave {
  -webkit-mask-image: linear-gradient(130deg, #000000 55%, rgba(0, 0, 0, 0.8) 75%, #000000 95%);
          mask-image: linear-gradient(130deg, #000000 55%, rgba(0, 0, 0, 0.8) 75%, #000000 95%);
  -webkit-mask-size: 200% 100%;
          mask-size: 200% 100%;
  animation: placeholder-wave 2s linear infinite;
}

@keyframes placeholder-wave {
  100% {
    -webkit-mask-position: -200% 0%;
            mask-position: -200% 0%;
  }
}
.clearfix::after {
  display: block;
  clear: both;
  content: "";
}

.text-bg-primary {
  color: #ffffff !important;
  background-color: RGBA(63, 82, 227, var(--bs-bg-opacity, 1)) !important;
}

.text-bg-secondary {
  color: #ffffff !important;
  background-color: RGBA(67, 67, 67, var(--bs-bg-opacity, 1)) !important;
}

.text-bg-success {
  color: #000000 !important;
  background-color: RGBA(65, 204, 104, var(--bs-bg-opacity, 1)) !important;
}

.text-bg-info {
  color: #000000 !important;
  background-color: RGBA(2, 162, 185, var(--bs-bg-opacity, 1)) !important;
}

.text-bg-warning {
  color: #000000 !important;
  background-color: RGBA(255, 187, 51, var(--bs-bg-opacity, 1)) !important;
}

.text-bg-danger {
  color: #000000 !important;
  background-color: RGBA(255, 36, 36, var(--bs-bg-opacity, 1)) !important;
}

.text-bg-light {
  color: #000000 !important;
  background-color: RGBA(255, 255, 255, var(--bs-bg-opacity, 1)) !important;
}

.text-bg-dark {
  color: #ffffff !important;
  background-color: RGBA(17, 17, 32, var(--bs-bg-opacity, 1)) !important;
}

.text-bg-gray-ligth {
  color: #000000 !important;
  background-color: RGBA(215, 221, 233, var(--bs-bg-opacity, 1)) !important;
}

.link-primary {
  color: #3f52e3 !important;
}
.link-primary:hover, .link-primary:focus {
  color: #3242b6 !important;
}

.link-secondary {
  color: #434343 !important;
}
.link-secondary:hover, .link-secondary:focus {
  color: #363636 !important;
}

.link-success {
  color: #41cc68 !important;
}
.link-success:hover, .link-success:focus {
  color: #67d686 !important;
}

.link-info {
  color: #02a2b9 !important;
}
.link-info:hover, .link-info:focus {
  color: #35b5c7 !important;
}

.link-warning {
  color: #ffbb33 !important;
}
.link-warning:hover, .link-warning:focus {
  color: #ffc95c !important;
}

.link-danger {
  color: #ff2424 !important;
}
.link-danger:hover, .link-danger:focus {
  color: #ff5050 !important;
}

.link-light {
  color: #ffffff !important;
}
.link-light:hover, .link-light:focus {
  color: white !important;
}

.link-dark {
  color: #111120 !important;
}
.link-dark:hover, .link-dark:focus {
  color: #0e0e1a !important;
}

.link-gray-ligth {
  color: #d7dde9 !important;
}
.link-gray-ligth:hover, .link-gray-ligth:focus {
  color: #dfe4ed !important;
}

.ratio {
  position: relative;
  width: 100%;
}
.ratio::before {
  display: block;
  padding-top: var(--bs-aspect-ratio);
  content: "";
}
.ratio > * {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.ratio-1x1 {
  --bs-aspect-ratio: 100%;
}

.ratio-4x3 {
  --bs-aspect-ratio: 75%;
}

.ratio-16x9 {
  --bs-aspect-ratio: 56.25%;
}

.ratio-21x9 {
  --bs-aspect-ratio: 42.8571428571%;
}

.fixed-top {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1030;
}

.fixed-bottom {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1030;
}

.sticky-top {
  position: sticky;
  top: 0;
  z-index: 1020;
}

.sticky-bottom {
  position: sticky;
  bottom: 0;
  z-index: 1020;
}

@media (min-width: 576px) {
  .sticky-sm-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }
  .sticky-sm-bottom {
    position: sticky;
    bottom: 0;
    z-index: 1020;
  }
}
@media (min-width: 768px) {
  .sticky-md-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }
  .sticky-md-bottom {
    position: sticky;
    bottom: 0;
    z-index: 1020;
  }
}
@media (min-width: 992px) {
  .sticky-lg-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }
  .sticky-lg-bottom {
    position: sticky;
    bottom: 0;
    z-index: 1020;
  }
}
@media (min-width: 1200px) {
  .sticky-xl-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }
  .sticky-xl-bottom {
    position: sticky;
    bottom: 0;
    z-index: 1020;
  }
}
@media (min-width: 1400px) {
  .sticky-xxl-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }
  .sticky-xxl-bottom {
    position: sticky;
    bottom: 0;
    z-index: 1020;
  }
}
.hstack {
  display: flex;
  flex-direction: row;
  align-items: center;
  align-self: stretch;
}

.vstack {
  display: flex;
  flex: 1 1 auto;
  flex-direction: column;
  align-self: stretch;
}

.visually-hidden,
.visually-hidden-focusable:not(:focus):not(:focus-within) {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

.stretched-link::after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;
  content: "";
}

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.vr {
  display: inline-block;
  align-self: stretch;
  width: 1px;
  min-height: 1em;
  background-color: currentcolor;
  opacity: 0.25;
}

.align-baseline {
  vertical-align: baseline !important;
}

.align-top {
  vertical-align: top !important;
}

.align-middle {
  vertical-align: middle !important;
}

.align-bottom {
  vertical-align: bottom !important;
}

.align-text-bottom {
  vertical-align: text-bottom !important;
}

.align-text-top {
  vertical-align: text-top !important;
}

.float-start {
  float: left !important;
}

.float-end {
  float: right !important;
}

.float-none {
  float: none !important;
}

.opacity-0 {
  opacity: 0 !important;
}

.opacity-25 {
  opacity: 0.25 !important;
}

.opacity-50 {
  opacity: 0.5 !important;
}

.opacity-75 {
  opacity: 0.75 !important;
}

.opacity-100 {
  opacity: 1 !important;
}

.overflow-auto {
  overflow: auto !important;
}

.overflow-hidden {
  overflow: hidden !important;
}

.overflow-visible {
  overflow: visible !important;
}

.overflow-scroll {
  overflow: scroll !important;
}

.d-inline {
  display: inline !important;
}

.d-inline-block {
  display: inline-block !important;
}

.d-block {
  display: block !important;
}

.d-grid {
  display: grid !important;
}

.d-table {
  display: table !important;
}

.d-table-row {
  display: table-row !important;
}

.d-table-cell {
  display: table-cell !important;
}

.d-flex {
  display: flex !important;
}

.d-inline-flex {
  display: inline-flex !important;
}

.d-none {
  display: none !important;
}

.shadow {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.shadow-sm {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.shadow-lg {
  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}

.shadow-none {
  box-shadow: none !important;
}

.position-static {
  position: static !important;
}

.position-relative {
  position: relative !important;
}

.position-absolute {
  position: absolute !important;
}

.position-fixed {
  position: fixed !important;
}

.position-sticky {
  position: sticky !important;
}

.top-0 {
  top: 0 !important;
}

.top-50 {
  top: 50% !important;
}

.top-100 {
  top: 100% !important;
}

.bottom-0 {
  bottom: 0 !important;
}

.bottom-50 {
  bottom: 50% !important;
}

.bottom-100 {
  bottom: 100% !important;
}

.start-0 {
  left: 0 !important;
}

.start-50 {
  left: 50% !important;
}

.start-100 {
  left: 100% !important;
}

.end-0 {
  right: 0 !important;
}

.end-50 {
  right: 50% !important;
}

.end-100 {
  right: 100% !important;
}

.translate-middle {
  transform: translate(-50%, -50%) !important;
}

.translate-middle-x {
  transform: translateX(-50%) !important;
}

.translate-middle-y {
  transform: translateY(-50%) !important;
}

.border {
  border: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
}

.border-0 {
  border: 0 !important;
}

.border-top {
  border-top: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
}

.border-top-0 {
  border-top: 0 !important;
}

.border-end {
  border-right: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
}

.border-end-0 {
  border-right: 0 !important;
}

.border-bottom {
  border-bottom: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
}

.border-bottom-0 {
  border-bottom: 0 !important;
}

.border-start {
  border-left: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
}

.border-start-0 {
  border-left: 0 !important;
}

.border-primary {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-primary-rgb), var(--bs-border-opacity)) !important;
}

.border-secondary {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-secondary-rgb), var(--bs-border-opacity)) !important;
}

.border-success {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-success-rgb), var(--bs-border-opacity)) !important;
}

.border-info {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-info-rgb), var(--bs-border-opacity)) !important;
}

.border-warning {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-warning-rgb), var(--bs-border-opacity)) !important;
}

.border-danger {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-danger-rgb), var(--bs-border-opacity)) !important;
}

.border-light {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-light-rgb), var(--bs-border-opacity)) !important;
}

.border-dark {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-dark-rgb), var(--bs-border-opacity)) !important;
}

.border-gray-ligth {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-gray-ligth-rgb), var(--bs-border-opacity)) !important;
}

.border-white {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-white-rgb), var(--bs-border-opacity)) !important;
}

.border-1 {
  --bs-border-width: 1px;
}

.border-2 {
  --bs-border-width: 2px;
}

.border-3 {
  --bs-border-width: 3px;
}

.border-4 {
  --bs-border-width: 4px;
}

.border-5 {
  --bs-border-width: 5px;
}

.border-opacity-10 {
  --bs-border-opacity: 0.1;
}

.border-opacity-25 {
  --bs-border-opacity: 0.25;
}

.border-opacity-50 {
  --bs-border-opacity: 0.5;
}

.border-opacity-75 {
  --bs-border-opacity: 0.75;
}

.border-opacity-100 {
  --bs-border-opacity: 1;
}

.w-10 {
  width: 10% !important;
}

.w-15 {
  width: 15% !important;
}

.w-25 {
  width: 25% !important;
}

.w-50 {
  width: 50% !important;
}

.w-75 {
  width: 75% !important;
}

.w-85 {
  width: 85% !important;
}

.w-90 {
  width: 90% !important;
}

.w-100 {
  width: 100% !important;
}

.w-auto {
  width: auto !important;
}

.mw-100 {
  max-width: 100% !important;
}

.vw-100 {
  width: 100vw !important;
}

.min-vw-100 {
  min-width: 100vw !important;
}

.h-25 {
  height: 25% !important;
}

.h-50 {
  height: 50% !important;
}

.h-75 {
  height: 75% !important;
}

.h-100 {
  height: 100% !important;
}

.h-auto {
  height: auto !important;
}

.mh-100 {
  max-height: 100% !important;
}

.vh-100 {
  height: 100vh !important;
}

.min-vh-100 {
  min-height: 100vh !important;
}

.flex-fill {
  flex: 1 1 auto !important;
}

.flex-row {
  flex-direction: row !important;
}

.flex-column {
  flex-direction: column !important;
}

.flex-row-reverse {
  flex-direction: row-reverse !important;
}

.flex-column-reverse {
  flex-direction: column-reverse !important;
}

.flex-grow-0 {
  flex-grow: 0 !important;
}

.flex-grow-1 {
  flex-grow: 1 !important;
}

.flex-shrink-0 {
  flex-shrink: 0 !important;
}

.flex-shrink-1 {
  flex-shrink: 1 !important;
}

.flex-wrap {
  flex-wrap: wrap !important;
}

.flex-nowrap {
  flex-wrap: nowrap !important;
}

.flex-wrap-reverse {
  flex-wrap: wrap-reverse !important;
}

.justify-content-start {
  justify-content: flex-start !important;
}

.justify-content-end {
  justify-content: flex-end !important;
}

.justify-content-center {
  justify-content: center !important;
}

.justify-content-between {
  justify-content: space-between !important;
}

.justify-content-around {
  justify-content: space-around !important;
}

.justify-content-evenly {
  justify-content: space-evenly !important;
}

.align-items-start {
  align-items: flex-start !important;
}

.align-items-end {
  align-items: flex-end !important;
}

.align-items-center {
  align-items: center !important;
}

.align-items-baseline {
  align-items: baseline !important;
}

.align-items-stretch {
  align-items: stretch !important;
}

.align-content-start {
  align-content: flex-start !important;
}

.align-content-end {
  align-content: flex-end !important;
}

.align-content-center {
  align-content: center !important;
}

.align-content-between {
  align-content: space-between !important;
}

.align-content-around {
  align-content: space-around !important;
}

.align-content-stretch {
  align-content: stretch !important;
}

.align-self-auto {
  align-self: auto !important;
}

.align-self-start {
  align-self: flex-start !important;
}

.align-self-end {
  align-self: flex-end !important;
}

.align-self-center {
  align-self: center !important;
}

.align-self-baseline {
  align-self: baseline !important;
}

.align-self-stretch {
  align-self: stretch !important;
}

.order-first {
  order: -1 !important;
}

.order-0 {
  order: 0 !important;
}

.order-1 {
  order: 1 !important;
}

.order-2 {
  order: 2 !important;
}

.order-3 {
  order: 3 !important;
}

.order-4 {
  order: 4 !important;
}

.order-5 {
  order: 5 !important;
}

.order-last {
  order: 6 !important;
}

.m-0 {
  margin: 0 !important;
}

.m-1 {
  margin: 0.25rem !important;
}

.m-2 {
  margin: 0.5rem !important;
}

.m-3 {
  margin: 1rem !important;
}

.m-4 {
  margin: 1.5rem !important;
}

.m-5 {
  margin: 3rem !important;
}

.m-12 {
  margin: 0.75rem !important;
}

.m-18 {
  margin: 1.125rem !important;
}

.m-20 {
  margin: 1.25rem !important;
}

.m-30 {
  margin: 1.875rem !important;
}

.m-40 {
  margin: 2.5rem !important;
}

.m-auto {
  margin: auto !important;
}

.mx-0 {
  margin-right: 0 !important;
  margin-left: 0 !important;
}

.mx-1 {
  margin-right: 0.25rem !important;
  margin-left: 0.25rem !important;
}

.mx-2 {
  margin-right: 0.5rem !important;
  margin-left: 0.5rem !important;
}

.mx-3 {
  margin-right: 1rem !important;
  margin-left: 1rem !important;
}

.mx-4 {
  margin-right: 1.5rem !important;
  margin-left: 1.5rem !important;
}

.mx-5 {
  margin-right: 3rem !important;
  margin-left: 3rem !important;
}

.mx-12 {
  margin-right: 0.75rem !important;
  margin-left: 0.75rem !important;
}

.mx-18 {
  margin-right: 1.125rem !important;
  margin-left: 1.125rem !important;
}

.mx-20 {
  margin-right: 1.25rem !important;
  margin-left: 1.25rem !important;
}

.mx-30 {
  margin-right: 1.875rem !important;
  margin-left: 1.875rem !important;
}

.mx-40 {
  margin-right: 2.5rem !important;
  margin-left: 2.5rem !important;
}

.mx-auto {
  margin-right: auto !important;
  margin-left: auto !important;
}

.my-0 {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

.my-1 {
  margin-top: 0.25rem !important;
  margin-bottom: 0.25rem !important;
}

.my-2 {
  margin-top: 0.5rem !important;
  margin-bottom: 0.5rem !important;
}

.my-3 {
  margin-top: 1rem !important;
  margin-bottom: 1rem !important;
}

.my-4 {
  margin-top: 1.5rem !important;
  margin-bottom: 1.5rem !important;
}

.my-5 {
  margin-top: 3rem !important;
  margin-bottom: 3rem !important;
}

.my-12 {
  margin-top: 0.75rem !important;
  margin-bottom: 0.75rem !important;
}

.my-18 {
  margin-top: 1.125rem !important;
  margin-bottom: 1.125rem !important;
}

.my-20 {
  margin-top: 1.25rem !important;
  margin-bottom: 1.25rem !important;
}

.my-30 {
  margin-top: 1.875rem !important;
  margin-bottom: 1.875rem !important;
}

.my-40 {
  margin-top: 2.5rem !important;
  margin-bottom: 2.5rem !important;
}

.my-auto {
  margin-top: auto !important;
  margin-bottom: auto !important;
}

.mt-0 {
  margin-top: 0 !important;
}

.mt-1 {
  margin-top: 0.25rem !important;
}

.mt-2 {
  margin-top: 0.5rem !important;
}

.mt-3 {
  margin-top: 1rem !important;
}

.mt-4 {
  margin-top: 1.5rem !important;
}

.mt-5 {
  margin-top: 3rem !important;
}

.mt-12 {
  margin-top: 0.75rem !important;
}

.mt-18 {
  margin-top: 1.125rem !important;
}

.mt-20 {
  margin-top: 1.25rem !important;
}

.mt-30 {
  margin-top: 1.875rem !important;
}

.mt-40 {
  margin-top: 2.5rem !important;
}

.mt-auto {
  margin-top: auto !important;
}

.me-0 {
  margin-right: 0 !important;
}

.me-1 {
  margin-right: 0.25rem !important;
}

.me-2 {
  margin-right: 0.5rem !important;
}

.me-3 {
  margin-right: 1rem !important;
}

.me-4 {
  margin-right: 1.5rem !important;
}

.me-5 {
  margin-right: 3rem !important;
}

.me-12 {
  margin-right: 0.75rem !important;
}

.me-18 {
  margin-right: 1.125rem !important;
}

.me-20 {
  margin-right: 1.25rem !important;
}

.me-30 {
  margin-right: 1.875rem !important;
}

.me-40 {
  margin-right: 2.5rem !important;
}

.me-auto {
  margin-right: auto !important;
}

.mb-0 {
  margin-bottom: 0 !important;
}

.mb-1 {
  margin-bottom: 0.25rem !important;
}

.mb-2 {
  margin-bottom: 0.5rem !important;
}

.mb-3 {
  margin-bottom: 1rem !important;
}

.mb-4 {
  margin-bottom: 1.5rem !important;
}

.mb-5 {
  margin-bottom: 3rem !important;
}

.mb-12 {
  margin-bottom: 0.75rem !important;
}

.mb-18 {
  margin-bottom: 1.125rem !important;
}

.mb-20 {
  margin-bottom: 1.25rem !important;
}

.mb-30 {
  margin-bottom: 1.875rem !important;
}

.mb-40 {
  margin-bottom: 2.5rem !important;
}

.mb-auto {
  margin-bottom: auto !important;
}

.ms-0 {
  margin-left: 0 !important;
}

.ms-1 {
  margin-left: 0.25rem !important;
}

.ms-2 {
  margin-left: 0.5rem !important;
}

.ms-3 {
  margin-left: 1rem !important;
}

.ms-4 {
  margin-left: 1.5rem !important;
}

.ms-5 {
  margin-left: 3rem !important;
}

.ms-12 {
  margin-left: 0.75rem !important;
}

.ms-18 {
  margin-left: 1.125rem !important;
}

.ms-20 {
  margin-left: 1.25rem !important;
}

.ms-30 {
  margin-left: 1.875rem !important;
}

.ms-40 {
  margin-left: 2.5rem !important;
}

.ms-auto {
  margin-left: auto !important;
}

.p-0 {
  padding: 0 !important;
}

.p-1 {
  padding: 0.25rem !important;
}

.p-2 {
  padding: 0.5rem !important;
}

.p-3 {
  padding: 1rem !important;
}

.p-4 {
  padding: 1.5rem !important;
}

.p-5 {
  padding: 3rem !important;
}

.p-12 {
  padding: 0.75rem !important;
}

.p-18 {
  padding: 1.125rem !important;
}

.p-20 {
  padding: 1.25rem !important;
}

.p-30 {
  padding: 1.875rem !important;
}

.p-40 {
  padding: 2.5rem !important;
}

.px-0 {
  padding-right: 0 !important;
  padding-left: 0 !important;
}

.px-1 {
  padding-right: 0.25rem !important;
  padding-left: 0.25rem !important;
}

.px-2 {
  padding-right: 0.5rem !important;
  padding-left: 0.5rem !important;
}

.px-3 {
  padding-right: 1rem !important;
  padding-left: 1rem !important;
}

.px-4 {
  padding-right: 1.5rem !important;
  padding-left: 1.5rem !important;
}

.px-5 {
  padding-right: 3rem !important;
  padding-left: 3rem !important;
}

.px-12 {
  padding-right: 0.75rem !important;
  padding-left: 0.75rem !important;
}

.px-18 {
  padding-right: 1.125rem !important;
  padding-left: 1.125rem !important;
}

.px-20 {
  padding-right: 1.25rem !important;
  padding-left: 1.25rem !important;
}

.px-30 {
  padding-right: 1.875rem !important;
  padding-left: 1.875rem !important;
}

.px-40 {
  padding-right: 2.5rem !important;
  padding-left: 2.5rem !important;
}

.py-0 {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

.py-1 {
  padding-top: 0.25rem !important;
  padding-bottom: 0.25rem !important;
}

.py-2 {
  padding-top: 0.5rem !important;
  padding-bottom: 0.5rem !important;
}

.py-3 {
  padding-top: 1rem !important;
  padding-bottom: 1rem !important;
}

.py-4 {
  padding-top: 1.5rem !important;
  padding-bottom: 1.5rem !important;
}

.py-5 {
  padding-top: 3rem !important;
  padding-bottom: 3rem !important;
}

.py-12 {
  padding-top: 0.75rem !important;
  padding-bottom: 0.75rem !important;
}

.py-18 {
  padding-top: 1.125rem !important;
  padding-bottom: 1.125rem !important;
}

.py-20 {
  padding-top: 1.25rem !important;
  padding-bottom: 1.25rem !important;
}

.py-30 {
  padding-top: 1.875rem !important;
  padding-bottom: 1.875rem !important;
}

.py-40 {
  padding-top: 2.5rem !important;
  padding-bottom: 2.5rem !important;
}

.pt-0 {
  padding-top: 0 !important;
}

.pt-1 {
  padding-top: 0.25rem !important;
}

.pt-2 {
  padding-top: 0.5rem !important;
}

.pt-3 {
  padding-top: 1rem !important;
}

.pt-4 {
  padding-top: 1.5rem !important;
}

.pt-5 {
  padding-top: 3rem !important;
}

.pt-12 {
  padding-top: 0.75rem !important;
}

.pt-18 {
  padding-top: 1.125rem !important;
}

.pt-20 {
  padding-top: 1.25rem !important;
}

.pt-30 {
  padding-top: 1.875rem !important;
}

.pt-40 {
  padding-top: 2.5rem !important;
}

.pe-0 {
  padding-right: 0 !important;
}

.pe-1 {
  padding-right: 0.25rem !important;
}

.pe-2 {
  padding-right: 0.5rem !important;
}

.pe-3 {
  padding-right: 1rem !important;
}

.pe-4 {
  padding-right: 1.5rem !important;
}

.pe-5 {
  padding-right: 3rem !important;
}

.pe-12 {
  padding-right: 0.75rem !important;
}

.pe-18 {
  padding-right: 1.125rem !important;
}

.pe-20 {
  padding-right: 1.25rem !important;
}

.pe-30 {
  padding-right: 1.875rem !important;
}

.pe-40 {
  padding-right: 2.5rem !important;
}

.pb-0 {
  padding-bottom: 0 !important;
}

.pb-1 {
  padding-bottom: 0.25rem !important;
}

.pb-2 {
  padding-bottom: 0.5rem !important;
}

.pb-3 {
  padding-bottom: 1rem !important;
}

.pb-4 {
  padding-bottom: 1.5rem !important;
}

.pb-5 {
  padding-bottom: 3rem !important;
}

.pb-12 {
  padding-bottom: 0.75rem !important;
}

.pb-18 {
  padding-bottom: 1.125rem !important;
}

.pb-20 {
  padding-bottom: 1.25rem !important;
}

.pb-30 {
  padding-bottom: 1.875rem !important;
}

.pb-40 {
  padding-bottom: 2.5rem !important;
}

.ps-0 {
  padding-left: 0 !important;
}

.ps-1 {
  padding-left: 0.25rem !important;
}

.ps-2 {
  padding-left: 0.5rem !important;
}

.ps-3 {
  padding-left: 1rem !important;
}

.ps-4 {
  padding-left: 1.5rem !important;
}

.ps-5 {
  padding-left: 3rem !important;
}

.ps-12 {
  padding-left: 0.75rem !important;
}

.ps-18 {
  padding-left: 1.125rem !important;
}

.ps-20 {
  padding-left: 1.25rem !important;
}

.ps-30 {
  padding-left: 1.875rem !important;
}

.ps-40 {
  padding-left: 2.5rem !important;
}

.gap-0 {
  gap: 0 !important;
}

.gap-1 {
  gap: 0.25rem !important;
}

.gap-2 {
  gap: 0.5rem !important;
}

.gap-3 {
  gap: 1rem !important;
}

.gap-4 {
  gap: 1.5rem !important;
}

.gap-5 {
  gap: 3rem !important;
}

.gap-12 {
  gap: 0.75rem !important;
}

.gap-18 {
  gap: 1.125rem !important;
}

.gap-20 {
  gap: 1.25rem !important;
}

.gap-30 {
  gap: 1.875rem !important;
}

.gap-40 {
  gap: 2.5rem !important;
}

.font-monospace {
  font-family: var(--bs-font-monospace) !important;
}

.fs-1 {
  font-size: calc(1.375rem + 1.5vw) !important;
}

.fs-2 {
  font-size: calc(1.325rem + 0.9vw) !important;
}

.fs-3 {
  font-size: calc(1.3rem + 0.6vw) !important;
}

.fs-4 {
  font-size: calc(1.275rem + 0.3vw) !important;
}

.fs-5 {
  font-size: 1.25rem !important;
}

.fs-6 {
  font-size: 1rem !important;
}

.fst-italic {
  font-style: italic !important;
}

.fst-normal {
  font-style: normal !important;
}

.fw-light {
  font-weight: 300 !important;
}

.fw-lighter {
  font-weight: lighter !important;
}

.fw-normal {
  font-weight: 400 !important;
}

.fw-bold {
  font-weight: 700 !important;
}

.fw-semibold {
  font-weight: 600 !important;
}

.fw-bolder {
  font-weight: bolder !important;
}

.lh-1 {
  line-height: 1 !important;
}

.lh-sm {
  line-height: 1.25 !important;
}

.lh-base {
  line-height: 1.5 !important;
}

.lh-lg {
  line-height: 2 !important;
}

.text-start {
  text-align: left !important;
}

.text-end {
  text-align: right !important;
}

.text-center {
  text-align: center !important;
}

.text-decoration-none {
  text-decoration: none !important;
}

.text-decoration-underline {
  text-decoration: underline !important;
}

.text-decoration-line-through {
  text-decoration: line-through !important;
}

.text-lowercase {
  text-transform: lowercase !important;
}

.text-uppercase {
  text-transform: uppercase !important;
}

.text-capitalize {
  text-transform: capitalize !important;
}

.text-wrap {
  white-space: normal !important;
}

.text-nowrap {
  white-space: nowrap !important;
}

/* rtl:begin:remove */
.text-break {
  word-wrap: break-word !important;
  word-break: break-word !important;
}

/* rtl:end:remove */
.text-primary {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-primary-rgb), var(--bs-text-opacity)) !important;
}

.text-secondary {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-secondary-rgb), var(--bs-text-opacity)) !important;
}

.text-success {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-success-rgb), var(--bs-text-opacity)) !important;
}

.text-info {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-info-rgb), var(--bs-text-opacity)) !important;
}

.text-warning {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-warning-rgb), var(--bs-text-opacity)) !important;
}

.text-danger {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-danger-rgb), var(--bs-text-opacity)) !important;
}

.text-light {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-light-rgb), var(--bs-text-opacity)) !important;
}

.text-dark {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-dark-rgb), var(--bs-text-opacity)) !important;
}

.text-gray-ligth {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-gray-ligth-rgb), var(--bs-text-opacity)) !important;
}

.text-black {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-black-rgb), var(--bs-text-opacity)) !important;
}

.text-white {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-white-rgb), var(--bs-text-opacity)) !important;
}

.text-body {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-body-color-rgb), var(--bs-text-opacity)) !important;
}

.text-muted {
  --bs-text-opacity: 1;
  color: #6c757d !important;
}

.text-black-50 {
  --bs-text-opacity: 1;
  color: rgba(0, 0, 0, 0.5) !important;
}

.text-white-50 {
  --bs-text-opacity: 1;
  color: rgba(255, 255, 255, 0.5) !important;
}

.text-reset {
  --bs-text-opacity: 1;
  color: inherit !important;
}

.text-opacity-25 {
  --bs-text-opacity: 0.25;
}

.text-opacity-50 {
  --bs-text-opacity: 0.5;
}

.text-opacity-75 {
  --bs-text-opacity: 0.75;
}

.text-opacity-100 {
  --bs-text-opacity: 1;
}

.bg-primary {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-primary-rgb), var(--bs-bg-opacity)) !important;
}

.bg-secondary {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-secondary-rgb), var(--bs-bg-opacity)) !important;
}

.bg-success {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-success-rgb), var(--bs-bg-opacity)) !important;
}

.bg-info {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-info-rgb), var(--bs-bg-opacity)) !important;
}

.bg-warning {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-warning-rgb), var(--bs-bg-opacity)) !important;
}

.bg-danger {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-danger-rgb), var(--bs-bg-opacity)) !important;
}

.bg-light {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-light-rgb), var(--bs-bg-opacity)) !important;
}

.bg-dark {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-dark-rgb), var(--bs-bg-opacity)) !important;
}

.bg-gray-ligth {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-gray-ligth-rgb), var(--bs-bg-opacity)) !important;
}

.bg-black {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-black-rgb), var(--bs-bg-opacity)) !important;
}

.bg-white {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-white-rgb), var(--bs-bg-opacity)) !important;
}

.bg-body {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-body-bg-rgb), var(--bs-bg-opacity)) !important;
}

.bg-transparent {
  --bs-bg-opacity: 1;
  background-color: transparent !important;
}

.bg-opacity-10 {
  --bs-bg-opacity: 0.1;
}

.bg-opacity-25 {
  --bs-bg-opacity: 0.25;
}

.bg-opacity-50 {
  --bs-bg-opacity: 0.5;
}

.bg-opacity-75 {
  --bs-bg-opacity: 0.75;
}

.bg-opacity-100 {
  --bs-bg-opacity: 1;
}

.bg-gradient {
  background-image: var(--bs-gradient) !important;
}

.user-select-all {
  -webkit-user-select: all !important;
     -moz-user-select: all !important;
          user-select: all !important;
}

.user-select-auto {
  -webkit-user-select: auto !important;
     -moz-user-select: auto !important;
          user-select: auto !important;
}

.user-select-none {
  -webkit-user-select: none !important;
     -moz-user-select: none !important;
          user-select: none !important;
}

.pe-none {
  pointer-events: none !important;
}

.pe-auto {
  pointer-events: auto !important;
}

.rounded {
  border-radius: var(--bs-border-radius) !important;
}

.rounded-0 {
  border-radius: 0 !important;
}

.rounded-1 {
  border-radius: var(--bs-border-radius-sm) !important;
}

.rounded-2 {
  border-radius: var(--bs-border-radius) !important;
}

.rounded-3 {
  border-radius: var(--bs-border-radius-lg) !important;
}

.rounded-4 {
  border-radius: var(--bs-border-radius-xl) !important;
}

.rounded-5 {
  border-radius: var(--bs-border-radius-2xl) !important;
}

.rounded-circle {
  border-radius: 50% !important;
}

.rounded-pill {
  border-radius: var(--bs-border-radius-pill) !important;
}

.rounded-top {
  border-top-left-radius: var(--bs-border-radius) !important;
  border-top-right-radius: var(--bs-border-radius) !important;
}

.rounded-end {
  border-top-right-radius: var(--bs-border-radius) !important;
  border-bottom-right-radius: var(--bs-border-radius) !important;
}

.rounded-bottom {
  border-bottom-right-radius: var(--bs-border-radius) !important;
  border-bottom-left-radius: var(--bs-border-radius) !important;
}

.rounded-start {
  border-bottom-left-radius: var(--bs-border-radius) !important;
  border-top-left-radius: var(--bs-border-radius) !important;
}

.visible {
  visibility: visible !important;
}

.invisible {
  visibility: hidden !important;
}

@media (min-width: 576px) {
  .float-sm-start {
    float: left !important;
  }
  .float-sm-end {
    float: right !important;
  }
  .float-sm-none {
    float: none !important;
  }
  .d-sm-inline {
    display: inline !important;
  }
  .d-sm-inline-block {
    display: inline-block !important;
  }
  .d-sm-block {
    display: block !important;
  }
  .d-sm-grid {
    display: grid !important;
  }
  .d-sm-table {
    display: table !important;
  }
  .d-sm-table-row {
    display: table-row !important;
  }
  .d-sm-table-cell {
    display: table-cell !important;
  }
  .d-sm-flex {
    display: flex !important;
  }
  .d-sm-inline-flex {
    display: inline-flex !important;
  }
  .d-sm-none {
    display: none !important;
  }
  .flex-sm-fill {
    flex: 1 1 auto !important;
  }
  .flex-sm-row {
    flex-direction: row !important;
  }
  .flex-sm-column {
    flex-direction: column !important;
  }
  .flex-sm-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-sm-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-sm-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-sm-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-sm-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-sm-shrink-1 {
    flex-shrink: 1 !important;
  }
  .flex-sm-wrap {
    flex-wrap: wrap !important;
  }
  .flex-sm-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-sm-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .justify-content-sm-start {
    justify-content: flex-start !important;
  }
  .justify-content-sm-end {
    justify-content: flex-end !important;
  }
  .justify-content-sm-center {
    justify-content: center !important;
  }
  .justify-content-sm-between {
    justify-content: space-between !important;
  }
  .justify-content-sm-around {
    justify-content: space-around !important;
  }
  .justify-content-sm-evenly {
    justify-content: space-evenly !important;
  }
  .align-items-sm-start {
    align-items: flex-start !important;
  }
  .align-items-sm-end {
    align-items: flex-end !important;
  }
  .align-items-sm-center {
    align-items: center !important;
  }
  .align-items-sm-baseline {
    align-items: baseline !important;
  }
  .align-items-sm-stretch {
    align-items: stretch !important;
  }
  .align-content-sm-start {
    align-content: flex-start !important;
  }
  .align-content-sm-end {
    align-content: flex-end !important;
  }
  .align-content-sm-center {
    align-content: center !important;
  }
  .align-content-sm-between {
    align-content: space-between !important;
  }
  .align-content-sm-around {
    align-content: space-around !important;
  }
  .align-content-sm-stretch {
    align-content: stretch !important;
  }
  .align-self-sm-auto {
    align-self: auto !important;
  }
  .align-self-sm-start {
    align-self: flex-start !important;
  }
  .align-self-sm-end {
    align-self: flex-end !important;
  }
  .align-self-sm-center {
    align-self: center !important;
  }
  .align-self-sm-baseline {
    align-self: baseline !important;
  }
  .align-self-sm-stretch {
    align-self: stretch !important;
  }
  .order-sm-first {
    order: -1 !important;
  }
  .order-sm-0 {
    order: 0 !important;
  }
  .order-sm-1 {
    order: 1 !important;
  }
  .order-sm-2 {
    order: 2 !important;
  }
  .order-sm-3 {
    order: 3 !important;
  }
  .order-sm-4 {
    order: 4 !important;
  }
  .order-sm-5 {
    order: 5 !important;
  }
  .order-sm-last {
    order: 6 !important;
  }
  .m-sm-0 {
    margin: 0 !important;
  }
  .m-sm-1 {
    margin: 0.25rem !important;
  }
  .m-sm-2 {
    margin: 0.5rem !important;
  }
  .m-sm-3 {
    margin: 1rem !important;
  }
  .m-sm-4 {
    margin: 1.5rem !important;
  }
  .m-sm-5 {
    margin: 3rem !important;
  }
  .m-sm-12 {
    margin: 0.75rem !important;
  }
  .m-sm-18 {
    margin: 1.125rem !important;
  }
  .m-sm-20 {
    margin: 1.25rem !important;
  }
  .m-sm-30 {
    margin: 1.875rem !important;
  }
  .m-sm-40 {
    margin: 2.5rem !important;
  }
  .m-sm-auto {
    margin: auto !important;
  }
  .mx-sm-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }
  .mx-sm-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important;
  }
  .mx-sm-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important;
  }
  .mx-sm-3 {
    margin-right: 1rem !important;
    margin-left: 1rem !important;
  }
  .mx-sm-4 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important;
  }
  .mx-sm-5 {
    margin-right: 3rem !important;
    margin-left: 3rem !important;
  }
  .mx-sm-12 {
    margin-right: 0.75rem !important;
    margin-left: 0.75rem !important;
  }
  .mx-sm-18 {
    margin-right: 1.125rem !important;
    margin-left: 1.125rem !important;
  }
  .mx-sm-20 {
    margin-right: 1.25rem !important;
    margin-left: 1.25rem !important;
  }
  .mx-sm-30 {
    margin-right: 1.875rem !important;
    margin-left: 1.875rem !important;
  }
  .mx-sm-40 {
    margin-right: 2.5rem !important;
    margin-left: 2.5rem !important;
  }
  .mx-sm-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }
  .my-sm-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }
  .my-sm-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important;
  }
  .my-sm-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }
  .my-sm-3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
  }
  .my-sm-4 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important;
  }
  .my-sm-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important;
  }
  .my-sm-12 {
    margin-top: 0.75rem !important;
    margin-bottom: 0.75rem !important;
  }
  .my-sm-18 {
    margin-top: 1.125rem !important;
    margin-bottom: 1.125rem !important;
  }
  .my-sm-20 {
    margin-top: 1.25rem !important;
    margin-bottom: 1.25rem !important;
  }
  .my-sm-30 {
    margin-top: 1.875rem !important;
    margin-bottom: 1.875rem !important;
  }
  .my-sm-40 {
    margin-top: 2.5rem !important;
    margin-bottom: 2.5rem !important;
  }
  .my-sm-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
  .mt-sm-0 {
    margin-top: 0 !important;
  }
  .mt-sm-1 {
    margin-top: 0.25rem !important;
  }
  .mt-sm-2 {
    margin-top: 0.5rem !important;
  }
  .mt-sm-3 {
    margin-top: 1rem !important;
  }
  .mt-sm-4 {
    margin-top: 1.5rem !important;
  }
  .mt-sm-5 {
    margin-top: 3rem !important;
  }
  .mt-sm-12 {
    margin-top: 0.75rem !important;
  }
  .mt-sm-18 {
    margin-top: 1.125rem !important;
  }
  .mt-sm-20 {
    margin-top: 1.25rem !important;
  }
  .mt-sm-30 {
    margin-top: 1.875rem !important;
  }
  .mt-sm-40 {
    margin-top: 2.5rem !important;
  }
  .mt-sm-auto {
    margin-top: auto !important;
  }
  .me-sm-0 {
    margin-right: 0 !important;
  }
  .me-sm-1 {
    margin-right: 0.25rem !important;
  }
  .me-sm-2 {
    margin-right: 0.5rem !important;
  }
  .me-sm-3 {
    margin-right: 1rem !important;
  }
  .me-sm-4 {
    margin-right: 1.5rem !important;
  }
  .me-sm-5 {
    margin-right: 3rem !important;
  }
  .me-sm-12 {
    margin-right: 0.75rem !important;
  }
  .me-sm-18 {
    margin-right: 1.125rem !important;
  }
  .me-sm-20 {
    margin-right: 1.25rem !important;
  }
  .me-sm-30 {
    margin-right: 1.875rem !important;
  }
  .me-sm-40 {
    margin-right: 2.5rem !important;
  }
  .me-sm-auto {
    margin-right: auto !important;
  }
  .mb-sm-0 {
    margin-bottom: 0 !important;
  }
  .mb-sm-1 {
    margin-bottom: 0.25rem !important;
  }
  .mb-sm-2 {
    margin-bottom: 0.5rem !important;
  }
  .mb-sm-3 {
    margin-bottom: 1rem !important;
  }
  .mb-sm-4 {
    margin-bottom: 1.5rem !important;
  }
  .mb-sm-5 {
    margin-bottom: 3rem !important;
  }
  .mb-sm-12 {
    margin-bottom: 0.75rem !important;
  }
  .mb-sm-18 {
    margin-bottom: 1.125rem !important;
  }
  .mb-sm-20 {
    margin-bottom: 1.25rem !important;
  }
  .mb-sm-30 {
    margin-bottom: 1.875rem !important;
  }
  .mb-sm-40 {
    margin-bottom: 2.5rem !important;
  }
  .mb-sm-auto {
    margin-bottom: auto !important;
  }
  .ms-sm-0 {
    margin-left: 0 !important;
  }
  .ms-sm-1 {
    margin-left: 0.25rem !important;
  }
  .ms-sm-2 {
    margin-left: 0.5rem !important;
  }
  .ms-sm-3 {
    margin-left: 1rem !important;
  }
  .ms-sm-4 {
    margin-left: 1.5rem !important;
  }
  .ms-sm-5 {
    margin-left: 3rem !important;
  }
  .ms-sm-12 {
    margin-left: 0.75rem !important;
  }
  .ms-sm-18 {
    margin-left: 1.125rem !important;
  }
  .ms-sm-20 {
    margin-left: 1.25rem !important;
  }
  .ms-sm-30 {
    margin-left: 1.875rem !important;
  }
  .ms-sm-40 {
    margin-left: 2.5rem !important;
  }
  .ms-sm-auto {
    margin-left: auto !important;
  }
  .p-sm-0 {
    padding: 0 !important;
  }
  .p-sm-1 {
    padding: 0.25rem !important;
  }
  .p-sm-2 {
    padding: 0.5rem !important;
  }
  .p-sm-3 {
    padding: 1rem !important;
  }
  .p-sm-4 {
    padding: 1.5rem !important;
  }
  .p-sm-5 {
    padding: 3rem !important;
  }
  .p-sm-12 {
    padding: 0.75rem !important;
  }
  .p-sm-18 {
    padding: 1.125rem !important;
  }
  .p-sm-20 {
    padding: 1.25rem !important;
  }
  .p-sm-30 {
    padding: 1.875rem !important;
  }
  .p-sm-40 {
    padding: 2.5rem !important;
  }
  .px-sm-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }
  .px-sm-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important;
  }
  .px-sm-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important;
  }
  .px-sm-3 {
    padding-right: 1rem !important;
    padding-left: 1rem !important;
  }
  .px-sm-4 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important;
  }
  .px-sm-5 {
    padding-right: 3rem !important;
    padding-left: 3rem !important;
  }
  .px-sm-12 {
    padding-right: 0.75rem !important;
    padding-left: 0.75rem !important;
  }
  .px-sm-18 {
    padding-right: 1.125rem !important;
    padding-left: 1.125rem !important;
  }
  .px-sm-20 {
    padding-right: 1.25rem !important;
    padding-left: 1.25rem !important;
  }
  .px-sm-30 {
    padding-right: 1.875rem !important;
    padding-left: 1.875rem !important;
  }
  .px-sm-40 {
    padding-right: 2.5rem !important;
    padding-left: 2.5rem !important;
  }
  .py-sm-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
  .py-sm-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important;
  }
  .py-sm-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }
  .py-sm-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }
  .py-sm-4 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
  }
  .py-sm-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }
  .py-sm-12 {
    padding-top: 0.75rem !important;
    padding-bottom: 0.75rem !important;
  }
  .py-sm-18 {
    padding-top: 1.125rem !important;
    padding-bottom: 1.125rem !important;
  }
  .py-sm-20 {
    padding-top: 1.25rem !important;
    padding-bottom: 1.25rem !important;
  }
  .py-sm-30 {
    padding-top: 1.875rem !important;
    padding-bottom: 1.875rem !important;
  }
  .py-sm-40 {
    padding-top: 2.5rem !important;
    padding-bottom: 2.5rem !important;
  }
  .pt-sm-0 {
    padding-top: 0 !important;
  }
  .pt-sm-1 {
    padding-top: 0.25rem !important;
  }
  .pt-sm-2 {
    padding-top: 0.5rem !important;
  }
  .pt-sm-3 {
    padding-top: 1rem !important;
  }
  .pt-sm-4 {
    padding-top: 1.5rem !important;
  }
  .pt-sm-5 {
    padding-top: 3rem !important;
  }
  .pt-sm-12 {
    padding-top: 0.75rem !important;
  }
  .pt-sm-18 {
    padding-top: 1.125rem !important;
  }
  .pt-sm-20 {
    padding-top: 1.25rem !important;
  }
  .pt-sm-30 {
    padding-top: 1.875rem !important;
  }
  .pt-sm-40 {
    padding-top: 2.5rem !important;
  }
  .pe-sm-0 {
    padding-right: 0 !important;
  }
  .pe-sm-1 {
    padding-right: 0.25rem !important;
  }
  .pe-sm-2 {
    padding-right: 0.5rem !important;
  }
  .pe-sm-3 {
    padding-right: 1rem !important;
  }
  .pe-sm-4 {
    padding-right: 1.5rem !important;
  }
  .pe-sm-5 {
    padding-right: 3rem !important;
  }
  .pe-sm-12 {
    padding-right: 0.75rem !important;
  }
  .pe-sm-18 {
    padding-right: 1.125rem !important;
  }
  .pe-sm-20 {
    padding-right: 1.25rem !important;
  }
  .pe-sm-30 {
    padding-right: 1.875rem !important;
  }
  .pe-sm-40 {
    padding-right: 2.5rem !important;
  }
  .pb-sm-0 {
    padding-bottom: 0 !important;
  }
  .pb-sm-1 {
    padding-bottom: 0.25rem !important;
  }
  .pb-sm-2 {
    padding-bottom: 0.5rem !important;
  }
  .pb-sm-3 {
    padding-bottom: 1rem !important;
  }
  .pb-sm-4 {
    padding-bottom: 1.5rem !important;
  }
  .pb-sm-5 {
    padding-bottom: 3rem !important;
  }
  .pb-sm-12 {
    padding-bottom: 0.75rem !important;
  }
  .pb-sm-18 {
    padding-bottom: 1.125rem !important;
  }
  .pb-sm-20 {
    padding-bottom: 1.25rem !important;
  }
  .pb-sm-30 {
    padding-bottom: 1.875rem !important;
  }
  .pb-sm-40 {
    padding-bottom: 2.5rem !important;
  }
  .ps-sm-0 {
    padding-left: 0 !important;
  }
  .ps-sm-1 {
    padding-left: 0.25rem !important;
  }
  .ps-sm-2 {
    padding-left: 0.5rem !important;
  }
  .ps-sm-3 {
    padding-left: 1rem !important;
  }
  .ps-sm-4 {
    padding-left: 1.5rem !important;
  }
  .ps-sm-5 {
    padding-left: 3rem !important;
  }
  .ps-sm-12 {
    padding-left: 0.75rem !important;
  }
  .ps-sm-18 {
    padding-left: 1.125rem !important;
  }
  .ps-sm-20 {
    padding-left: 1.25rem !important;
  }
  .ps-sm-30 {
    padding-left: 1.875rem !important;
  }
  .ps-sm-40 {
    padding-left: 2.5rem !important;
  }
  .gap-sm-0 {
    gap: 0 !important;
  }
  .gap-sm-1 {
    gap: 0.25rem !important;
  }
  .gap-sm-2 {
    gap: 0.5rem !important;
  }
  .gap-sm-3 {
    gap: 1rem !important;
  }
  .gap-sm-4 {
    gap: 1.5rem !important;
  }
  .gap-sm-5 {
    gap: 3rem !important;
  }
  .gap-sm-12 {
    gap: 0.75rem !important;
  }
  .gap-sm-18 {
    gap: 1.125rem !important;
  }
  .gap-sm-20 {
    gap: 1.25rem !important;
  }
  .gap-sm-30 {
    gap: 1.875rem !important;
  }
  .gap-sm-40 {
    gap: 2.5rem !important;
  }
  .text-sm-start {
    text-align: left !important;
  }
  .text-sm-end {
    text-align: right !important;
  }
  .text-sm-center {
    text-align: center !important;
  }
}
@media (min-width: 768px) {
  .float-md-start {
    float: left !important;
  }
  .float-md-end {
    float: right !important;
  }
  .float-md-none {
    float: none !important;
  }
  .d-md-inline {
    display: inline !important;
  }
  .d-md-inline-block {
    display: inline-block !important;
  }
  .d-md-block {
    display: block !important;
  }
  .d-md-grid {
    display: grid !important;
  }
  .d-md-table {
    display: table !important;
  }
  .d-md-table-row {
    display: table-row !important;
  }
  .d-md-table-cell {
    display: table-cell !important;
  }
  .d-md-flex {
    display: flex !important;
  }
  .d-md-inline-flex {
    display: inline-flex !important;
  }
  .d-md-none {
    display: none !important;
  }
  .flex-md-fill {
    flex: 1 1 auto !important;
  }
  .flex-md-row {
    flex-direction: row !important;
  }
  .flex-md-column {
    flex-direction: column !important;
  }
  .flex-md-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-md-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-md-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-md-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-md-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-md-shrink-1 {
    flex-shrink: 1 !important;
  }
  .flex-md-wrap {
    flex-wrap: wrap !important;
  }
  .flex-md-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-md-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .justify-content-md-start {
    justify-content: flex-start !important;
  }
  .justify-content-md-end {
    justify-content: flex-end !important;
  }
  .justify-content-md-center {
    justify-content: center !important;
  }
  .justify-content-md-between {
    justify-content: space-between !important;
  }
  .justify-content-md-around {
    justify-content: space-around !important;
  }
  .justify-content-md-evenly {
    justify-content: space-evenly !important;
  }
  .align-items-md-start {
    align-items: flex-start !important;
  }
  .align-items-md-end {
    align-items: flex-end !important;
  }
  .align-items-md-center {
    align-items: center !important;
  }
  .align-items-md-baseline {
    align-items: baseline !important;
  }
  .align-items-md-stretch {
    align-items: stretch !important;
  }
  .align-content-md-start {
    align-content: flex-start !important;
  }
  .align-content-md-end {
    align-content: flex-end !important;
  }
  .align-content-md-center {
    align-content: center !important;
  }
  .align-content-md-between {
    align-content: space-between !important;
  }
  .align-content-md-around {
    align-content: space-around !important;
  }
  .align-content-md-stretch {
    align-content: stretch !important;
  }
  .align-self-md-auto {
    align-self: auto !important;
  }
  .align-self-md-start {
    align-self: flex-start !important;
  }
  .align-self-md-end {
    align-self: flex-end !important;
  }
  .align-self-md-center {
    align-self: center !important;
  }
  .align-self-md-baseline {
    align-self: baseline !important;
  }
  .align-self-md-stretch {
    align-self: stretch !important;
  }
  .order-md-first {
    order: -1 !important;
  }
  .order-md-0 {
    order: 0 !important;
  }
  .order-md-1 {
    order: 1 !important;
  }
  .order-md-2 {
    order: 2 !important;
  }
  .order-md-3 {
    order: 3 !important;
  }
  .order-md-4 {
    order: 4 !important;
  }
  .order-md-5 {
    order: 5 !important;
  }
  .order-md-last {
    order: 6 !important;
  }
  .m-md-0 {
    margin: 0 !important;
  }
  .m-md-1 {
    margin: 0.25rem !important;
  }
  .m-md-2 {
    margin: 0.5rem !important;
  }
  .m-md-3 {
    margin: 1rem !important;
  }
  .m-md-4 {
    margin: 1.5rem !important;
  }
  .m-md-5 {
    margin: 3rem !important;
  }
  .m-md-12 {
    margin: 0.75rem !important;
  }
  .m-md-18 {
    margin: 1.125rem !important;
  }
  .m-md-20 {
    margin: 1.25rem !important;
  }
  .m-md-30 {
    margin: 1.875rem !important;
  }
  .m-md-40 {
    margin: 2.5rem !important;
  }
  .m-md-auto {
    margin: auto !important;
  }
  .mx-md-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }
  .mx-md-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important;
  }
  .mx-md-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important;
  }
  .mx-md-3 {
    margin-right: 1rem !important;
    margin-left: 1rem !important;
  }
  .mx-md-4 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important;
  }
  .mx-md-5 {
    margin-right: 3rem !important;
    margin-left: 3rem !important;
  }
  .mx-md-12 {
    margin-right: 0.75rem !important;
    margin-left: 0.75rem !important;
  }
  .mx-md-18 {
    margin-right: 1.125rem !important;
    margin-left: 1.125rem !important;
  }
  .mx-md-20 {
    margin-right: 1.25rem !important;
    margin-left: 1.25rem !important;
  }
  .mx-md-30 {
    margin-right: 1.875rem !important;
    margin-left: 1.875rem !important;
  }
  .mx-md-40 {
    margin-right: 2.5rem !important;
    margin-left: 2.5rem !important;
  }
  .mx-md-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }
  .my-md-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }
  .my-md-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important;
  }
  .my-md-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }
  .my-md-3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
  }
  .my-md-4 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important;
  }
  .my-md-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important;
  }
  .my-md-12 {
    margin-top: 0.75rem !important;
    margin-bottom: 0.75rem !important;
  }
  .my-md-18 {
    margin-top: 1.125rem !important;
    margin-bottom: 1.125rem !important;
  }
  .my-md-20 {
    margin-top: 1.25rem !important;
    margin-bottom: 1.25rem !important;
  }
  .my-md-30 {
    margin-top: 1.875rem !important;
    margin-bottom: 1.875rem !important;
  }
  .my-md-40 {
    margin-top: 2.5rem !important;
    margin-bottom: 2.5rem !important;
  }
  .my-md-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
  .mt-md-0 {
    margin-top: 0 !important;
  }
  .mt-md-1 {
    margin-top: 0.25rem !important;
  }
  .mt-md-2 {
    margin-top: 0.5rem !important;
  }
  .mt-md-3 {
    margin-top: 1rem !important;
  }
  .mt-md-4 {
    margin-top: 1.5rem !important;
  }
  .mt-md-5 {
    margin-top: 3rem !important;
  }
  .mt-md-12 {
    margin-top: 0.75rem !important;
  }
  .mt-md-18 {
    margin-top: 1.125rem !important;
  }
  .mt-md-20 {
    margin-top: 1.25rem !important;
  }
  .mt-md-30 {
    margin-top: 1.875rem !important;
  }
  .mt-md-40 {
    margin-top: 2.5rem !important;
  }
  .mt-md-auto {
    margin-top: auto !important;
  }
  .me-md-0 {
    margin-right: 0 !important;
  }
  .me-md-1 {
    margin-right: 0.25rem !important;
  }
  .me-md-2 {
    margin-right: 0.5rem !important;
  }
  .me-md-3 {
    margin-right: 1rem !important;
  }
  .me-md-4 {
    margin-right: 1.5rem !important;
  }
  .me-md-5 {
    margin-right: 3rem !important;
  }
  .me-md-12 {
    margin-right: 0.75rem !important;
  }
  .me-md-18 {
    margin-right: 1.125rem !important;
  }
  .me-md-20 {
    margin-right: 1.25rem !important;
  }
  .me-md-30 {
    margin-right: 1.875rem !important;
  }
  .me-md-40 {
    margin-right: 2.5rem !important;
  }
  .me-md-auto {
    margin-right: auto !important;
  }
  .mb-md-0 {
    margin-bottom: 0 !important;
  }
  .mb-md-1 {
    margin-bottom: 0.25rem !important;
  }
  .mb-md-2 {
    margin-bottom: 0.5rem !important;
  }
  .mb-md-3 {
    margin-bottom: 1rem !important;
  }
  .mb-md-4 {
    margin-bottom: 1.5rem !important;
  }
  .mb-md-5 {
    margin-bottom: 3rem !important;
  }
  .mb-md-12 {
    margin-bottom: 0.75rem !important;
  }
  .mb-md-18 {
    margin-bottom: 1.125rem !important;
  }
  .mb-md-20 {
    margin-bottom: 1.25rem !important;
  }
  .mb-md-30 {
    margin-bottom: 1.875rem !important;
  }
  .mb-md-40 {
    margin-bottom: 2.5rem !important;
  }
  .mb-md-auto {
    margin-bottom: auto !important;
  }
  .ms-md-0 {
    margin-left: 0 !important;
  }
  .ms-md-1 {
    margin-left: 0.25rem !important;
  }
  .ms-md-2 {
    margin-left: 0.5rem !important;
  }
  .ms-md-3 {
    margin-left: 1rem !important;
  }
  .ms-md-4 {
    margin-left: 1.5rem !important;
  }
  .ms-md-5 {
    margin-left: 3rem !important;
  }
  .ms-md-12 {
    margin-left: 0.75rem !important;
  }
  .ms-md-18 {
    margin-left: 1.125rem !important;
  }
  .ms-md-20 {
    margin-left: 1.25rem !important;
  }
  .ms-md-30 {
    margin-left: 1.875rem !important;
  }
  .ms-md-40 {
    margin-left: 2.5rem !important;
  }
  .ms-md-auto {
    margin-left: auto !important;
  }
  .p-md-0 {
    padding: 0 !important;
  }
  .p-md-1 {
    padding: 0.25rem !important;
  }
  .p-md-2 {
    padding: 0.5rem !important;
  }
  .p-md-3 {
    padding: 1rem !important;
  }
  .p-md-4 {
    padding: 1.5rem !important;
  }
  .p-md-5 {
    padding: 3rem !important;
  }
  .p-md-12 {
    padding: 0.75rem !important;
  }
  .p-md-18 {
    padding: 1.125rem !important;
  }
  .p-md-20 {
    padding: 1.25rem !important;
  }
  .p-md-30 {
    padding: 1.875rem !important;
  }
  .p-md-40 {
    padding: 2.5rem !important;
  }
  .px-md-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }
  .px-md-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important;
  }
  .px-md-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important;
  }
  .px-md-3 {
    padding-right: 1rem !important;
    padding-left: 1rem !important;
  }
  .px-md-4 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important;
  }
  .px-md-5 {
    padding-right: 3rem !important;
    padding-left: 3rem !important;
  }
  .px-md-12 {
    padding-right: 0.75rem !important;
    padding-left: 0.75rem !important;
  }
  .px-md-18 {
    padding-right: 1.125rem !important;
    padding-left: 1.125rem !important;
  }
  .px-md-20 {
    padding-right: 1.25rem !important;
    padding-left: 1.25rem !important;
  }
  .px-md-30 {
    padding-right: 1.875rem !important;
    padding-left: 1.875rem !important;
  }
  .px-md-40 {
    padding-right: 2.5rem !important;
    padding-left: 2.5rem !important;
  }
  .py-md-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
  .py-md-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important;
  }
  .py-md-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }
  .py-md-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }
  .py-md-4 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
  }
  .py-md-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }
  .py-md-12 {
    padding-top: 0.75rem !important;
    padding-bottom: 0.75rem !important;
  }
  .py-md-18 {
    padding-top: 1.125rem !important;
    padding-bottom: 1.125rem !important;
  }
  .py-md-20 {
    padding-top: 1.25rem !important;
    padding-bottom: 1.25rem !important;
  }
  .py-md-30 {
    padding-top: 1.875rem !important;
    padding-bottom: 1.875rem !important;
  }
  .py-md-40 {
    padding-top: 2.5rem !important;
    padding-bottom: 2.5rem !important;
  }
  .pt-md-0 {
    padding-top: 0 !important;
  }
  .pt-md-1 {
    padding-top: 0.25rem !important;
  }
  .pt-md-2 {
    padding-top: 0.5rem !important;
  }
  .pt-md-3 {
    padding-top: 1rem !important;
  }
  .pt-md-4 {
    padding-top: 1.5rem !important;
  }
  .pt-md-5 {
    padding-top: 3rem !important;
  }
  .pt-md-12 {
    padding-top: 0.75rem !important;
  }
  .pt-md-18 {
    padding-top: 1.125rem !important;
  }
  .pt-md-20 {
    padding-top: 1.25rem !important;
  }
  .pt-md-30 {
    padding-top: 1.875rem !important;
  }
  .pt-md-40 {
    padding-top: 2.5rem !important;
  }
  .pe-md-0 {
    padding-right: 0 !important;
  }
  .pe-md-1 {
    padding-right: 0.25rem !important;
  }
  .pe-md-2 {
    padding-right: 0.5rem !important;
  }
  .pe-md-3 {
    padding-right: 1rem !important;
  }
  .pe-md-4 {
    padding-right: 1.5rem !important;
  }
  .pe-md-5 {
    padding-right: 3rem !important;
  }
  .pe-md-12 {
    padding-right: 0.75rem !important;
  }
  .pe-md-18 {
    padding-right: 1.125rem !important;
  }
  .pe-md-20 {
    padding-right: 1.25rem !important;
  }
  .pe-md-30 {
    padding-right: 1.875rem !important;
  }
  .pe-md-40 {
    padding-right: 2.5rem !important;
  }
  .pb-md-0 {
    padding-bottom: 0 !important;
  }
  .pb-md-1 {
    padding-bottom: 0.25rem !important;
  }
  .pb-md-2 {
    padding-bottom: 0.5rem !important;
  }
  .pb-md-3 {
    padding-bottom: 1rem !important;
  }
  .pb-md-4 {
    padding-bottom: 1.5rem !important;
  }
  .pb-md-5 {
    padding-bottom: 3rem !important;
  }
  .pb-md-12 {
    padding-bottom: 0.75rem !important;
  }
  .pb-md-18 {
    padding-bottom: 1.125rem !important;
  }
  .pb-md-20 {
    padding-bottom: 1.25rem !important;
  }
  .pb-md-30 {
    padding-bottom: 1.875rem !important;
  }
  .pb-md-40 {
    padding-bottom: 2.5rem !important;
  }
  .ps-md-0 {
    padding-left: 0 !important;
  }
  .ps-md-1 {
    padding-left: 0.25rem !important;
  }
  .ps-md-2 {
    padding-left: 0.5rem !important;
  }
  .ps-md-3 {
    padding-left: 1rem !important;
  }
  .ps-md-4 {
    padding-left: 1.5rem !important;
  }
  .ps-md-5 {
    padding-left: 3rem !important;
  }
  .ps-md-12 {
    padding-left: 0.75rem !important;
  }
  .ps-md-18 {
    padding-left: 1.125rem !important;
  }
  .ps-md-20 {
    padding-left: 1.25rem !important;
  }
  .ps-md-30 {
    padding-left: 1.875rem !important;
  }
  .ps-md-40 {
    padding-left: 2.5rem !important;
  }
  .gap-md-0 {
    gap: 0 !important;
  }
  .gap-md-1 {
    gap: 0.25rem !important;
  }
  .gap-md-2 {
    gap: 0.5rem !important;
  }
  .gap-md-3 {
    gap: 1rem !important;
  }
  .gap-md-4 {
    gap: 1.5rem !important;
  }
  .gap-md-5 {
    gap: 3rem !important;
  }
  .gap-md-12 {
    gap: 0.75rem !important;
  }
  .gap-md-18 {
    gap: 1.125rem !important;
  }
  .gap-md-20 {
    gap: 1.25rem !important;
  }
  .gap-md-30 {
    gap: 1.875rem !important;
  }
  .gap-md-40 {
    gap: 2.5rem !important;
  }
  .text-md-start {
    text-align: left !important;
  }
  .text-md-end {
    text-align: right !important;
  }
  .text-md-center {
    text-align: center !important;
  }
}
@media (min-width: 992px) {
  .float-lg-start {
    float: left !important;
  }
  .float-lg-end {
    float: right !important;
  }
  .float-lg-none {
    float: none !important;
  }
  .d-lg-inline {
    display: inline !important;
  }
  .d-lg-inline-block {
    display: inline-block !important;
  }
  .d-lg-block {
    display: block !important;
  }
  .d-lg-grid {
    display: grid !important;
  }
  .d-lg-table {
    display: table !important;
  }
  .d-lg-table-row {
    display: table-row !important;
  }
  .d-lg-table-cell {
    display: table-cell !important;
  }
  .d-lg-flex {
    display: flex !important;
  }
  .d-lg-inline-flex {
    display: inline-flex !important;
  }
  .d-lg-none {
    display: none !important;
  }
  .flex-lg-fill {
    flex: 1 1 auto !important;
  }
  .flex-lg-row {
    flex-direction: row !important;
  }
  .flex-lg-column {
    flex-direction: column !important;
  }
  .flex-lg-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-lg-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-lg-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-lg-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-lg-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-lg-shrink-1 {
    flex-shrink: 1 !important;
  }
  .flex-lg-wrap {
    flex-wrap: wrap !important;
  }
  .flex-lg-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-lg-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .justify-content-lg-start {
    justify-content: flex-start !important;
  }
  .justify-content-lg-end {
    justify-content: flex-end !important;
  }
  .justify-content-lg-center {
    justify-content: center !important;
  }
  .justify-content-lg-between {
    justify-content: space-between !important;
  }
  .justify-content-lg-around {
    justify-content: space-around !important;
  }
  .justify-content-lg-evenly {
    justify-content: space-evenly !important;
  }
  .align-items-lg-start {
    align-items: flex-start !important;
  }
  .align-items-lg-end {
    align-items: flex-end !important;
  }
  .align-items-lg-center {
    align-items: center !important;
  }
  .align-items-lg-baseline {
    align-items: baseline !important;
  }
  .align-items-lg-stretch {
    align-items: stretch !important;
  }
  .align-content-lg-start {
    align-content: flex-start !important;
  }
  .align-content-lg-end {
    align-content: flex-end !important;
  }
  .align-content-lg-center {
    align-content: center !important;
  }
  .align-content-lg-between {
    align-content: space-between !important;
  }
  .align-content-lg-around {
    align-content: space-around !important;
  }
  .align-content-lg-stretch {
    align-content: stretch !important;
  }
  .align-self-lg-auto {
    align-self: auto !important;
  }
  .align-self-lg-start {
    align-self: flex-start !important;
  }
  .align-self-lg-end {
    align-self: flex-end !important;
  }
  .align-self-lg-center {
    align-self: center !important;
  }
  .align-self-lg-baseline {
    align-self: baseline !important;
  }
  .align-self-lg-stretch {
    align-self: stretch !important;
  }
  .order-lg-first {
    order: -1 !important;
  }
  .order-lg-0 {
    order: 0 !important;
  }
  .order-lg-1 {
    order: 1 !important;
  }
  .order-lg-2 {
    order: 2 !important;
  }
  .order-lg-3 {
    order: 3 !important;
  }
  .order-lg-4 {
    order: 4 !important;
  }
  .order-lg-5 {
    order: 5 !important;
  }
  .order-lg-last {
    order: 6 !important;
  }
  .m-lg-0 {
    margin: 0 !important;
  }
  .m-lg-1 {
    margin: 0.25rem !important;
  }
  .m-lg-2 {
    margin: 0.5rem !important;
  }
  .m-lg-3 {
    margin: 1rem !important;
  }
  .m-lg-4 {
    margin: 1.5rem !important;
  }
  .m-lg-5 {
    margin: 3rem !important;
  }
  .m-lg-12 {
    margin: 0.75rem !important;
  }
  .m-lg-18 {
    margin: 1.125rem !important;
  }
  .m-lg-20 {
    margin: 1.25rem !important;
  }
  .m-lg-30 {
    margin: 1.875rem !important;
  }
  .m-lg-40 {
    margin: 2.5rem !important;
  }
  .m-lg-auto {
    margin: auto !important;
  }
  .mx-lg-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }
  .mx-lg-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important;
  }
  .mx-lg-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important;
  }
  .mx-lg-3 {
    margin-right: 1rem !important;
    margin-left: 1rem !important;
  }
  .mx-lg-4 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important;
  }
  .mx-lg-5 {
    margin-right: 3rem !important;
    margin-left: 3rem !important;
  }
  .mx-lg-12 {
    margin-right: 0.75rem !important;
    margin-left: 0.75rem !important;
  }
  .mx-lg-18 {
    margin-right: 1.125rem !important;
    margin-left: 1.125rem !important;
  }
  .mx-lg-20 {
    margin-right: 1.25rem !important;
    margin-left: 1.25rem !important;
  }
  .mx-lg-30 {
    margin-right: 1.875rem !important;
    margin-left: 1.875rem !important;
  }
  .mx-lg-40 {
    margin-right: 2.5rem !important;
    margin-left: 2.5rem !important;
  }
  .mx-lg-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }
  .my-lg-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }
  .my-lg-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important;
  }
  .my-lg-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }
  .my-lg-3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
  }
  .my-lg-4 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important;
  }
  .my-lg-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important;
  }
  .my-lg-12 {
    margin-top: 0.75rem !important;
    margin-bottom: 0.75rem !important;
  }
  .my-lg-18 {
    margin-top: 1.125rem !important;
    margin-bottom: 1.125rem !important;
  }
  .my-lg-20 {
    margin-top: 1.25rem !important;
    margin-bottom: 1.25rem !important;
  }
  .my-lg-30 {
    margin-top: 1.875rem !important;
    margin-bottom: 1.875rem !important;
  }
  .my-lg-40 {
    margin-top: 2.5rem !important;
    margin-bottom: 2.5rem !important;
  }
  .my-lg-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
  .mt-lg-0 {
    margin-top: 0 !important;
  }
  .mt-lg-1 {
    margin-top: 0.25rem !important;
  }
  .mt-lg-2 {
    margin-top: 0.5rem !important;
  }
  .mt-lg-3 {
    margin-top: 1rem !important;
  }
  .mt-lg-4 {
    margin-top: 1.5rem !important;
  }
  .mt-lg-5 {
    margin-top: 3rem !important;
  }
  .mt-lg-12 {
    margin-top: 0.75rem !important;
  }
  .mt-lg-18 {
    margin-top: 1.125rem !important;
  }
  .mt-lg-20 {
    margin-top: 1.25rem !important;
  }
  .mt-lg-30 {
    margin-top: 1.875rem !important;
  }
  .mt-lg-40 {
    margin-top: 2.5rem !important;
  }
  .mt-lg-auto {
    margin-top: auto !important;
  }
  .me-lg-0 {
    margin-right: 0 !important;
  }
  .me-lg-1 {
    margin-right: 0.25rem !important;
  }
  .me-lg-2 {
    margin-right: 0.5rem !important;
  }
  .me-lg-3 {
    margin-right: 1rem !important;
  }
  .me-lg-4 {
    margin-right: 1.5rem !important;
  }
  .me-lg-5 {
    margin-right: 3rem !important;
  }
  .me-lg-12 {
    margin-right: 0.75rem !important;
  }
  .me-lg-18 {
    margin-right: 1.125rem !important;
  }
  .me-lg-20 {
    margin-right: 1.25rem !important;
  }
  .me-lg-30 {
    margin-right: 1.875rem !important;
  }
  .me-lg-40 {
    margin-right: 2.5rem !important;
  }
  .me-lg-auto {
    margin-right: auto !important;
  }
  .mb-lg-0 {
    margin-bottom: 0 !important;
  }
  .mb-lg-1 {
    margin-bottom: 0.25rem !important;
  }
  .mb-lg-2 {
    margin-bottom: 0.5rem !important;
  }
  .mb-lg-3 {
    margin-bottom: 1rem !important;
  }
  .mb-lg-4 {
    margin-bottom: 1.5rem !important;
  }
  .mb-lg-5 {
    margin-bottom: 3rem !important;
  }
  .mb-lg-12 {
    margin-bottom: 0.75rem !important;
  }
  .mb-lg-18 {
    margin-bottom: 1.125rem !important;
  }
  .mb-lg-20 {
    margin-bottom: 1.25rem !important;
  }
  .mb-lg-30 {
    margin-bottom: 1.875rem !important;
  }
  .mb-lg-40 {
    margin-bottom: 2.5rem !important;
  }
  .mb-lg-auto {
    margin-bottom: auto !important;
  }
  .ms-lg-0 {
    margin-left: 0 !important;
  }
  .ms-lg-1 {
    margin-left: 0.25rem !important;
  }
  .ms-lg-2 {
    margin-left: 0.5rem !important;
  }
  .ms-lg-3 {
    margin-left: 1rem !important;
  }
  .ms-lg-4 {
    margin-left: 1.5rem !important;
  }
  .ms-lg-5 {
    margin-left: 3rem !important;
  }
  .ms-lg-12 {
    margin-left: 0.75rem !important;
  }
  .ms-lg-18 {
    margin-left: 1.125rem !important;
  }
  .ms-lg-20 {
    margin-left: 1.25rem !important;
  }
  .ms-lg-30 {
    margin-left: 1.875rem !important;
  }
  .ms-lg-40 {
    margin-left: 2.5rem !important;
  }
  .ms-lg-auto {
    margin-left: auto !important;
  }
  .p-lg-0 {
    padding: 0 !important;
  }
  .p-lg-1 {
    padding: 0.25rem !important;
  }
  .p-lg-2 {
    padding: 0.5rem !important;
  }
  .p-lg-3 {
    padding: 1rem !important;
  }
  .p-lg-4 {
    padding: 1.5rem !important;
  }
  .p-lg-5 {
    padding: 3rem !important;
  }
  .p-lg-12 {
    padding: 0.75rem !important;
  }
  .p-lg-18 {
    padding: 1.125rem !important;
  }
  .p-lg-20 {
    padding: 1.25rem !important;
  }
  .p-lg-30 {
    padding: 1.875rem !important;
  }
  .p-lg-40 {
    padding: 2.5rem !important;
  }
  .px-lg-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }
  .px-lg-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important;
  }
  .px-lg-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important;
  }
  .px-lg-3 {
    padding-right: 1rem !important;
    padding-left: 1rem !important;
  }
  .px-lg-4 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important;
  }
  .px-lg-5 {
    padding-right: 3rem !important;
    padding-left: 3rem !important;
  }
  .px-lg-12 {
    padding-right: 0.75rem !important;
    padding-left: 0.75rem !important;
  }
  .px-lg-18 {
    padding-right: 1.125rem !important;
    padding-left: 1.125rem !important;
  }
  .px-lg-20 {
    padding-right: 1.25rem !important;
    padding-left: 1.25rem !important;
  }
  .px-lg-30 {
    padding-right: 1.875rem !important;
    padding-left: 1.875rem !important;
  }
  .px-lg-40 {
    padding-right: 2.5rem !important;
    padding-left: 2.5rem !important;
  }
  .py-lg-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
  .py-lg-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important;
  }
  .py-lg-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }
  .py-lg-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }
  .py-lg-4 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
  }
  .py-lg-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }
  .py-lg-12 {
    padding-top: 0.75rem !important;
    padding-bottom: 0.75rem !important;
  }
  .py-lg-18 {
    padding-top: 1.125rem !important;
    padding-bottom: 1.125rem !important;
  }
  .py-lg-20 {
    padding-top: 1.25rem !important;
    padding-bottom: 1.25rem !important;
  }
  .py-lg-30 {
    padding-top: 1.875rem !important;
    padding-bottom: 1.875rem !important;
  }
  .py-lg-40 {
    padding-top: 2.5rem !important;
    padding-bottom: 2.5rem !important;
  }
  .pt-lg-0 {
    padding-top: 0 !important;
  }
  .pt-lg-1 {
    padding-top: 0.25rem !important;
  }
  .pt-lg-2 {
    padding-top: 0.5rem !important;
  }
  .pt-lg-3 {
    padding-top: 1rem !important;
  }
  .pt-lg-4 {
    padding-top: 1.5rem !important;
  }
  .pt-lg-5 {
    padding-top: 3rem !important;
  }
  .pt-lg-12 {
    padding-top: 0.75rem !important;
  }
  .pt-lg-18 {
    padding-top: 1.125rem !important;
  }
  .pt-lg-20 {
    padding-top: 1.25rem !important;
  }
  .pt-lg-30 {
    padding-top: 1.875rem !important;
  }
  .pt-lg-40 {
    padding-top: 2.5rem !important;
  }
  .pe-lg-0 {
    padding-right: 0 !important;
  }
  .pe-lg-1 {
    padding-right: 0.25rem !important;
  }
  .pe-lg-2 {
    padding-right: 0.5rem !important;
  }
  .pe-lg-3 {
    padding-right: 1rem !important;
  }
  .pe-lg-4 {
    padding-right: 1.5rem !important;
  }
  .pe-lg-5 {
    padding-right: 3rem !important;
  }
  .pe-lg-12 {
    padding-right: 0.75rem !important;
  }
  .pe-lg-18 {
    padding-right: 1.125rem !important;
  }
  .pe-lg-20 {
    padding-right: 1.25rem !important;
  }
  .pe-lg-30 {
    padding-right: 1.875rem !important;
  }
  .pe-lg-40 {
    padding-right: 2.5rem !important;
  }
  .pb-lg-0 {
    padding-bottom: 0 !important;
  }
  .pb-lg-1 {
    padding-bottom: 0.25rem !important;
  }
  .pb-lg-2 {
    padding-bottom: 0.5rem !important;
  }
  .pb-lg-3 {
    padding-bottom: 1rem !important;
  }
  .pb-lg-4 {
    padding-bottom: 1.5rem !important;
  }
  .pb-lg-5 {
    padding-bottom: 3rem !important;
  }
  .pb-lg-12 {
    padding-bottom: 0.75rem !important;
  }
  .pb-lg-18 {
    padding-bottom: 1.125rem !important;
  }
  .pb-lg-20 {
    padding-bottom: 1.25rem !important;
  }
  .pb-lg-30 {
    padding-bottom: 1.875rem !important;
  }
  .pb-lg-40 {
    padding-bottom: 2.5rem !important;
  }
  .ps-lg-0 {
    padding-left: 0 !important;
  }
  .ps-lg-1 {
    padding-left: 0.25rem !important;
  }
  .ps-lg-2 {
    padding-left: 0.5rem !important;
  }
  .ps-lg-3 {
    padding-left: 1rem !important;
  }
  .ps-lg-4 {
    padding-left: 1.5rem !important;
  }
  .ps-lg-5 {
    padding-left: 3rem !important;
  }
  .ps-lg-12 {
    padding-left: 0.75rem !important;
  }
  .ps-lg-18 {
    padding-left: 1.125rem !important;
  }
  .ps-lg-20 {
    padding-left: 1.25rem !important;
  }
  .ps-lg-30 {
    padding-left: 1.875rem !important;
  }
  .ps-lg-40 {
    padding-left: 2.5rem !important;
  }
  .gap-lg-0 {
    gap: 0 !important;
  }
  .gap-lg-1 {
    gap: 0.25rem !important;
  }
  .gap-lg-2 {
    gap: 0.5rem !important;
  }
  .gap-lg-3 {
    gap: 1rem !important;
  }
  .gap-lg-4 {
    gap: 1.5rem !important;
  }
  .gap-lg-5 {
    gap: 3rem !important;
  }
  .gap-lg-12 {
    gap: 0.75rem !important;
  }
  .gap-lg-18 {
    gap: 1.125rem !important;
  }
  .gap-lg-20 {
    gap: 1.25rem !important;
  }
  .gap-lg-30 {
    gap: 1.875rem !important;
  }
  .gap-lg-40 {
    gap: 2.5rem !important;
  }
  .text-lg-start {
    text-align: left !important;
  }
  .text-lg-end {
    text-align: right !important;
  }
  .text-lg-center {
    text-align: center !important;
  }
}
@media (min-width: 1200px) {
  .float-xl-start {
    float: left !important;
  }
  .float-xl-end {
    float: right !important;
  }
  .float-xl-none {
    float: none !important;
  }
  .d-xl-inline {
    display: inline !important;
  }
  .d-xl-inline-block {
    display: inline-block !important;
  }
  .d-xl-block {
    display: block !important;
  }
  .d-xl-grid {
    display: grid !important;
  }
  .d-xl-table {
    display: table !important;
  }
  .d-xl-table-row {
    display: table-row !important;
  }
  .d-xl-table-cell {
    display: table-cell !important;
  }
  .d-xl-flex {
    display: flex !important;
  }
  .d-xl-inline-flex {
    display: inline-flex !important;
  }
  .d-xl-none {
    display: none !important;
  }
  .flex-xl-fill {
    flex: 1 1 auto !important;
  }
  .flex-xl-row {
    flex-direction: row !important;
  }
  .flex-xl-column {
    flex-direction: column !important;
  }
  .flex-xl-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-xl-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-xl-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-xl-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-xl-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-xl-shrink-1 {
    flex-shrink: 1 !important;
  }
  .flex-xl-wrap {
    flex-wrap: wrap !important;
  }
  .flex-xl-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-xl-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .justify-content-xl-start {
    justify-content: flex-start !important;
  }
  .justify-content-xl-end {
    justify-content: flex-end !important;
  }
  .justify-content-xl-center {
    justify-content: center !important;
  }
  .justify-content-xl-between {
    justify-content: space-between !important;
  }
  .justify-content-xl-around {
    justify-content: space-around !important;
  }
  .justify-content-xl-evenly {
    justify-content: space-evenly !important;
  }
  .align-items-xl-start {
    align-items: flex-start !important;
  }
  .align-items-xl-end {
    align-items: flex-end !important;
  }
  .align-items-xl-center {
    align-items: center !important;
  }
  .align-items-xl-baseline {
    align-items: baseline !important;
  }
  .align-items-xl-stretch {
    align-items: stretch !important;
  }
  .align-content-xl-start {
    align-content: flex-start !important;
  }
  .align-content-xl-end {
    align-content: flex-end !important;
  }
  .align-content-xl-center {
    align-content: center !important;
  }
  .align-content-xl-between {
    align-content: space-between !important;
  }
  .align-content-xl-around {
    align-content: space-around !important;
  }
  .align-content-xl-stretch {
    align-content: stretch !important;
  }
  .align-self-xl-auto {
    align-self: auto !important;
  }
  .align-self-xl-start {
    align-self: flex-start !important;
  }
  .align-self-xl-end {
    align-self: flex-end !important;
  }
  .align-self-xl-center {
    align-self: center !important;
  }
  .align-self-xl-baseline {
    align-self: baseline !important;
  }
  .align-self-xl-stretch {
    align-self: stretch !important;
  }
  .order-xl-first {
    order: -1 !important;
  }
  .order-xl-0 {
    order: 0 !important;
  }
  .order-xl-1 {
    order: 1 !important;
  }
  .order-xl-2 {
    order: 2 !important;
  }
  .order-xl-3 {
    order: 3 !important;
  }
  .order-xl-4 {
    order: 4 !important;
  }
  .order-xl-5 {
    order: 5 !important;
  }
  .order-xl-last {
    order: 6 !important;
  }
  .m-xl-0 {
    margin: 0 !important;
  }
  .m-xl-1 {
    margin: 0.25rem !important;
  }
  .m-xl-2 {
    margin: 0.5rem !important;
  }
  .m-xl-3 {
    margin: 1rem !important;
  }
  .m-xl-4 {
    margin: 1.5rem !important;
  }
  .m-xl-5 {
    margin: 3rem !important;
  }
  .m-xl-12 {
    margin: 0.75rem !important;
  }
  .m-xl-18 {
    margin: 1.125rem !important;
  }
  .m-xl-20 {
    margin: 1.25rem !important;
  }
  .m-xl-30 {
    margin: 1.875rem !important;
  }
  .m-xl-40 {
    margin: 2.5rem !important;
  }
  .m-xl-auto {
    margin: auto !important;
  }
  .mx-xl-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }
  .mx-xl-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important;
  }
  .mx-xl-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important;
  }
  .mx-xl-3 {
    margin-right: 1rem !important;
    margin-left: 1rem !important;
  }
  .mx-xl-4 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important;
  }
  .mx-xl-5 {
    margin-right: 3rem !important;
    margin-left: 3rem !important;
  }
  .mx-xl-12 {
    margin-right: 0.75rem !important;
    margin-left: 0.75rem !important;
  }
  .mx-xl-18 {
    margin-right: 1.125rem !important;
    margin-left: 1.125rem !important;
  }
  .mx-xl-20 {
    margin-right: 1.25rem !important;
    margin-left: 1.25rem !important;
  }
  .mx-xl-30 {
    margin-right: 1.875rem !important;
    margin-left: 1.875rem !important;
  }
  .mx-xl-40 {
    margin-right: 2.5rem !important;
    margin-left: 2.5rem !important;
  }
  .mx-xl-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }
  .my-xl-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }
  .my-xl-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important;
  }
  .my-xl-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }
  .my-xl-3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
  }
  .my-xl-4 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important;
  }
  .my-xl-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important;
  }
  .my-xl-12 {
    margin-top: 0.75rem !important;
    margin-bottom: 0.75rem !important;
  }
  .my-xl-18 {
    margin-top: 1.125rem !important;
    margin-bottom: 1.125rem !important;
  }
  .my-xl-20 {
    margin-top: 1.25rem !important;
    margin-bottom: 1.25rem !important;
  }
  .my-xl-30 {
    margin-top: 1.875rem !important;
    margin-bottom: 1.875rem !important;
  }
  .my-xl-40 {
    margin-top: 2.5rem !important;
    margin-bottom: 2.5rem !important;
  }
  .my-xl-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
  .mt-xl-0 {
    margin-top: 0 !important;
  }
  .mt-xl-1 {
    margin-top: 0.25rem !important;
  }
  .mt-xl-2 {
    margin-top: 0.5rem !important;
  }
  .mt-xl-3 {
    margin-top: 1rem !important;
  }
  .mt-xl-4 {
    margin-top: 1.5rem !important;
  }
  .mt-xl-5 {
    margin-top: 3rem !important;
  }
  .mt-xl-12 {
    margin-top: 0.75rem !important;
  }
  .mt-xl-18 {
    margin-top: 1.125rem !important;
  }
  .mt-xl-20 {
    margin-top: 1.25rem !important;
  }
  .mt-xl-30 {
    margin-top: 1.875rem !important;
  }
  .mt-xl-40 {
    margin-top: 2.5rem !important;
  }
  .mt-xl-auto {
    margin-top: auto !important;
  }
  .me-xl-0 {
    margin-right: 0 !important;
  }
  .me-xl-1 {
    margin-right: 0.25rem !important;
  }
  .me-xl-2 {
    margin-right: 0.5rem !important;
  }
  .me-xl-3 {
    margin-right: 1rem !important;
  }
  .me-xl-4 {
    margin-right: 1.5rem !important;
  }
  .me-xl-5 {
    margin-right: 3rem !important;
  }
  .me-xl-12 {
    margin-right: 0.75rem !important;
  }
  .me-xl-18 {
    margin-right: 1.125rem !important;
  }
  .me-xl-20 {
    margin-right: 1.25rem !important;
  }
  .me-xl-30 {
    margin-right: 1.875rem !important;
  }
  .me-xl-40 {
    margin-right: 2.5rem !important;
  }
  .me-xl-auto {
    margin-right: auto !important;
  }
  .mb-xl-0 {
    margin-bottom: 0 !important;
  }
  .mb-xl-1 {
    margin-bottom: 0.25rem !important;
  }
  .mb-xl-2 {
    margin-bottom: 0.5rem !important;
  }
  .mb-xl-3 {
    margin-bottom: 1rem !important;
  }
  .mb-xl-4 {
    margin-bottom: 1.5rem !important;
  }
  .mb-xl-5 {
    margin-bottom: 3rem !important;
  }
  .mb-xl-12 {
    margin-bottom: 0.75rem !important;
  }
  .mb-xl-18 {
    margin-bottom: 1.125rem !important;
  }
  .mb-xl-20 {
    margin-bottom: 1.25rem !important;
  }
  .mb-xl-30 {
    margin-bottom: 1.875rem !important;
  }
  .mb-xl-40 {
    margin-bottom: 2.5rem !important;
  }
  .mb-xl-auto {
    margin-bottom: auto !important;
  }
  .ms-xl-0 {
    margin-left: 0 !important;
  }
  .ms-xl-1 {
    margin-left: 0.25rem !important;
  }
  .ms-xl-2 {
    margin-left: 0.5rem !important;
  }
  .ms-xl-3 {
    margin-left: 1rem !important;
  }
  .ms-xl-4 {
    margin-left: 1.5rem !important;
  }
  .ms-xl-5 {
    margin-left: 3rem !important;
  }
  .ms-xl-12 {
    margin-left: 0.75rem !important;
  }
  .ms-xl-18 {
    margin-left: 1.125rem !important;
  }
  .ms-xl-20 {
    margin-left: 1.25rem !important;
  }
  .ms-xl-30 {
    margin-left: 1.875rem !important;
  }
  .ms-xl-40 {
    margin-left: 2.5rem !important;
  }
  .ms-xl-auto {
    margin-left: auto !important;
  }
  .p-xl-0 {
    padding: 0 !important;
  }
  .p-xl-1 {
    padding: 0.25rem !important;
  }
  .p-xl-2 {
    padding: 0.5rem !important;
  }
  .p-xl-3 {
    padding: 1rem !important;
  }
  .p-xl-4 {
    padding: 1.5rem !important;
  }
  .p-xl-5 {
    padding: 3rem !important;
  }
  .p-xl-12 {
    padding: 0.75rem !important;
  }
  .p-xl-18 {
    padding: 1.125rem !important;
  }
  .p-xl-20 {
    padding: 1.25rem !important;
  }
  .p-xl-30 {
    padding: 1.875rem !important;
  }
  .p-xl-40 {
    padding: 2.5rem !important;
  }
  .px-xl-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }
  .px-xl-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important;
  }
  .px-xl-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important;
  }
  .px-xl-3 {
    padding-right: 1rem !important;
    padding-left: 1rem !important;
  }
  .px-xl-4 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important;
  }
  .px-xl-5 {
    padding-right: 3rem !important;
    padding-left: 3rem !important;
  }
  .px-xl-12 {
    padding-right: 0.75rem !important;
    padding-left: 0.75rem !important;
  }
  .px-xl-18 {
    padding-right: 1.125rem !important;
    padding-left: 1.125rem !important;
  }
  .px-xl-20 {
    padding-right: 1.25rem !important;
    padding-left: 1.25rem !important;
  }
  .px-xl-30 {
    padding-right: 1.875rem !important;
    padding-left: 1.875rem !important;
  }
  .px-xl-40 {
    padding-right: 2.5rem !important;
    padding-left: 2.5rem !important;
  }
  .py-xl-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
  .py-xl-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important;
  }
  .py-xl-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }
  .py-xl-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }
  .py-xl-4 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
  }
  .py-xl-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }
  .py-xl-12 {
    padding-top: 0.75rem !important;
    padding-bottom: 0.75rem !important;
  }
  .py-xl-18 {
    padding-top: 1.125rem !important;
    padding-bottom: 1.125rem !important;
  }
  .py-xl-20 {
    padding-top: 1.25rem !important;
    padding-bottom: 1.25rem !important;
  }
  .py-xl-30 {
    padding-top: 1.875rem !important;
    padding-bottom: 1.875rem !important;
  }
  .py-xl-40 {
    padding-top: 2.5rem !important;
    padding-bottom: 2.5rem !important;
  }
  .pt-xl-0 {
    padding-top: 0 !important;
  }
  .pt-xl-1 {
    padding-top: 0.25rem !important;
  }
  .pt-xl-2 {
    padding-top: 0.5rem !important;
  }
  .pt-xl-3 {
    padding-top: 1rem !important;
  }
  .pt-xl-4 {
    padding-top: 1.5rem !important;
  }
  .pt-xl-5 {
    padding-top: 3rem !important;
  }
  .pt-xl-12 {
    padding-top: 0.75rem !important;
  }
  .pt-xl-18 {
    padding-top: 1.125rem !important;
  }
  .pt-xl-20 {
    padding-top: 1.25rem !important;
  }
  .pt-xl-30 {
    padding-top: 1.875rem !important;
  }
  .pt-xl-40 {
    padding-top: 2.5rem !important;
  }
  .pe-xl-0 {
    padding-right: 0 !important;
  }
  .pe-xl-1 {
    padding-right: 0.25rem !important;
  }
  .pe-xl-2 {
    padding-right: 0.5rem !important;
  }
  .pe-xl-3 {
    padding-right: 1rem !important;
  }
  .pe-xl-4 {
    padding-right: 1.5rem !important;
  }
  .pe-xl-5 {
    padding-right: 3rem !important;
  }
  .pe-xl-12 {
    padding-right: 0.75rem !important;
  }
  .pe-xl-18 {
    padding-right: 1.125rem !important;
  }
  .pe-xl-20 {
    padding-right: 1.25rem !important;
  }
  .pe-xl-30 {
    padding-right: 1.875rem !important;
  }
  .pe-xl-40 {
    padding-right: 2.5rem !important;
  }
  .pb-xl-0 {
    padding-bottom: 0 !important;
  }
  .pb-xl-1 {
    padding-bottom: 0.25rem !important;
  }
  .pb-xl-2 {
    padding-bottom: 0.5rem !important;
  }
  .pb-xl-3 {
    padding-bottom: 1rem !important;
  }
  .pb-xl-4 {
    padding-bottom: 1.5rem !important;
  }
  .pb-xl-5 {
    padding-bottom: 3rem !important;
  }
  .pb-xl-12 {
    padding-bottom: 0.75rem !important;
  }
  .pb-xl-18 {
    padding-bottom: 1.125rem !important;
  }
  .pb-xl-20 {
    padding-bottom: 1.25rem !important;
  }
  .pb-xl-30 {
    padding-bottom: 1.875rem !important;
  }
  .pb-xl-40 {
    padding-bottom: 2.5rem !important;
  }
  .ps-xl-0 {
    padding-left: 0 !important;
  }
  .ps-xl-1 {
    padding-left: 0.25rem !important;
  }
  .ps-xl-2 {
    padding-left: 0.5rem !important;
  }
  .ps-xl-3 {
    padding-left: 1rem !important;
  }
  .ps-xl-4 {
    padding-left: 1.5rem !important;
  }
  .ps-xl-5 {
    padding-left: 3rem !important;
  }
  .ps-xl-12 {
    padding-left: 0.75rem !important;
  }
  .ps-xl-18 {
    padding-left: 1.125rem !important;
  }
  .ps-xl-20 {
    padding-left: 1.25rem !important;
  }
  .ps-xl-30 {
    padding-left: 1.875rem !important;
  }
  .ps-xl-40 {
    padding-left: 2.5rem !important;
  }
  .gap-xl-0 {
    gap: 0 !important;
  }
  .gap-xl-1 {
    gap: 0.25rem !important;
  }
  .gap-xl-2 {
    gap: 0.5rem !important;
  }
  .gap-xl-3 {
    gap: 1rem !important;
  }
  .gap-xl-4 {
    gap: 1.5rem !important;
  }
  .gap-xl-5 {
    gap: 3rem !important;
  }
  .gap-xl-12 {
    gap: 0.75rem !important;
  }
  .gap-xl-18 {
    gap: 1.125rem !important;
  }
  .gap-xl-20 {
    gap: 1.25rem !important;
  }
  .gap-xl-30 {
    gap: 1.875rem !important;
  }
  .gap-xl-40 {
    gap: 2.5rem !important;
  }
  .text-xl-start {
    text-align: left !important;
  }
  .text-xl-end {
    text-align: right !important;
  }
  .text-xl-center {
    text-align: center !important;
  }
}
@media (min-width: 1400px) {
  .float-xxl-start {
    float: left !important;
  }
  .float-xxl-end {
    float: right !important;
  }
  .float-xxl-none {
    float: none !important;
  }
  .d-xxl-inline {
    display: inline !important;
  }
  .d-xxl-inline-block {
    display: inline-block !important;
  }
  .d-xxl-block {
    display: block !important;
  }
  .d-xxl-grid {
    display: grid !important;
  }
  .d-xxl-table {
    display: table !important;
  }
  .d-xxl-table-row {
    display: table-row !important;
  }
  .d-xxl-table-cell {
    display: table-cell !important;
  }
  .d-xxl-flex {
    display: flex !important;
  }
  .d-xxl-inline-flex {
    display: inline-flex !important;
  }
  .d-xxl-none {
    display: none !important;
  }
  .flex-xxl-fill {
    flex: 1 1 auto !important;
  }
  .flex-xxl-row {
    flex-direction: row !important;
  }
  .flex-xxl-column {
    flex-direction: column !important;
  }
  .flex-xxl-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-xxl-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-xxl-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-xxl-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-xxl-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-xxl-shrink-1 {
    flex-shrink: 1 !important;
  }
  .flex-xxl-wrap {
    flex-wrap: wrap !important;
  }
  .flex-xxl-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-xxl-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .justify-content-xxl-start {
    justify-content: flex-start !important;
  }
  .justify-content-xxl-end {
    justify-content: flex-end !important;
  }
  .justify-content-xxl-center {
    justify-content: center !important;
  }
  .justify-content-xxl-between {
    justify-content: space-between !important;
  }
  .justify-content-xxl-around {
    justify-content: space-around !important;
  }
  .justify-content-xxl-evenly {
    justify-content: space-evenly !important;
  }
  .align-items-xxl-start {
    align-items: flex-start !important;
  }
  .align-items-xxl-end {
    align-items: flex-end !important;
  }
  .align-items-xxl-center {
    align-items: center !important;
  }
  .align-items-xxl-baseline {
    align-items: baseline !important;
  }
  .align-items-xxl-stretch {
    align-items: stretch !important;
  }
  .align-content-xxl-start {
    align-content: flex-start !important;
  }
  .align-content-xxl-end {
    align-content: flex-end !important;
  }
  .align-content-xxl-center {
    align-content: center !important;
  }
  .align-content-xxl-between {
    align-content: space-between !important;
  }
  .align-content-xxl-around {
    align-content: space-around !important;
  }
  .align-content-xxl-stretch {
    align-content: stretch !important;
  }
  .align-self-xxl-auto {
    align-self: auto !important;
  }
  .align-self-xxl-start {
    align-self: flex-start !important;
  }
  .align-self-xxl-end {
    align-self: flex-end !important;
  }
  .align-self-xxl-center {
    align-self: center !important;
  }
  .align-self-xxl-baseline {
    align-self: baseline !important;
  }
  .align-self-xxl-stretch {
    align-self: stretch !important;
  }
  .order-xxl-first {
    order: -1 !important;
  }
  .order-xxl-0 {
    order: 0 !important;
  }
  .order-xxl-1 {
    order: 1 !important;
  }
  .order-xxl-2 {
    order: 2 !important;
  }
  .order-xxl-3 {
    order: 3 !important;
  }
  .order-xxl-4 {
    order: 4 !important;
  }
  .order-xxl-5 {
    order: 5 !important;
  }
  .order-xxl-last {
    order: 6 !important;
  }
  .m-xxl-0 {
    margin: 0 !important;
  }
  .m-xxl-1 {
    margin: 0.25rem !important;
  }
  .m-xxl-2 {
    margin: 0.5rem !important;
  }
  .m-xxl-3 {
    margin: 1rem !important;
  }
  .m-xxl-4 {
    margin: 1.5rem !important;
  }
  .m-xxl-5 {
    margin: 3rem !important;
  }
  .m-xxl-12 {
    margin: 0.75rem !important;
  }
  .m-xxl-18 {
    margin: 1.125rem !important;
  }
  .m-xxl-20 {
    margin: 1.25rem !important;
  }
  .m-xxl-30 {
    margin: 1.875rem !important;
  }
  .m-xxl-40 {
    margin: 2.5rem !important;
  }
  .m-xxl-auto {
    margin: auto !important;
  }
  .mx-xxl-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }
  .mx-xxl-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important;
  }
  .mx-xxl-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important;
  }
  .mx-xxl-3 {
    margin-right: 1rem !important;
    margin-left: 1rem !important;
  }
  .mx-xxl-4 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important;
  }
  .mx-xxl-5 {
    margin-right: 3rem !important;
    margin-left: 3rem !important;
  }
  .mx-xxl-12 {
    margin-right: 0.75rem !important;
    margin-left: 0.75rem !important;
  }
  .mx-xxl-18 {
    margin-right: 1.125rem !important;
    margin-left: 1.125rem !important;
  }
  .mx-xxl-20 {
    margin-right: 1.25rem !important;
    margin-left: 1.25rem !important;
  }
  .mx-xxl-30 {
    margin-right: 1.875rem !important;
    margin-left: 1.875rem !important;
  }
  .mx-xxl-40 {
    margin-right: 2.5rem !important;
    margin-left: 2.5rem !important;
  }
  .mx-xxl-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }
  .my-xxl-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }
  .my-xxl-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important;
  }
  .my-xxl-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }
  .my-xxl-3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
  }
  .my-xxl-4 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important;
  }
  .my-xxl-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important;
  }
  .my-xxl-12 {
    margin-top: 0.75rem !important;
    margin-bottom: 0.75rem !important;
  }
  .my-xxl-18 {
    margin-top: 1.125rem !important;
    margin-bottom: 1.125rem !important;
  }
  .my-xxl-20 {
    margin-top: 1.25rem !important;
    margin-bottom: 1.25rem !important;
  }
  .my-xxl-30 {
    margin-top: 1.875rem !important;
    margin-bottom: 1.875rem !important;
  }
  .my-xxl-40 {
    margin-top: 2.5rem !important;
    margin-bottom: 2.5rem !important;
  }
  .my-xxl-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
  .mt-xxl-0 {
    margin-top: 0 !important;
  }
  .mt-xxl-1 {
    margin-top: 0.25rem !important;
  }
  .mt-xxl-2 {
    margin-top: 0.5rem !important;
  }
  .mt-xxl-3 {
    margin-top: 1rem !important;
  }
  .mt-xxl-4 {
    margin-top: 1.5rem !important;
  }
  .mt-xxl-5 {
    margin-top: 3rem !important;
  }
  .mt-xxl-12 {
    margin-top: 0.75rem !important;
  }
  .mt-xxl-18 {
    margin-top: 1.125rem !important;
  }
  .mt-xxl-20 {
    margin-top: 1.25rem !important;
  }
  .mt-xxl-30 {
    margin-top: 1.875rem !important;
  }
  .mt-xxl-40 {
    margin-top: 2.5rem !important;
  }
  .mt-xxl-auto {
    margin-top: auto !important;
  }
  .me-xxl-0 {
    margin-right: 0 !important;
  }
  .me-xxl-1 {
    margin-right: 0.25rem !important;
  }
  .me-xxl-2 {
    margin-right: 0.5rem !important;
  }
  .me-xxl-3 {
    margin-right: 1rem !important;
  }
  .me-xxl-4 {
    margin-right: 1.5rem !important;
  }
  .me-xxl-5 {
    margin-right: 3rem !important;
  }
  .me-xxl-12 {
    margin-right: 0.75rem !important;
  }
  .me-xxl-18 {
    margin-right: 1.125rem !important;
  }
  .me-xxl-20 {
    margin-right: 1.25rem !important;
  }
  .me-xxl-30 {
    margin-right: 1.875rem !important;
  }
  .me-xxl-40 {
    margin-right: 2.5rem !important;
  }
  .me-xxl-auto {
    margin-right: auto !important;
  }
  .mb-xxl-0 {
    margin-bottom: 0 !important;
  }
  .mb-xxl-1 {
    margin-bottom: 0.25rem !important;
  }
  .mb-xxl-2 {
    margin-bottom: 0.5rem !important;
  }
  .mb-xxl-3 {
    margin-bottom: 1rem !important;
  }
  .mb-xxl-4 {
    margin-bottom: 1.5rem !important;
  }
  .mb-xxl-5 {
    margin-bottom: 3rem !important;
  }
  .mb-xxl-12 {
    margin-bottom: 0.75rem !important;
  }
  .mb-xxl-18 {
    margin-bottom: 1.125rem !important;
  }
  .mb-xxl-20 {
    margin-bottom: 1.25rem !important;
  }
  .mb-xxl-30 {
    margin-bottom: 1.875rem !important;
  }
  .mb-xxl-40 {
    margin-bottom: 2.5rem !important;
  }
  .mb-xxl-auto {
    margin-bottom: auto !important;
  }
  .ms-xxl-0 {
    margin-left: 0 !important;
  }
  .ms-xxl-1 {
    margin-left: 0.25rem !important;
  }
  .ms-xxl-2 {
    margin-left: 0.5rem !important;
  }
  .ms-xxl-3 {
    margin-left: 1rem !important;
  }
  .ms-xxl-4 {
    margin-left: 1.5rem !important;
  }
  .ms-xxl-5 {
    margin-left: 3rem !important;
  }
  .ms-xxl-12 {
    margin-left: 0.75rem !important;
  }
  .ms-xxl-18 {
    margin-left: 1.125rem !important;
  }
  .ms-xxl-20 {
    margin-left: 1.25rem !important;
  }
  .ms-xxl-30 {
    margin-left: 1.875rem !important;
  }
  .ms-xxl-40 {
    margin-left: 2.5rem !important;
  }
  .ms-xxl-auto {
    margin-left: auto !important;
  }
  .p-xxl-0 {
    padding: 0 !important;
  }
  .p-xxl-1 {
    padding: 0.25rem !important;
  }
  .p-xxl-2 {
    padding: 0.5rem !important;
  }
  .p-xxl-3 {
    padding: 1rem !important;
  }
  .p-xxl-4 {
    padding: 1.5rem !important;
  }
  .p-xxl-5 {
    padding: 3rem !important;
  }
  .p-xxl-12 {
    padding: 0.75rem !important;
  }
  .p-xxl-18 {
    padding: 1.125rem !important;
  }
  .p-xxl-20 {
    padding: 1.25rem !important;
  }
  .p-xxl-30 {
    padding: 1.875rem !important;
  }
  .p-xxl-40 {
    padding: 2.5rem !important;
  }
  .px-xxl-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }
  .px-xxl-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important;
  }
  .px-xxl-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important;
  }
  .px-xxl-3 {
    padding-right: 1rem !important;
    padding-left: 1rem !important;
  }
  .px-xxl-4 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important;
  }
  .px-xxl-5 {
    padding-right: 3rem !important;
    padding-left: 3rem !important;
  }
  .px-xxl-12 {
    padding-right: 0.75rem !important;
    padding-left: 0.75rem !important;
  }
  .px-xxl-18 {
    padding-right: 1.125rem !important;
    padding-left: 1.125rem !important;
  }
  .px-xxl-20 {
    padding-right: 1.25rem !important;
    padding-left: 1.25rem !important;
  }
  .px-xxl-30 {
    padding-right: 1.875rem !important;
    padding-left: 1.875rem !important;
  }
  .px-xxl-40 {
    padding-right: 2.5rem !important;
    padding-left: 2.5rem !important;
  }
  .py-xxl-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
  .py-xxl-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important;
  }
  .py-xxl-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }
  .py-xxl-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }
  .py-xxl-4 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
  }
  .py-xxl-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }
  .py-xxl-12 {
    padding-top: 0.75rem !important;
    padding-bottom: 0.75rem !important;
  }
  .py-xxl-18 {
    padding-top: 1.125rem !important;
    padding-bottom: 1.125rem !important;
  }
  .py-xxl-20 {
    padding-top: 1.25rem !important;
    padding-bottom: 1.25rem !important;
  }
  .py-xxl-30 {
    padding-top: 1.875rem !important;
    padding-bottom: 1.875rem !important;
  }
  .py-xxl-40 {
    padding-top: 2.5rem !important;
    padding-bottom: 2.5rem !important;
  }
  .pt-xxl-0 {
    padding-top: 0 !important;
  }
  .pt-xxl-1 {
    padding-top: 0.25rem !important;
  }
  .pt-xxl-2 {
    padding-top: 0.5rem !important;
  }
  .pt-xxl-3 {
    padding-top: 1rem !important;
  }
  .pt-xxl-4 {
    padding-top: 1.5rem !important;
  }
  .pt-xxl-5 {
    padding-top: 3rem !important;
  }
  .pt-xxl-12 {
    padding-top: 0.75rem !important;
  }
  .pt-xxl-18 {
    padding-top: 1.125rem !important;
  }
  .pt-xxl-20 {
    padding-top: 1.25rem !important;
  }
  .pt-xxl-30 {
    padding-top: 1.875rem !important;
  }
  .pt-xxl-40 {
    padding-top: 2.5rem !important;
  }
  .pe-xxl-0 {
    padding-right: 0 !important;
  }
  .pe-xxl-1 {
    padding-right: 0.25rem !important;
  }
  .pe-xxl-2 {
    padding-right: 0.5rem !important;
  }
  .pe-xxl-3 {
    padding-right: 1rem !important;
  }
  .pe-xxl-4 {
    padding-right: 1.5rem !important;
  }
  .pe-xxl-5 {
    padding-right: 3rem !important;
  }
  .pe-xxl-12 {
    padding-right: 0.75rem !important;
  }
  .pe-xxl-18 {
    padding-right: 1.125rem !important;
  }
  .pe-xxl-20 {
    padding-right: 1.25rem !important;
  }
  .pe-xxl-30 {
    padding-right: 1.875rem !important;
  }
  .pe-xxl-40 {
    padding-right: 2.5rem !important;
  }
  .pb-xxl-0 {
    padding-bottom: 0 !important;
  }
  .pb-xxl-1 {
    padding-bottom: 0.25rem !important;
  }
  .pb-xxl-2 {
    padding-bottom: 0.5rem !important;
  }
  .pb-xxl-3 {
    padding-bottom: 1rem !important;
  }
  .pb-xxl-4 {
    padding-bottom: 1.5rem !important;
  }
  .pb-xxl-5 {
    padding-bottom: 3rem !important;
  }
  .pb-xxl-12 {
    padding-bottom: 0.75rem !important;
  }
  .pb-xxl-18 {
    padding-bottom: 1.125rem !important;
  }
  .pb-xxl-20 {
    padding-bottom: 1.25rem !important;
  }
  .pb-xxl-30 {
    padding-bottom: 1.875rem !important;
  }
  .pb-xxl-40 {
    padding-bottom: 2.5rem !important;
  }
  .ps-xxl-0 {
    padding-left: 0 !important;
  }
  .ps-xxl-1 {
    padding-left: 0.25rem !important;
  }
  .ps-xxl-2 {
    padding-left: 0.5rem !important;
  }
  .ps-xxl-3 {
    padding-left: 1rem !important;
  }
  .ps-xxl-4 {
    padding-left: 1.5rem !important;
  }
  .ps-xxl-5 {
    padding-left: 3rem !important;
  }
  .ps-xxl-12 {
    padding-left: 0.75rem !important;
  }
  .ps-xxl-18 {
    padding-left: 1.125rem !important;
  }
  .ps-xxl-20 {
    padding-left: 1.25rem !important;
  }
  .ps-xxl-30 {
    padding-left: 1.875rem !important;
  }
  .ps-xxl-40 {
    padding-left: 2.5rem !important;
  }
  .gap-xxl-0 {
    gap: 0 !important;
  }
  .gap-xxl-1 {
    gap: 0.25rem !important;
  }
  .gap-xxl-2 {
    gap: 0.5rem !important;
  }
  .gap-xxl-3 {
    gap: 1rem !important;
  }
  .gap-xxl-4 {
    gap: 1.5rem !important;
  }
  .gap-xxl-5 {
    gap: 3rem !important;
  }
  .gap-xxl-12 {
    gap: 0.75rem !important;
  }
  .gap-xxl-18 {
    gap: 1.125rem !important;
  }
  .gap-xxl-20 {
    gap: 1.25rem !important;
  }
  .gap-xxl-30 {
    gap: 1.875rem !important;
  }
  .gap-xxl-40 {
    gap: 2.5rem !important;
  }
  .text-xxl-start {
    text-align: left !important;
  }
  .text-xxl-end {
    text-align: right !important;
  }
  .text-xxl-center {
    text-align: center !important;
  }
}
@media (min-width: 1200px) {
  .fs-1 {
    font-size: 2.5rem !important;
  }
  .fs-2 {
    font-size: 2rem !important;
  }
  .fs-3 {
    font-size: 1.75rem !important;
  }
  .fs-4 {
    font-size: 1.5rem !important;
  }
}
@media print {
  .d-print-inline {
    display: inline !important;
  }
  .d-print-inline-block {
    display: inline-block !important;
  }
  .d-print-block {
    display: block !important;
  }
  .d-print-grid {
    display: grid !important;
  }
  .d-print-table {
    display: table !important;
  }
  .d-print-table-row {
    display: table-row !important;
  }
  .d-print-table-cell {
    display: table-cell !important;
  }
  .d-print-flex {
    display: flex !important;
  }
  .d-print-inline-flex {
    display: inline-flex !important;
  }
  .d-print-none {
    display: none !important;
  }
}
:root {
  --sg-light: #eeeeee;
  --sg-dark: #111120;
  --sg-primary: #2FABF7;
  --sg-secondary: #434343;
  --sg-link-hover-color: #000000;
  --sg-btn-hover-color: #2FABF7;
  --sg-btn-hover-border-color: #2FABF7;
  --sg-table-border-color: #d7dde9;
  --sg-body-bg-color: #f1f3fb;
  --sg-table-header-bg-color: #d9dcf9;
  --sg-sidebar-menu-active: #2FABF7;
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

* {
  margin: 0;
  padding: 0;
}

html,
body {
  height: 100%;
}

body {
  font-size: 16px;
  font-family: "Outfit", sans-serif;
  font-weight: 400;
  font-style: normal;
  line-height: 26px;
  overflow-x: hidden;
  color: #7e7f92;
  -webkit-font-smoothing: antialiased;
  -webkit-text-size-adjust: 100%;
  text-rendering: optimizeSpeed;
  background-color: rgba(47, 171, 247, 0.02);
  position: relative;
  height: auto;
  min-height: 100% !important;
}

::-moz-selection {
  background: #1696e7;
  color: #ffffff;
}

::selection {
  background: #1696e7;
  color: #ffffff;
}

::-moz-selection {
  background: #1696e7;
  color: #ffffff;
}

*::-moz-placeholder {
  color: #bfbfbf;
  opacity: 1;
}

*::placeholder {
  color: #bfbfbf;
  opacity: 1;
}

img::-moz-selection {
  background: transparent;
}

img::selection {
  background: transparent;
}

img::-moz-selection {
  background: transparent;
}

a {
  color: #7e7f92;
  text-decoration: none;
  outline: 0;
}

a:hover,
a:focus {
  color: inherit;
  text-decoration: none;
  outline: none;
}

h1, .h1,
h2,
.h2,
h3,
.h3,
h4,
.h4,
h5,
.h5,
h6,
.h6 {
  color: #556068;
  font-weight: 500;
  margin: 0;
  padding: 0;
}

p {
  padding: 0;
  margin: 0;
}

ul {
  margin: 0px;
  padding: 0px;
}

li {
  list-style: none;
}

img,
picture,
video,
canvas,
svg {
  display: block;
  max-width: 100%;
}

.main-wrapper {
  padding-left: 312px;
  width: 100%;
  transition: all 0.5s ease;
}

.main-content-wrapper {
  padding: 24px 12px 60px;
}

.simplebar,
textarea.form-control,
.note-editable {
  overflow-y: auto;
  overflow-x: hidden;
}
.simplebar::-webkit-scrollbar,
textarea.form-control::-webkit-scrollbar,
.note-editable::-webkit-scrollbar {
  width: 5px;
}
.simplebar::-webkit-scrollbar-track,
textarea.form-control::-webkit-scrollbar-track,
.note-editable::-webkit-scrollbar-track {
  box-shadow: none;
  background-color: #d3f7ed;
  border-radius: 0 4px 4px 0;
}
.simplebar::-webkit-scrollbar-thumb,
textarea.form-control::-webkit-scrollbar-thumb,
.note-editable::-webkit-scrollbar-thumb {
  background-color: var(--sg-primary);
  border-radius: 4px;
  -webkit-transition: background-color 0.5s ease;
  transition: background-color 0.5s ease;
}

.redious-border {
  border: 1px solid rgba(47, 171, 247, 0.15);
  border-radius: 20px;
}

.redious-border-5 {
  border-radius: 5px !important;
}

.rounded-0 {
  border-radius: 0px !important;
}

.rounded-10 {
  border-radius: 10px !important;
}

.rounded-20 {
  border-radius: 20px;
}

.section-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #d7dde9;
  padding: 0 0 12px;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 20px;
}
.section-top h6, .section-top .h6 {
  color: #556068;
}
.section-top a {
  color: #556068;
  font-weight: 500;
}

.section-title {
  font-weight: 500;
  font-size: 24px;
  line-height: 35px;
  color: #556068;
  margin-bottom: 12px;
}

.sub-title {
  color: #556068;
  border-bottom: 1px solid #d7dde9;
  padding-bottom: 12px;
  margin-bottom: 20px;
}

.h-80 {
  min-height: 80px !important;
}

.h-150 {
  min-height: 150px !important;
}

.gap-100 {
  gap: 100px;
}

.inactive {
  font-size: 14px;
  font-weight: 400;
  line-height: 24px;
  color: #bfbfbf;
}

.sg-text-primary {
  color: var(--sg-primary);
}

.truncate, .user-info-box .user-name, .selling-course-title p {
  display: -webkit-box !important;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.fw-500 {
  font-weight: 500;
}

.header-position {
  position: fixed;
  top: 0px;
  inset-inline-start: 0;
  bottom: 0;
  background-color: #ffffff;
  border-inline-end: 1px solid #d7dde9;
  transition: all 0.5s ease;
  width: 312px;
  z-index: 999999 !important;
  height: 100%;
}

@media (max-width: 991px) {
  .main-wrapper {
    padding-left: 0;
  }
  .header-position {
    inset-inline-start: -312px !important;
    z-index: 9999;
  }
  .sidebar-collapse .header-position {
    inset-inline-start: 0  !important;
    width: 312px !important;
  }
  .header-position .sidebar-toggler {
    display: flex !important;
    border: none;
    background: transparent;
    position: absolute;
    right: 0;
  }
  .sidebar-collapse .side-nav ul li a span {
    display: block !important;
  }
  .side-nav ul li a.dropdown-icon::after {
    display: block !important;
    opacity: 1 !important;
}
  .sidebar-collapse .main-wrapper {
    padding-left: 0 !important;
  }
  .sidebar-collapse .dashboard-logo .logo {
    display: block !important;
}
  .sidebar-collapse .dashboard-logo .logo-icon {
    display: none !important;
}
}



.header-position .sidebar-toggler {
  display: none;
}

.dashboard-logo {
  border-bottom: 1px solid #d7dde9;
  padding: 20px 0;
  min-height: 80px;
  transition: all 0.5s ease;
  max-height: 100px;
}
.dashboard-logo .logo img,
.dashboard-logo .logo svg {
  width: 148px;
}
.dashboard-logo .logo-icon {
  display: none;
  transition: all 0.5s ease;
}
.dashboard-logo .logo-icon img {
  height: 40px;
  width: 40px;
}

.side-nav {
  margin: 12px 0;
  padding: 0 30px;
  /* max-height: calc(100% - 110px); */
  max-height: calc(100% - 85px);
  overflow-y: auto;
  overflow-x: hidden;
}
.side-nav::-webkit-scrollbar {
  width: 5px;
}
.side-nav::-webkit-scrollbar-track {
  box-shadow: none;
}
.side-nav::-webkit-scrollbar-thumb {
  background-color: transparent;
  border-radius: 4px;
  -webkit-transition: background-color 0.5s ease;
  transition: background-color 0.5s ease;
}
.side-nav:hover::-webkit-scrollbar-thumb {
  background-color: #7e7f92;
}
.side-nav ul li a {
  display: flex;
  gap: 16px;
  align-items: center;
  padding: 12px 20px;
  border-radius: 10px;
  font-size: 16px;
  position: relative;
  font-weight: 400;
  line-height: 26px;
}
.side-nav ul li a:hover {
  background-color: #f1f3fb;
  color: #434343;
  font-weight: 400;
}
.side-nav ul li a i {
  font-size: 20px;
}
.side-nav ul li a.dropdown-icon::after {
  content: "\f107";
  display: block;
  position: absolute;
  inset-inline-end: 20px;
  top: 50%;
  transition: all 0.2s ease-in-out;
  transform-origin: center;
  font-family: "Line Awesome Free";
  font-weight: 900;
  transform: translateY(-50%);
}
.side-nav ul li a.dropdown-icon[aria-expanded=true]::after {
  transform: translateY(-50%) rotate(180deg);
}
.side-nav ul li ul.sub-menu {
  padding-left: 60px;
}
.side-nav ul li ul.sub-menu li {
  margin-top: 12px;
}
.side-nav ul li ul.sub-menu li a {
  padding: 0 20px;
  line-height: 26px;
  transition: 0.5s;
}
.side-nav ul li ul.sub-menu li a.active {
  color: #ffffff;
}
.side-nav ul li ul.sub-menu li a.active::after {
  background-color: #ffffff;
}
.side-nav ul li ul.sub-menu li a::after {
  position: absolute;
  top: 10px;
  left: 0;
  height: 5px;
  width: 5px;
  background: #bfbfbf;
  content: "";
  border-radius: 50%;
}
.side-nav ul li ul.sub-menu li a:hover {
  background-color: transparent;
  color: #434343;
}
.side-nav ul li ul.sub-menu li a:hover::after {
  background-color: var(--sg-light);
}
.side-nav ul li.active > a {
  background-color: #f1f3fb;
  color: #434343;
}

.sidebar-collapse .header-position {
  width: 80px;
}

.chat_sidebar_collapse .header-position:hover,
.sidebar-collapse .header-position:hover {
  width: 312px;
  z-index: 99999 !important;
  height: 100%;
  top: 0;
  bottom: 0;
  max-height: 100%;
  inset-inline-start: 0;
}

.chat_sidebar_collapse .header-position:hover .side-nav ul li a span,
.sidebar-collapse .header-position:hover .side-nav ul li a span {
  display: block;
}


.sidebar-collapse .header-position:hover .side-nav ul li a.active .badges,
.chat_sidebar_collapse .header-position:hover .side-nav ul li.active > a .badges,
.sidebar-collapse .header-position:hover .side-nav ul li.active > a .badges {
  opacity: 1;
  visibility: visible;
  pointer-events: all;
  right: 10px;
}

.chat_sidebar_collapse .header-position:hover .side-nav ul li a.dropdown-icon::after,
.sidebar-collapse .header-position:hover .side-nav ul li a.dropdown-icon::after {
  display: block;
  opacity: 1;
}

.sidebar-collapse .header-position .side-nav {
  padding: 0 15px;
}

.sidebar-collapse .main-wrapper {
  padding-left: 80px;
}

.sidebar-collapse .side-nav ul li a span {
  display: none;
}

.sidebar-collapse .dashboard-logo .logo {
  display: none;
}
.sidebar-collapse .dashboard-logo .logo-icon {
  display: block;
}
.sidebar-collapse .side-nav ul li a.dropdown-icon::after {
  display: none;
  opacity: 0;
}
.sidebar-collapse .side-nav ul li a {
  padding: 12px 14px;

}


.chat_sidebar_collapse .side-nav ul li a .badges,
.side-nav ul li a .badges {
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
  transition-delay: 300ms;
  transition-property: opacity;
}

@media (max-width: 991px) {
  .chat_sidebar_collapse .side-nav ul li a .badges,
  .side-nav ul li a .badges {
    opacity: 1;
    visibility: visible;
    pointer-events: all;
  }

}


.chat_sidebar_collapse .side-nav ul li.active > a .badges {
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
}

.side-nav ul li a.active .badges {
  right: 0 !important;
}



.side-nav ul li a.active .badges,
.side-nav ul li:hover > a .badges {
  transition-delay: initial;
  opacity: 1;
  visibility: visible;
  pointer-events: all;
  right: 10px;

}

.side-nav ul li.active > a .badges {
  background: var(--bs-dark);
  color: var(--bs-body-bg);
  right: 10px;
  opacity: 1;
  visibility: visible;
  pointer-events: all;
}

.sidebar-collapse .side-nav ul li a.active .badges,
.sidebar-collapse .side-nav ul li.active > a .badges {
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
}

@media (max-width: 991px) {
  .sidebar-collapse .side-nav ul li a.active .badges,
  .sidebar-collapse .side-nav ul li.active > a .badges {
    opacity: 1;
    visibility: visible;
    pointer-events: all;
  }

}

.swal2-container {
  z-index: 999999 !important;
}

header.navbar-dark-v1 .header-position {
  background-color: var(--sg-dark);
}
header.navbar-dark-v1 .dashboard-logo {
  border-bottom: 1px solid #28284d;
}
header.navbar-dark-v1 .side-nav ul li a {
  color: #bfbfbf;
}
header.navbar-dark-v1 .side-nav ul li a .svg-fill path {
  fill: #bfbfbf;
}
header.navbar-dark-v1 .side-nav ul li a .svg-stroke path {
  stroke: #bfbfbf;
}
header.navbar-dark-v1 .side-nav ul li a:hover, header.navbar-dark-v1 .side-nav ul li.active > a {
  background-color: var(--sg-sidebar-menu-active);
  color: #ffffff;
  font-weight: 400;
}
header.navbar-dark-v1 .side-nav ul li a:hover .svg-fill path, header.navbar-dark-v1 .side-nav ul li.active > a .svg-fill path {
  fill: #ffffff;
}
header.navbar-dark-v1 .side-nav ul li a:hover .svg-stroke path, header.navbar-dark-v1 .side-nav ul li.active > a .svg-stroke path {
  stroke: #ffffff;
}
header.navbar-dark-v1 .side-nav ul li ul.sub-menu a:hover {
  color: #ffffff;
}

.sidebar-collapse .side-nav ul li a span.svg-fill,
.sidebar-collapse .side-nav ul li a span.svg-stroke {
  display: block;
}

.navbar-top {
  min-height: 80px;
  border-bottom: 1px solid #d7dde9;
  font-family: "Outfit", sans-serif;
}

.navbar-top .dashboard-btn {
  margin-bottom: 0;
}

.chat-customtoggle,
.sidebar-toggler {
  border: 1px solid #d7dde9;
  padding: 7px;
  border-radius: 5px;
  transition: border-color 0.4s;
  width: 40px;
  height: 40px;
  min-width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.chat-customtoggle:hover,
.sidebar-toggler:hover {
  border-color: var(--sg-btn-hover-border-color);
}

.chat-customtoggle:hover .icon svg path,
.sidebar-toggler:hover .icon svg path {
  stroke: var(--sg-btn-hover-border-color);
}

.navbar-brand img {
  width: 105px;
}

.navbar-right-content ul li a {
  position: relative;
  transition: 0.5s;
}
.navbar-right-content ul li.visit-website {
  border: 1px solid #d7dde9;
  border-radius: 50px;
  height: 35px;
  width: 35px;
  text-align: center;
  /* line-height: 40px; */
  display: grid;
  place-items: center;
}
.navbar-right-content ul li.visit-website a i {
  font-size: 24px;
  line-height: 33px;
}
.navbar-right-content ul li.visit-website a:hover span.icon-hover {
  opacity: 1;
  visibility: visible;
}
.navbar-right-content ul li.visit-website .dropdown-toggle::after {
  display: none;
}

span.has_notification,
span.active_status {
  position: absolute;
  height: 8px;
  width: 8px;
  content: "";
  border-radius: 50%;
}

span.has_notification {
  top: -5px;
  inset-inline-end: -2px;
  background: #ff5630;
}

span.active_status {
  top: -5px;
  inset-inline-start: 20px;
  background: #24d6a5;
  border: 3px solid #ffffff;
  height: 13px;
  width: 13px;
}

span.icon-hover {
  position: absolute;
  inset-inline-start: -165px;
  top: -4px;
  width: -moz-max-content;
  width: max-content;
  filter: drop-shadow(0px 5px 30px rgba(0, 0, 0, 0.15));
  background: #ffffff;
  padding: 7px 30px;
  border-radius: 10px;
  color: #556068;
  opacity: 0;
  visibility: hidden;
  transition: 0.5s;
}
span.icon-hover::after {
  position: absolute;
  top: 50%;
  inset-inline-end: -12px;
  content: "";
  border-top: 6px solid transparent;
  border-left: 6px solid #ffffff;
  border-bottom: 6px solid transparent;
  border-right: 6px solid transparent;
  transform: translateY(-50%);
}

.dropdown-toggle::after {
  content: "\f107";
  border: none;
  font-family: "Line Awesome Free";
  font-weight: 900;
  color: #7e7f92;
  font-size: 15px;
  position: absolute;
  top: 50%;
  inset-inline-end: -24px;
  transform: translateY(-50%);
}

.dropdown-toggle[aria-expanded=true]::after {
  content: "\f106";
}

.notification .popup-card {
  padding: 0;
}
.notification .popup-card li:not(:last-child) {
  border-bottom: 1px solid #d7dde9;
}
.notification .popup-card li:first-child {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 14px 20px;
  color: #556068;
  font-size: 16px;
  font-weight: 500;
}
.notification .popup-card li:first-child a {
  display: block;
  text-align: right;
  padding: 0;
  color: #ec3438;
  font-size: 12px;
  line-height: 17px;
}
.notification .popup-card li:last-child a {
  display: block;
  text-align: center;
  color: #7e7f92;
  font-weight: 500;
}

img.user-avater {
  width: 30px;
  height: 30px !important;
  border-radius: 50%;
  -o-object-fit: cover;
     object-fit: cover;
}

.notification-content {
  display: flex;
  gap: 12px;
  align-items: center;
  position: r elative;
}
.notification-img {
  height: 40px;
  width: 40px;
  -o-object-fit: contain;
     object-fit: contain;
  aspect-ratio: 3/2;
}
.notification-text h6, .notification-text .h6 {
  color: #556068;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
}
.notification-text p {
  color: #7e7f92;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  width: 302px;
  word-break: break-word;
  white-space: pre-wrap;
}
.notification-time {
  position: absolute;
  top: 10px;
  right: 20px;
  font-size: 12px;
  color: #434343;
  line-height: 17px;
  font-weight: 400;
}

.unread {
  background-color: #d7dde9;
}

.navbar-toggler {
  padding: 0;
  font-size: 24px;
  line-height: 1;
  color: #7e7f92;
  background-color: transparent;
  border: none;
  border-radius: var(--bs-navbar-toggler-border-radius);
  transition: var(--bs-navbar-toggler-transition);
}
.navbar-toggler:focus {
  text-decoration: none;
  outline: 0;
  box-shadow: none;
}

.course-edit-action li.listMove,
.course-edit-action li.listMove a,
.course-edit-action li.lessonMove,
.course-edit-action li.lessonMove a,
.course-edit-action .ansMove {
  cursor: move !important;
}
.course-edit-action .course-edit-action li.listMove a {
  cursor: move;
}
.course-edit-action li.dropdown {
  border: 1px solid var(--sg-primary);
  border-radius: 5px;
  display: flex;
  align-items: center;
  padding: 0px 28px 0 0;
  margin-top: 3px;
}
.course-edit-action li.dropdown a {
  color: var(--sg-primary);
}
.course-edit-action li.dropdown .dropdown-toggle::after {
  inset-inline-end: 20px;
  color: var(--sg-primary);
}
.course-edit-action li.dropdown .dropdown-toggle.icon::after {
  display: none;
}
.course-edit-action li.dropdown .dropdown-item:hover,
.course-edit-action li.dropdown .dropdown-item:focus {
  color: var(--sg-primary);
  background-color: transparent;
}
.course-edit-action li.dropdown .dropdown-menu.show {
  display: block;
  border-color: var(--sg-primary);
  border-radius: 0 0 5px 5px;
  top: -3px !important;
  transform: translate(-1px, 40px) !important;
}

.add-new-page .button-default, .add-new-page .button-default-lg {
  padding-right: 50px;
}
.add-new-page .dropdown-toggle::after {
  inset-inline-end: 15px;
}
.add-new-page .dropdown-menu.show {
  top: 59px;
  padding: 20px 15px;
  border: none;
  box-shadow: 0px 5px 25px rgba(0, 0, 0, 0.15);
  border-radius: 10px;
  background-color: #ffffff;
  max-height: 600px;
  overflow-y: auto;
}
.add-new-page .dropdown-menu li:not(:last-child) a.dropdown-item {
  margin-bottom: 4px;
}
.add-new-page .dropdown-menu a.dropdown-item {
  padding: 3px 10px;
  font-size: 16px;
  line-height: 26px;
  font-weight: 400;
  color: #7e7f92;
  border-radius: 5px;
}
.add-new-page .dropdown-menu a.dropdown-item:hover {
  background: #f1f3fb;
  border-radius: 5px;
}
.add-new-page .dropdown-menu a.dropdown-item i {
  font-size: 18px;
  padding-right: 8px;
}

.button-default, .button-default-lg {
  border: 1px solid #d7dde9;
  border-radius: 5px;
  padding: 6px 15px;
  background-color: #ffffff;
  transition: all 0.4s;
}
.button-default span, .button-default-lg span {
  color: #7e7f92;
  font-weight: 400;
}
.button-default:hover, .button-default-lg:hover {
  color: var(--sg-btn-hover-color);
  border-color: var(--sg-btn-hover-border-color);
}
.button-default:hover .icon, .button-default-lg:hover .icon {
  filter: invert(49%) sepia(91%) saturate(365%) hue-rotate(108deg) brightness(94%) contrast(85%);
}
.button-default:hover::after, .button-default-lg:hover::after,
.button-default:hover span,
.button-default-lg:hover span {
  color: var(--sg-btn-hover-color);
}
.button-default .las, .button-default-lg .las {
  font-size: 18px;
}
.button-default.icon, .icon.button-default-lg {
  padding: 11px 10px !important;
}

.button-default-lg {
  padding: 6px 50px;
}
.button-default-lg:hover {
  color: var(--sg-btn-hover-color);
  background-color: #d9dcf9;
  border-color: #d9dcf9;
}

.btn {
  font-weight: 500;
}

.btn.btn-primary:hover,
.btn.btn-primary:focus {
  color: #ffffff;
}

.btn-pink {
  background-color: #ffddd6;
  color: #ff5630;
}
.btn-pink:hover {
  background-color: #ffddd6;
  color: #ff5630;
}

.sg-btn-primary {
  color: #ffffff !important;
  background-color: var(--sg-primary) !important;
  border: 1px solid var(--sg-primary) !important;
}
.sg-btn-primary:hover {
  color: #ffffff;
  background-color: var(--sg-primary);
  border-color: var(--sg-primary);
}

.sg-btn-outline-primary {
  color: var(--sg-primary);
  background-color: transparent;
  border: 1px solid var(--sg-primary);
}
.sg-btn-outline-primary:hover {
  color: #ffffff;
  background-color: var(--sg-primary);
  border-color: var(--sg-primary);
}

.badge {
  font-size: 14px;
  line-height: 24px;
  font-weight: 500;
  padding: 5px 15px;
  border-radius: 5px;
}
.badge-primary {
  background-color: #d9dcf9;
  color: var(--sg-primary);
}
.badge-success {
  background-color: #d3f7ed;
  color: #41cc68;
}
.badge-danger {
  background-color: #ffddd6;
  color: #ff2424;
}
.badge-warning {
  background-color: #ffedcc;
  color: #ffa600;
}
.badge-info {
  background-color: #ccecf1;
  color: #02a2b9;
}
.badge-light-gray {
  background-color: #f1f3fb;
  color: #7e7f92;
}

.base {
  margin-left: 20px;
  background: #f1f3fb;
  padding: 2px 10px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.verified {
  color: #24d6a5;
  background-color: #d3f7ed;
}

.un-verified {
  color: #ff5630;
  background-color: #ffddd6;
}

.gray-badge {
  padding: 7px 15px;
  border-radius: 5px;
  font-size: 14px;
  font-weight: 400;
  line-height: 24px;
  background-color: #f1f3fb;
  width: 100%;
}

.gray-badge-clr {
  color: #7e7f92;
}

.popup-card {
  border: none;
  background: #ffffff;
  box-shadow: 0px 5px 30px rgba(0, 0, 0, 0.15);
  top: 53px !important;
  padding: 30px 0;
  left: auto !important;
  border-radius: 10px;
  right: -6px;
  width: -moz-max-content;
  width: max-content;
  min-width: 200px;
}
.popup-card .dropdown-item {
  font-weight: 400;
  line-height: 26px;
  padding: 8px 20px;
  display: flex;
  align-items: center;
  gap: 12px;
  color: #434343;
  transition: 0.5s;
  font-size: 16px;
}
.popup-card .dropdown-item:hover, .popup-card .dropdown-item:focus {
  background-color: transparent;
  color: var(--sg-primary);
}
.popup-card .dropdown-item i {
  font-size: 20px;
}

/* Oftions Section Style Start========= */
.oftions {
  padding-bottom: 20px;
}

.customer-length {
  border: 1px solid #d7dde9;
  height: 40px;
  width: 86px;
  margin-right: 20px;
  padding: 8px 15px;
  color: #7e7f92;
  border-radius: 10px;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background: url(../../app/img/icons/chevron-down.svg) no-repeat center right 15px;
  background-color: #fff;
}
.customer-length:focus {
  border: 1px solid #3f52e3;
  outline: none;
}

.oftions-content-right {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}
.oftions-content-right .media-info-box {
  position: relative;
}
.oftions-content-right ul.info-box {
  top: 42px;
  inset-inline-start: -134px;
}

.oftions-content-search {
  background: #ffffff;
  padding: 7px 15px;
  display: flex;
  align-items: center;
  border-radius: 5px;
  border: 1px solid #d7dde9;
  width: 250px;
  height: 40px;
  transition: border-color 0.5s ease;
}
.oftions-content-search:focus-within {
  border-color: var(--sg-btn-hover-border-color);
}
.oftions-content-search input[type=search] {
  width: 196px;
}
.oftions-content-search input[type=search],
.oftions-content-search button[type=submit],
.oftions-content-search button[type=button] {
  border: none;
  background-color: transparent;
  margin: 0;
  padding: 0;
  color: #7e7f92;
}
.oftions-content-search input[type=search]:focus,
.oftions-content-search button[type=submit]:focus,
.oftions-content-search button[type=button]:focus {
  outline: 0;
}

.dataTables_wrapper .dataTables_filter input:focus {
  border-color: var(--sg-btn-hover-border-color) !important;
}

.search .oftions-content-search {
  width: 100% !important;
}
.search .oftions-content-search input[type=search] {
  width: 100%;
}

/* Oftions Section Style End========= */
.lang-sortField {
  margin-right: -34px;
}
.lang-sortField .oftions-content-right {
  width: 250px;
}

.oftions-content-search.search_pos {
  position: absolute;
  top: 32px;
  right: 25px;
}

.default-list-table .table {
  background: #ffffff;
  border-radius: 20px !important;
  border: 1px solid var(--sg-table-border-color);
  border-collapse: separate;
  border-spacing: 0;
  margin-bottom: 0;
}
.default-list-table .table tr {
  vertical-align: middle;
}
.default-list-table .table tr:nth-of-type(odd) {
  background: #f1f3fb;
}
.default-list-table .table tr:last-child {
  border-color: transparent;
}
.default-list-table .table tr th:first-of-type {
  width: 105px;
  max-width: 105px;
}
.default-list-table .table tr:hover .edit-panel {
  opacity: 1;
  visibility: visible;
}
.default-list-table .table tr:hover .user-edit-panel {
  transform: translateY(-10px);
}
.default-list-table .table td,
.default-list-table .table th {
  padding: 15px 30px;
  color: #7e7f92;
  /* white-space: nowrap; */
  /* min-width: 300px; */
}
.default-list-table .table td span.timetable,
.default-list-table .table th span.timetable {
  display: block;
  font-size: 14px;
}
.default-list-table .table thead tr {
  background: transparent !important;
  position: relative;
  z-index: 1;
}
.default-list-table .table thead tr::after {
  /* background: var(--sg-table-header-bg-color) !important; */
  background-color: rgba(47, 171, 247, 0.2) !important;
  content: "";
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  position: absolute;
  z-index: -1;
  border-radius: 20px 20px 0 0;
}
.default-list-table .table thead tr th {
  font-weight: 500;
  color: #556068;
}
.default-list-table .table tbody tr th {
  color: #7e7f92;
  font-weight: 400;
  font-size: 16px;
}
.default-list-table .table tbody tr td:last-child {
  text-align: right;
  padding: 15px 30px;
}

table.result-table {
  margin-top: 30px;
  width: 100%;
  border: 1px solid var(--sg-table-border-color);
  border-radius: 20px !important;
  padding: 20px 40px;
  border-collapse: separate;
  border-spacing: 0;
  background: var(--sg-table-header-bg-color) !important;
}
table.result-table tbody tr td {
  color: #7e7f92;
  font-weight: 500;
  line-height: 24px;
}

.action-card {
  text-align: center;
}
.action-card .dropdown-toggle {
  height: 40px;
  width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  border-radius: 5px;
  color: #7e7f92;
  margin-left: auto;
  transition: 0.5s;
  background: transparent;
}
.action-card .dropdown-toggle::after {
  display: none;
}
.action-card .dropdown-toggle:hover {
  background-color: var(--sg-primary);
  color: #ffffff;
}
.action-card .dropdown-menu {
  width: 200px;
  padding: 20px 10px;
  border: none;
  background-color: #ffffff;
  box-shadow: 0px 5px 20px rgba(0, 0, 0, 0.15);
}
.action-card li:hover .dropdown-item {
  color: #7e7f92;
  background-color: #f1f3fb !important;
  border-radius: 5px;
}
.action-card .dropdown-item {
  padding: 3px 10px;
  line-height: 26px;
  color: #7e7f92;
  font-size: 16px;
  transition: all 0.5s;
}
.action-card .dropdown-item:hover, .action-card .dropdown-item:focus {
  color: #556068;
  background-color: transparent !important;
}
.action-card li:not(:last-child) .dropdown-item {
  display: block;
}
.action-card ul li a {
  font-size: 20px;
  color: #7e7f92;
  transition: color 0.5s ease;
}
.action-card ul li a .title {
  font-size: 16px;
  font-weight: 400 !important;
  line-height: 26px;
  color: #7e7f92;
}
.action-card ul li a:hover {
  color: #ff2424;
}

.user-edit-panel {
  position: relative;
  cursor: pointer;
  transition: all 0.5s ease;
}
.user-edit-panel:hover {
  transform: translateY(-10px);
}
.user-edit-panel:hover .edit-panel {
  opacity: 1;
  visibility: visible;
}

.edit-panel {
  position: absolute;
  bottom: -20px;
  transition: all 0.5s ease;
  opacity: 0;
  visibility: hidden;
}
.edit-panel ul li a {
  font-size: 14px;
  line-height: 26px;
  font-weight: 400;
  transition: all 0.5s ease;
}
.edit-panel ul li a:hover {
  color: #ff5630;
}

.default-list-table.manage-staff .table td,
.default-list-table.manage-staff .table th {
  padding: 10px 30px;
}

.user-info-panel .user-img {
  height: 40px;
  aspect-ratio: 1 / 1;
  width: auto;
}

.user-info-panel .user-img img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  -o-object-fit: cover;
     object-fit: cover;
}
.user-info-panel .user-info h4, .user-info-panel .user-info .h4 {
  color: #556068;
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
}
.user-info-panel .user-info span {
  color: #7e7f92;
  font-size: 16px;
  line-height: 24px;
}

.user-status {
  position: relative;
}
.user-status.active::before {
  background: #24d6a5;
}
.user-status::before {
  content: "";
  height: 12px;
  width: 12px;
  background: #bfbfbf;
  display: block;
  border-radius: 50px;
  border: 2px solid #ffffff;
  position: absolute;
  top: -5px;
  right: -1px;
}

.staff-role-heigh {
  max-height: 595px;
  padding-right: 10px;
}

.default-list-table.staff-role-table {
  border-radius: 5px;
}
.default-list-table.staff-role-table .table tr {
  vertical-align: initial;
}
.default-list-table.staff-role-table .table tr:nth-of-type(odd) {
  background: #ffffff;
}
.default-list-table.staff-role-table .table td {
  padding: 10px 20px;
  width: 50%;
}
.default-list-table.staff-role-table .table td:last-child {
  text-align: left;
}
.default-list-table.staff-role-table .table td:first-child {
  border-right: 1px solid #d7dde9;
}

.default-list-table.app-setting .app-img img {
  width: 70px;
  height: 50px;
  border: 1px solid #d7dde9;
  border-radius: 3px;
}
.default-list-table.app-setting tbody tr td:nth-child(4) {
  max-width: 650px;
}
.default-list-table.app-setting .action-card ul li a {
  font-size: 20px;
  color: var(--sg-primary);
  transition: color 0.5s ease;
}
.default-list-table.app-setting .action-card ul li a .title {
  font-size: 16px;
  font-weight: 400 !important;
  line-height: 26px;
  color: #7e7f92;
}
.default-list-table.app-setting .action-card ul li a:hover {
  color: #ff2424;
}
.default-list-table.app-setting .passField {
  width: 200px;
  border: none;
  font-size: 16px;
  line-height: 26px;
  font-weight: 400;
  color: #7e7f92;
  background-color: transparent !important;
}
.default-list-table.app-setting .passField:focus {
  outline: 0;
}
.default-list-table.app-setting .toggle-password {
  cursor: pointer;
}

.default-list-table.apk-setting .table th,
.default-list-table.apk-setting .table td {
  width: 25% !important;
}
.default-list-table.apk-setting tbody tr td:nth-child(4) {
  max-width: 650px;
}
.default-list-table.apk-setting .action-card ul li a {
  font-size: 20px;
  color: var(--sg-primary);
  transition: color 0.5s ease;
}
.default-list-table.apk-setting .action-card ul li a .title {
  font-size: 16px;
  font-weight: 400 !important;
  line-height: 26px;
  color: #7e7f92;
}
.default-list-table.apk-setting .action-card ul li a:hover {
  color: #ff2424;
}
.default-list-table.apk-setting .passField {
  width: 200px;
  border: none;
  font-size: 16px;
  line-height: 26px;
  font-weight: 400;
  color: #7e7f92;
}
.default-list-table.apk-setting .passField:focus {
  outline: 0;
}
.default-list-table.apk-setting label.toggle-password {
  cursor: pointer;
}

.default-list-table.lang-setting .table th:first-of-type {
  width: auto;
  max-width: initial;
}
.default-list-table.lang-setting .table td,
.default-list-table.lang-setting .table th {
  min-width: 260px !important;
}

@media (max-width: 1920px) {
  .default-list-table.lang-setting .table td,
.default-list-table.lang-setting .table th {
  max-width: 460px !important;
}
}

@media (max-width: 991px) {
  .translation_form {
    margin-top: 20px;
  }
}

/* .default-list-table .table td, .default-list-table .table th */

.default-list-table.lang-setting .table thead tr th:last-child,
.default-list-table.lang-setting .table tbody tr td:last-child {
  text-align: left;
  width: 126px !important;
  min-width: 100px !important;
}

.default-list-table.edit-course table tr:hover td,
.default-list-table.edit-course table tr:hover th {
  color: var(--sg-primary);
}
.default-list-table.edit-course table tr:hover .action-card ul {
  opacity: 1;
  visibility: visible;
}
.default-list-table.edit-course table .action-card ul {
  opacity: 0;
  visibility: hidden;
}
.default-list-table.edit-course table tbody tr td:nth-child(2) {
  width: 100%;
}

.default-list-table.social-proof-table .table tr th:first-of-type {
  width: initial;
  max-width: initial;
}

.default-list-table.refund .table thead tr th:last-child {
  text-align: center;
}
.default-list-table.refund .table tbody tr td:last-child {
  text-align: center;
}

.default-list-table.marketing-coupon .coupon-img img {
  width: 60px;
  height: 40px;
  border-radius: 5px;
}

.default-list-table.instructor-payment-table .user-info p {
  font-size: 14px;
  line-height: 24px;
  font-weight: 400;
}
.default-list-table.instructor-payment-table .payment-details ul li:last-child {
  font-size: 14px;
  color: #bfbfbf;
}
.default-list-table.instructor-payment-table .action-card ul {
  opacity: 0;
  visibility: hidden;
  transition: all 0.5s ease;
}
.default-list-table.instructor-payment-table table tbody tr:hover .action-card ul {
  opacity: 1;
  visibility: visible;
}

.installed-addons-table .custom-checkbox input[type=checkbox] + span::after {
  background: transparent;
  border-radius: 5px;
  border-color: #bfbfbf;
}
.installed-addons-table ul li {
  color: #556068;
  margin-right: 7px;
  padding-right: 7px;
  position: relative;
  font-size: 14px;
}
.installed-addons-table ul li::after {
  position: absolute;
  height: 14px;
  width: 1px;
  background: #556068;
  top: 6px;
  content: "";
  right: -1px;
}
.installed-addons-table ul li:last-child {
  margin-right: 0;
  padding-right: 0;
}
.installed-addons-table ul li:last-child::after {
  display: none;
}

.dataTables_wrapper .dataTables_filter {
  float: right;
  text-align: right;
  position: relative;
  margin-bottom: 20px;
}

.dataTables_wrapper .dataTables_filter input {
  margin-left: 0.5em;
  width: 250px;
  border: 1px solid var(--sg-table-border-color);
  height: 41px;
  border-radius: 5px;
  padding: 0px 34px 0 10px;
  color: #7e7f92;
}

.dataTables_wrapper .dataTables_filter input:focus {
  outline: 0;
}

.dataTables_wrapper .dataTables_filter::after {
  content: "\f002";
  position: absolute;
  right: 14px;
  top: 8px;
  font-family: "Line Awesome Free";
  font-weight: 900;
  transform: rotate(280deg);
  color: #7e7f92;
}

.yajra-dataTable div.dt-buttons {
  position: relative;
  float: right;
  margin-left: 20px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  border: 1px solid var(--sg-table-border-color);
  border-radius: 5px;
  /* width: 340px; */
  justify-content: end;
}

/* .yajra-dataTable button.dt-button:not(:last-child) {
  border-right: 1px solid #D7DDE9;
} */
/* .yajra-dataTable div.dt-buttons::after {
  content: "Export As";
  position: absolute;
  left: 15px;
} */
.yajra-dataTable button.dt-button {
  position: relative;
  display: flex;
  box-sizing: border-box;
  margin: 0 !important;
  flex-direction: column !important;
  padding: 7px 15px;
  border: 0;
  border-radius: 0px;
  cursor: pointer;
  font-size: 16px;
  line-height: 26px;
  color: #7e7f92;
  background-color: #ffffff;
  background: none;
  transition: all 0.5s ease;
}

.yajra-dataTable button.dt-button:not(:last-child)::after {
  position: absolute;
  top: 0;
  right: 0;
  border-right: 1px solid #dfe5ff;
  width: 1px;
  height: 100%;
  content: "";
}

.yajra-dataTable button.dt-button:hover {
  border: none !important;
  border-right: 1px solid #dfe5ff;
  background: #dfe5ff !important;
  color: var(--sg-primary);
}

.yajra-dataTable button.dt-button:focus:not(.disabled) {
  border: 0;
  text-shadow: none;
  outline: none;
  background-color: transparent;
  background: none;
  filter: none;
  box-shadow: none;
}

.yajra-dataTable button.dt-button:active:not(.disabled):hover:not(.disabled),
button.dt-button.active:not(.disabled):hover:not(.disabled),
div.dt-button:active:not(.disabled):hover:not(.disabled),
div.dt-button.active:not(.disabled):hover:not(.disabled),
a.dt-button:active:not(.disabled):hover:not(.disabled),
a.dt-button.active:not(.disabled):hover:not(.disabled),
input.dt-button:active:not(.disabled):hover:not(.disabled),
input.dt-button.active:not(.disabled):hover:not(.disabled) {
  box-shadow: none;
  background-color: transparent;
  background: none;
  filter: none;
}

.default-list-table.yajra-dataTable {
  border: none;
  border-radius: 0;
}

.yajra-dataTable table.dataTable thead th,
.yajra-dataTable table.dataTable thead td {
  /* padding: 10px 18px; */
  border-bottom: 0 !important;
}

.yajra-dataTable button.dt-button {
  margin-bottom: 0px;
}

.default-list-table.yajra-dataTable .table {
  /* background: $white;
  border-collapse: initial;
  border-radius: 20px;
  overflow: hidden;
  margin: 0;
  border: 1px solid #D7DDE9; */
  background: #ffffff;
  border-radius: 20px !important;
  border: 1px solid var(--sg-table-border-color);
  border-collapse: separate;
  border-spacing: 0;
  margin-bottom: 0;
  width: 100%;
}
.default-list-table.yajra-dataTable .table tr {
  vertical-align: middle;
  width: 100% !important;
}
.default-list-table.yajra-dataTable .table tr:nth-of-type(odd) {
  /* background: var(--sg-body-bg-color); */
  background-color: rgba(47, 171, 247, 0.05) !important;
}
.default-list-table.yajra-dataTable .table tr:last-child {
  border-color: transparent;
}
.default-list-table.yajra-dataTable .table tr th:first-of-type {
  width: 105px;
  max-width: 105px;
}
.default-list-table.yajra-dataTable .table tr:hover .edit-panel {
  opacity: 1;
  visibility: visible;
}
.default-list-table.yajra-dataTable .table tr:hover .user-edit-panel {
  transform: translateY(-10px);
}

.yajra-dataTable .dataTables_wrapper .dataTables_info {
  font-family: "Outfit";
  font-size: 16px;
  color: #7e7f92 !important;
  line-height: 26px;
}

.yajra-dataTable .dataTables_wrapper .dataTables_info,
.yajra-dataTable .dataTables_wrapper .dataTables_paginate {
  margin-top: 30px;
}

.yajra-dataTable .dataTables_wrapper .dataTables_paginate {
  border: 1px solid var(--sg-table-border-color);
  padding: 0;
  font-family: "Outfit";
  border-radius: 5px;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.disabled,
.dataTables_wrapper .dataTables_paginate .paginate_button.disabled:hover,
.dataTables_wrapper .dataTables_paginate .paginate_button.disabled:active {
  cursor: default;
  color: #7e7f92 !important;
  border: 0 !important;
  background: transparent;
  box-shadow: none;
  font-size: 16px;
  line-height: 26px;
  font-weight: 400;
  font-family: "Outfit";
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current,
.dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
  color: #ffffff !important;
  border: 1px solid var(--sg-primary);
  background-color: var(--sg-primary);
  border-radius: 0;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
  min-width: 1.5em;
  padding: 0.5em 1em;
  cursor: pointer;
  *cursor: hand;
  color: #7e7f92 !important;
  border-radius: 0;
  margin-left: 0;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
  color: white !important;
  border: 1px solid var(--sg-primary);
  background-color: var(--sg-primary);
  background: var(--sg-primary);
}

.dataTables_wrapper .dataTables_paginate .paginate_button:active {
  outline: none;
  background-color: none;
  box-shadow: none;
}

.yajra-dataTable .oftions-content-right a:focus {
  color: #ffffff;
}

.yajra-dataTable .dropdown-toggle::after {
  display: none;
}

.yajra-dataTable .select2-container--default .select2-selection--single {
  border-radius: 5px;
}

.yajra-dataTable .dataTables_length label {
  width: 300px;
  color: #7e7f92;
}

.yajra-dataTable .select2-container--default {
  width: 30% !important;
  margin-right: 15px;
}

.default-list-table.yajra-dataTable .table tbody tr.odd td.dataTables_empty {
  text-align: center;
  width: 100%;
}

.swal2-styled.swal2-default-outline:focus {
  box-shadow: none !important;
}

.select2-container--default .select2-selection--single:focus-visible {
  outline: 0;
  border: 1px solid var(--sg-table-border-color);
}

table.dataTable thead .sorting_desc {
  background-image: url("../images/sort_desc.png");
}

table.dataTable thead .sorting {
  background-image: url("../images/sort_both.png");
}

table.dataTable thead .sorting_asc {
  background-image: url("../images/sort_asc.png");
}

.default-list-table.yajra-dataTable .table tr.odd:last-child {
  background: none;
  border-radius: 0 0 20px 20px !important;
}

table.dataTable tbody tr {
  background-color: transparent;
}

.default-list-table.yajra-dataTable .table tr.odd:last-child {
  background: transparent;
}

.lang_edit_col {
  display: flex;
  gap: 8px;
}
.lang_edit_col svg {
  width: 12px;
  fill: #7e7f92;
}
.lang_edit_col svg.text-green {
  fill: #41cc68;
}
.lang_edit_col svg.text-red {
  fill: #ff2424;
}
.lang_edit_col textarea {
  border: none;
  color: #7e7f92;
  font-size: 16px;
  resize: none;
}
.lang_edit_col textarea::-webkit-scrollbar {
  display: none;
}
.lang_edit_col textarea:focus {
  border: 1px solid #d7dde9;
  height: 60px;
  width: 284px;
  outline: 0;
  border-radius: 5px;
  padding: 8px 10px;
}

.accountID:hover {
  color: var(--sg-primary);
}

#copyText {
  cursor: pointer;
}

/* Table */
table.dataTable.collapsed td.child ul li {
  display: flex;
  align-items: center !important;
  justify-content: space-between;
}

table.dataTable.collapsed td.child ul li:not(:last-child) {
  margin-bottom: 15px;
}

table.dataTable.collapsed td.child ul li .dtr-title {
  color: #556068;
  font-weight: 500;
  line-height: 20px;
}

table.dataTable.collapsed td.child ul li .dtr-data ul li {
  margin-bottom: 0 !important;
}

table.dataTable.collapsed > tbody > tr[role=row] > td:first-child,
table.dataTable.collapsed > tbody > tr[role=row] > th:first-child {
  position: relative;
  padding-left: 30px;
  cursor: pointer;
}

table.dataTable.collapsed > tbody > tr[role=row] > td:first-child:before,
table.dataTable.collapsed > tbody > tr[role=row] > th:first-child:before {
  top: 22px;
  left: 0px;
  height: 18px;
  width: 18px;
  display: block;
  position: absolute;
  color: white;
  border-radius: 18px;
  box-sizing: content-box;
  text-align: center;
  text-indent: 0 !important;
  font-size: 23px;
  font-family: "Courier New", Courier, monospace;
  line-height: 18px;
  content: "+";
  background: var(--sg-primary);
  transition: all 0.4s ease 0s;
}

table.dataTable.collapsed > tbody > tr[role=row] > td:first-child::before,
table.dataTable.collapsed > tbody > tr[role=row] > th:first-child::before {
  z-index: 9;
  top: 50%;
  position: absolute;
  transform: translateY(-50%);
  font-family: "Line Awesome Free";
  font-size: 12px;
  line-height: 14px;
  font-weight: 600;
  background-color: var(--sg-primary);
  box-shadow: 6px 6px 12px rgba(0, 0, 0, 0.08), -6px -6px 12px white;
  left: 8px;
  content: "\f067";
  display: grid;
  place-content: center;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current,
.dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
  background-color: linear-gradient(to bottom, var(--sg-primary) 0%, var(--sg-primary) 100%);
}

table.dataTable th.dt-center,
table.dataTable td.dt-center,
table.dataTable td.dataTables_empty {
  text-align: center !important;
}

.default-list-table.yajra-dataTable .table#dataTableBuilder thead tr th:last-child {
  text-align: center;
}

.page-item {
  --bdrs: 5px;
}
.page-item:first-child .page-link {
  border-top-left-radius: var(--bdrs);
  border-bottom-left-radius: var(--bdrs);
}
.page-item:last-child .page-link {
  border-top-right-radius: var(--bdrs);
  border-bottom-right-radius: var(--bdrs);
}

.page-link {
  position: relative;
  display: block;
  padding: 9px 12px;
  font-size: 16px;
  color: #7e7f92;
  text-decoration: none;
  background-color: #fff;
  border: 1px solid #d7dde9;
}
.page-link:hover {
  z-index: 2;
  color: #ffffff;
  background-color: var(--sg-primary);
  border-color: var(--sg-primary);
}

.page-link.active, .active > .page-link {
  z-index: 3;
  color: #ffffff;
  background-color: var(--sg-primary);
  border-color: var(--sg-primary);
}

.page-link:hover {
  z-index: 2;
  color: #ffffff;
  background-color: var(--sg-primary);
  border-color: var(--sg-primary);
}

.page-link:focus {
  z-index: 3;
  color: var(--bs-pagination-focus-color);
  background-color: var(--bs-pagination-focus-bg);
  outline: 0;
  box-shadow: none;
}

/* Setting Tools Style======= */
.settings-tools {
  padding: 30px 0;
}
.settings-tools-nav ul li.active a {
  background-color: #f1f3fb;
}
.settings-tools-nav ul li a {
  padding: 12px 20px;
  display: block;
  transition: 0.5s;
  color: #7e7f92;
  font-family: 400;
  line-height: 26px;
  position: relative;
}
.settings-tools-nav ul li a i {
  font-size: 18px;
}
.settings-tools-nav ul li a span {
  padding-left: 15px;
}
.settings-tools-nav ul li a:hover {
  background-color: #f1f3fb;
}
.settings-tools-nav ul li a.has-dropdown .dropdown-icon {
  position: absolute;
  top: 15px;
  right: 20px;
  display: grid;
  place-items: center;
  height: 18px;
  width: 18px;
}
.settings-tools-nav ul li a.has-dropdown.sub-menu-opened .dropdown-icon::before {
  content: "\f106";
}
.settings-tools-nav ul li a.has-dropdown.sub-menu-opened + ul.sub-menu {
  padding-left: 55px;
  opacity: 1;
  visibility: visible;
  height: auto;
  margin-bottom: 12px;
}
.settings-tools-nav ul li ul.sub-menu {
  height: 0;
  overflow: hidden;
  opacity: 0;
  visibility: hidden;
  transition: 0.5s;
}
.settings-tools-nav ul li ul.sub-menu li {
  margin-top: 12px;
}
.settings-tools-nav ul li ul.sub-menu li a {
  padding: 0 20px;
  line-height: 26px;
  transition: 0.5s;
  background-color: transparent;
}
.settings-tools-nav ul li ul.sub-menu li a::after {
  position: absolute;
  top: 10px;
  left: 0;
  height: 5px;
  width: 5px;
  background: #7e7f92;
  content: "";
  border-radius: 50%;
  transition: 0.5s;
}
.settings-tools-nav ul li ul.sub-menu li a:hover {
  background-color: transparent;
  color: #434343;
  transition: 0.5s;
}
.settings-tools-nav ul li ul.sub-menu li a:hover::after {
  background-color: #434343;
}

/* Setting Tools End======= */
.editor-wrapper .note-editor.note-frame {
  border: 1px solid #d7dde9;
  border-radius: 5px;
  background-color: #ffffff;
}
.editor-wrapper .note-toolbar {
  background-color: #fff;
}
.editor-wrapper .note-btn {
  border: 1px solid #d7dde9;
  border-radius: 0;
  color: #7e7f92;
  font-size: 16px;
}
.editor-wrapper [class*=" note-icon"]:before,
.editor-wrapper [class^=note-icon]:before {
  color: #7e7f92;
}
.editor-wrapper .dropdown-toggle::after {
  border-top-color: #7e7f92;
}
.editor-wrapper .note-icon-caret:before {
  display: none;
}
.editor-wrapper .note-editor .note-toolbar .note-color-all .note-dropdown-menu,
.editor-wrapper .note-popover .popover-content .note-color-all .note-dropdown-menu {
  min-width: 347px;
}
.editor-wrapper .note-editor .note-toolbar .note-para .note-dropdown-menu > div + div,
.editor-wrapper .note-popover .popover-content .note-para .note-dropdown-menu > div + div {
  margin-left: 0px;
  margin-top: 5px;
}
.editor-wrapper .note-statusbar.locked {
  display: none;
}
.editor-wrapper .note-editable p {
  color: #7e7f92;
}

.img-uploader.dropzone {
  border: 2px dashed #d7dde9;
  min-height: 80px;
}
.img-uploader.dropzone button.dz-button i {
  font-size: 36px;
  color: #7e7f92;
}

.alert-note {
  color: #bfbfbf;
  font-size: 14px;
  line-height: 20px;
  font-style: italic;
  margin-top: 12px;
}

.default-tab-list ul.nav li {
  padding: 0px 18px;
}
.default-tab-list ul.nav li:first-child {
  padding-left: 0;
}
.default-tab-list ul li:last-child a.nav-link::before {
  display: none;
  padding-right: 0;
}
.default-tab-list ul li a.nav-link {
  color: #556068;
  font-weight: 500;
  line-height: 24px;
  cursor: pointer;
  position: relative;
  padding: 0 0 8px 0px;
}
.default-tab-list ul li a.nav-link::before {
  content: "\f105";
  position: absolute;
  top: 2px;
  right: -25px;
  font-family: "Line Awesome Free";
  font-weight: 900;
  font-size: 18px;
  color: #556068;
}
.default-tab-list ul li a.nav-link.active {
  color: var(--sg-primary);
  font-weight: 500;
}
.default-tab-list ul li a.nav-link.active::before {
  color: var(--sg-primary);
}
.default-tab-list ul li a.nav-link.active .default-tab-count {
  border: 1px solid var(--sg-primary) !important;
  background-color: var(--sg-primary);
  color: #ffffff;
}
.default-tab-list ul li .default-tab-count {
  background-color: #ffffff;
  color: #556068;
  border: 1px solid #434343;
  width: 24px;
  height: 24px;
  display: inline-block;
  border-radius: 50%;
  font-weight: 500;
  font-size: 16px;
  text-align: center;
  line-height: 24px;
  margin-right: 15px;
  transition: all 0.5s ease;
}

.default-tab-list.default-tab-list-v2 .nav {
  border-bottom: 1px solid #d7dde9;
}
.default-tab-list.default-tab-list-v2 ul li a.nav-link::before {
  display: none;
}
.default-tab-list.default-tab-list-v2 ul li a.nav-link.active::after {
  position: absolute;
  bottom: -14px;
  left: 0;
  width: 100%;
  border: 1px solid var(--sg-primary);
  content: "";
}

@media (max-width: 659px) {
  .default-tab-list.default-tab-list-v2 ul li a.nav-link.active::after {
    bottom: 0;
  }
}

.default-tab-list.media-modal-tab .oftions-content-search {
  margin-right: 50px;
}
.default-tab-list.media-modal-tab .btn-close.modal-close {
  top: 42px;
}

.default-tab-list .tab-content .row .col-lg-12 .editCourseCurriculum .accordion-item:last-child {
  margin-bottom: 0;
}

/* Default Sidenav Style Start========= */
.default-sidenav li a {
  font-size: 16px;
  line-height: 26px;
  font-weight: 400;
  color: #7e7f92;
  padding: 12px 20px;
  display: block;
  transition: all 0.5s ease;
  position: relative;
}
.default-sidenav li a .icon {
  padding-right: 10px;
}
.default-sidenav li a .icon i {
  font-size: 18px;
}
.default-sidenav li a.active, .default-sidenav li a:hover {
  background-color: var(--sg-table-header-bg-color);
  color: #556068;
}
.default-sidenav li a.has-dropdown.sub-menu-opened + ul.sidenav-dropdown {
  overflow: hidden;
  max-height: 500px;
  opacity: 1;
  transform: scaleY(1);
  transition: all 0.5s ease, max-height 0.5s ease-in;
}
.default-sidenav li a.has-dropdown.sub-menu-opened .arrow-icon {
  transform: rotate(-180deg);
}
.default-sidenav li a .arrow-icon {
  position: absolute;
  top: 15px;
  right: 20px;
  font-size: 18px;
  transition: all 0.5s ease;
  transform-origin: center;
}

.sidenav-dropdown {
  overflow: hidden;
  max-height: 0;
  opacity: 0;
  margin-left: 50px;
  transform: scaleY(0);
  transform-origin: top;
  transition: all 0.5s ease, max-height 0.5s ease-out;
}
.sidenav-dropdown li a {
  padding: 8px 20px;
}
.sidenav-dropdown li a:hover {
  background-color: transparent;
}
.sidenav-dropdown li a::after {
  position: absolute;
  top: 16px;
  left: 0;
  background: #7e7f92;
  height: 6px;
  width: 6px;
  content: "";
  border-radius: 50px;
}

.badges {
  background: var(--bs-primary);
  color: var(--bs-body-bg);
  border-radius: 30px;
  line-height: 100%;
  padding: 4px 7px;
  font-size: 12px;
  min-width: 50px;
  text-align: center;
  position: absolute;
  right: 0;

}

/* Default List View Style Start========= */
.list-view {
  background: #f1f3fb;
  border-radius: 5px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.list-view:not(:last-child) {
  margin-bottom: 12px;
}
.list-view ul li a i {
  font-size: 20px;
}

.list-view-content {
  padding: 11px 20px;
}
.list-view-content h6, .list-view-content .h6 {
  line-height: 24px;
  font-size: 16px;
}
.list-view-content p {
  font-weight: 400;
  line-height: 24px;
  font-size: 14px;
}

.list-view-icon {
  background: #f1f3fb;
  border-radius: 0 5px 5px 0;
}
.list-view-icon a {
  padding: 23px;
  font-size: 24px;
  display: block;
}

.editCourseCurriculum .list-view-content {
  padding: 14px 20px;
}
.editCourseCurriculum .list-view-content p {
  font-weight: 500;
  line-height: 20px;
  font-size: 14px;
  color: #7e7f92;
}
.editCourseCurriculum .list-view:not(:last-child) {
  margin-bottom: 20px;
}
.editCourseCurriculum .list-view ul li:not(:last-child) {
  display: none;
  transition: all 0.5 ease !important;
}
.editCourseCurriculum .list-view .icon {
  font-size: 24px;
  color: #7e7f92;
}
.editCourseCurriculum .list-view:hover ul li {
  display: block;
}
.editCourseCurriculum .list-view.fixed-list-view ul li {
  display: block;
}

.select2-container--default .select2-results__option {
  font-size: 14px !important;
}

.action-btn li {
  position: relative;
}
.action-btn li::after {
  position: absolute;
  top: 0;
  left: 0;
  content: "";
}
.action-btn a {
  font-size: 16px;
  font-weight: 400;
  line-height: 26px;
  color: #7e7f92;
  padding: 5px 15px;
  border-radius: 6px;
}
.action-btn a.active {
  background: #41cc68;
  color: #ffffff;
}
.action-btn a.inactive {
  background: #bfbfbf;
  color: #7e7f92;
}

.package-list-group .list-view {
  padding: 20px 30px;
  margin-bottom: 0 !important;
  border-radius: 10px 10px 0 0;
}
.package-list-group .list-view-content {
  padding: 0;
}
.package-list-group .list-view-content h3, .package-list-group .list-view-content .h3 {
  font-size: 36px;
  line-height: 52px;
}
.package-list-group .list-view-body {
  border: 1px solid #d7dde9;
  border-top: none;
  padding: 10px 30px 30px;
  border-radius: 0 0 10px 10px;
}
.package-list-group .list-view-box {
  gap: 144px;
  align-items: center;
}
.package-list-group .list-view-box p {
  max-width: 452px;
}
.package-list-group .list-view-box .price {
  font-size: 24px;
  line-height: 35px;
  font-weight: 500;
  color: var(--sg-primary);
  margin-top: 8px;
}

.header-left {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  align-items: center;
}
.header-left img {
  height: 20px;
  width: 20px;
}
.header-left h4, .header-left .h4 {
  border-left: 1px solid #d7dde9;
  padding-left: 30px;
}

.header-right ul {
  gap: 30px;
}
.header-right ul li a {
  font-size: 20px;
  transition: color 0.5s ease;
}
.header-right ul li a:hover {
  color: #ff2424;
}

.story-box {
  border: 1px solid #d7dde9;
}
.story-box .delete-icon {
  position: absolute;
  top: 15px;
  right: 30px;
  font-size: 18px;
}
.story-box .delete-icon:hover {
  color: #ff2424;
}

.builder-content {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  place-items: center;
  gap: 15px;
}
.builder-content .builder {
  border: 1px solid #d7dde9;
  width: 146px;
  height: 110px;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  transition: all 0.5s ease;
  cursor: pointer;
  padding: 0 10px;
}
.builder-content .builder:hover {
  background-color: #f1f3fb;
  border-color: #f1f3fb;
}
.builder-content .builder .icon img {
  height: 40px;
  width: 40px;
  margin: 0 auto;
}
.builder-content .builder .title {
  font-size: 14px;
  line-height: 24px;
  font-weight: 400;
}

.accordion-button.bef-iconRMV::after {
  display: none;
}

.homepageFixBTN {
  border-top: 1px solid #d7dde9;
  display: flex;
  justify-content: end;
  position: fixed;
  bottom: 0;
  width: calc(100% - 312px);
  z-index: 990;
  right: 0;
}

.sidebar-collapse .homepageFixBTN {
  width: calc(100% - 80px);
}

body:not(.sidebar-collapse) .homepageFixBTN {
  width: 100%;
}

.fixedSide {
  position: fixed;
  right: 18px;
}
.fixedSide .simplebar {
  height: 72vh;
  overflow-y: auto;
  padding: 30px !important;
}
.fixedSide .simplebar::-webkit-scrollbar-track {
  box-shadow: none;
  margin: 12px;
}

.media-box {
  width: 194.4px;
  border: 1px solid #d7dde9;
  border-radius: 5px;
  position: relative;
}
.media-card-thumb {
  border-radius: 5px 5px 0 0;
  background-color: #e9e8e8;
}
.media-card-thumb img {
  width: 194px;
  height: 184px;
  -o-object-fit: contain;
     object-fit: contain;
  border-radius: 5px 5px 0 0;
}
.media-card-body {
  padding: 10px;
}
.media-card-body p {
  font-size: 12px;
  line-height: 17px;
  font-weight: 500;
}
.media-card-body h6, .media-card-body .h6 {
  font-size: 14px;
  line-height: 24px;
  font-weight: 400;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.media-box-interaction .select-box {
  position: absolute;
  top: 7px;
  left: 10px;
}
.media-box-interaction .select-box .custom-checkbox input[type=checkbox] + span::after {
  background-color: rgba(255, 255, 255, 0.8);
}
.media-box-interaction .media-info-box {
  position: absolute;
  top: 16px;
  right: 10px;
}
.media-box-interaction .media-info-box a.info-icon {
  font-size: 24px;
  background-color: rgba(215, 221, 233, 0.8);
  border-radius: 5px;
  color: #556068;
}

ul.info-box {
  position: absolute;
  top: 34px;
  inset-inline-start: -150px;
  width: 175px;
  background-color: #ffffff;
  border-radius: 10px;
  box-shadow: 0px 5px 20px rgba(0, 0, 0, 0.15);
  padding: 20px;
  transform: translateY(-10px);
  transform-origin: top;
  opacity: 0;
  visibility: hidden;
  transition: all 0.5s ease;
  z-index: 111;
}
ul.info-box a {
  font-size: 16px;
  line-height: 26px;
  padding: 6px 0;
  display: block;
  transition: color 0.5s ease;
}
ul.info-box a:hover {
  color: var(--sg-primary);
}

.media-info-box:hover ul.info-box {
  transform: translateY(0px);
  opacity: 1;
  visibility: visible;
}

.media-uploader {
  border: 1px solid #d7dde9;
  border-radius: 5px;
  background: #ffffff;
  padding: 30px !important;
}

.media-message {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  cursor: pointer;
}
.media-message h6, .media-message .h6 {
  color: #7e7f92;
  font-size: 24px;
  line-height: 35px;
}
.media-message button {
  margin: 10px 0;
}

.media-flies-wrapper {
  max-height: 500px;
  overflow-y: auto;
  overflow-x: hidden;
}

.custom-checkbox .select-box {
  position: absolute;
  top: 9px;
  left: 10px;
}
.custom-checkbox input[type=checkbox] + span.select-box::after {
  background-color: rgba(255, 255, 255, 0.8);
}

.login-form {
  width: 80%;
  margin: auto;
  padding: 50px 60px;
  box-shadow: 0px 4px 30px rgba(0, 0, 0, 0.07);
}
.login-form h3, .login-form .h3 {
  font-size: 24px;
  color: var(--sg-primary);
  line-height: 35px;
  font-weight: 500;
  text-align: center;
  margin-bottom: 30px;
}

.login-form p {
  line-height: 20px;
  margin-bottom: 6px;
}

@media (max-width: 767px) {
  .login-form {
    width: 100%;
  }
}

@media (max-width: 575px) {
  .login-form {
    width: 90%;
  }
  .login-form h3, .login-form .h3 {
    line-height: 30px;
  }
}
@media (max-width: 479px) {
  .login-form {
    width: 100%;
    padding: 40px 30px;
  }
}

@media (max-width: 375px) {
  .login-form h3, .login-form .h3 {
    font-size: 22px;
  }
}

.bg-rect-shape {
  position: absolute;
  top: -36px;
  z-index: -1;
  left: 20px;
}

.bg-circle-shape {
  position: absolute;
  z-index: -1;
  right: 0;
  top: 20%;
}

.bg-circle-block-shape {
  position: absolute;
  bottom: 57px;
  z-index: -1;
  left: 30px;
}

.analytics {
  display: flex;
  align-items: center;
  gap: 20px;
}
.analytics-icon {
  height: 64px;
  width: 64px;
  border-radius: 8px;
  display: flex;
  font-size: 32px;
  background-color: #f1f3fb;
  color: var(--sg-primary);
  align-items: center;
  justify-content: center;
}
.analytics-content h4, .analytics-content .h4 {
  color: var(--sg-primary);
  font-size: 24px;
  line-height: 35px;
  font-weight: 500;
}

@media (max-width: 1440px) {
  .analytics-content h4, .analytics-content .h4 {
    font-size: 20px;
  }
}
.analytics-content h3, .analytics-content .h3 {
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
}
.analytics-content .designation {
  font-size: 16px;
  font-weight: 400;
  line-height: 26px;
  color: #7e7f92;
}
.analytics-content p {
  font-size: 16px;
  font-weight: 500;
  color: #556068;
}

@media (max-width: 1440px) {
  .analytics-content p {
    font-size: 15px;

  }
}
.analytics.clr-1 .analytics-icon {
  background-color: #d9dcf9;
}
.analytics.clr-1 .analytics-icon svg path {
  fill: #3f52e3;
}
.analytics.clr-1 .analytics-icon,
.analytics.clr-1 .analytics-content h4,
.analytics.clr-1 .analytics-content .h4 {
  color: #3f52e3;
}
.analytics.clr-2 .analytics-icon {
  background-color: #d3f7ed;
}
.analytics.clr-2 .analytics-icon svg path {
  fill: #24d6a5;
}
.analytics.clr-2 .analytics-icon,
.analytics.clr-2 .analytics-content h4,
.analytics.clr-2 .analytics-content .h4 {
  color: #24d6a5;
}
.analytics.clr-3 .analytics-icon {
  background-color: #ffedcc;
}
.analytics.clr-3 .analytics-icon svg path {
  fill: #ffa600;
}
.analytics.clr-3 .analytics-icon,
.analytics.clr-3 .analytics-content h4,
.analytics.clr-3 .analytics-content .h4 {
  color: #ffa600;
}
.analytics.clr-4 .analytics-icon {
  background-color: #ffddd6;
}
.analytics.clr-4 .analytics-icon svg path {
  fill: #ff5630;
}
.analytics.clr-4 .analytics-icon,
.analytics.clr-4 .analytics-content h4,
.analytics.clr-4 .analytics-content .h4 {
  color: #ff5630;
}

.analytics.clr-6 .analytics-icon {
  background-color: rgba(47, 171, 247, 0.2);
}
.analytics.clr-6 .analytics-icon svg path {
  fill: var(--sg-sidebar-menu-active);
}
.analytics.clr-6 .analytics-icon,
.analytics.clr-6 .analytics-content h4,
.analytics.clr-6 .analytics-content .h4 {
  color: var(--sg-sidebar-menu-active);
}

.analytics.analytics-v1 .analytics-icon {
  height: 100px;
  width: 100px;
}
.analytics.analytics-v1 .analytics-icon svg {
  height: 50px;
  width: 50px;
}

.analytics.analytics-v2 {
  flex-direction: column;
  gap: 25px;
}
.analytics.analytics-v2 .analytics-icon {
  height: 100px;
  width: 100px;
}
.analytics.analytics-v2 .analytics-content {
  text-align: center;
}
.analytics.analytics-v2 .analytics-content h4, .analytics.analytics-v2 .analytics-content .h4 {
  margin-top: 8px;
}

.analytics.analytics-v3 .analytics-icon {
  height: 140px;
  width: 140px;
}
.analytics.analytics-v3 .analytics-icon img {
  height: 100%;
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  border-radius: 10px;
}
.analytics.analytics-v3 .analytics-content {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: start;
  height: 140px;
}

.analytics.system-support {
  justify-content: center;
}
.analytics.system-support .analytics-content {
  display: flex;
  align-items: center;
  gap: 10px;
}
.analytics.system-support .analytics-content p,
.analytics.system-support .analytics-content h4,
.analytics.system-support .analytics-content .h4 {
  font-size: 24px;
  line-height: 35px;
  font-weight: 500;
}

.analytics.social-proof .analytics-icon {
  height: 100px;
  width: 100px;
}

/* Instructor Style Start========= */
.instructor-header {
  padding: 30px 20px 20px;
}
.instructor-img img {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  margin: 0 auto 20px;
}
.instructor-intro h6, .instructor-intro .h6 {
  color: #7e7f92;
  font-size: 14px;
  line-height: 20px;
}
.instructor-tags {
  margin-bottom: 20px;
}
.instructor-tags h6, .instructor-tags .h6 {
  margin-bottom: 12px;
  line-height: 24px;
}
.instructor-tags button {
  border: 1px solid #d7dde9;
  color: #7e7f92;
  font-weight: 400;
  font-size: 14px;
  padding: 8px 20px;
  border-radius: 5px;
}
.instructor-content {
  padding: 0px 20px 30px;
}
.instructor-content table tr {
  margin-bottom: 5px;
  display: block;
}
.instructor-content table tr:last-child {
  margin-bottom: 0;
}
.instructor-content table tr td a {
  padding-right: 5px;
  transition: color 0.5s ease;
}
.instructor-content table tr td a:hover {
  color: var(--sg-primary);
}
.instructor-content table tr td:first-child {
  font-weight: 500;
  line-height: 24px;
  vertical-align: baseline;
  width: 120px;
}
.instructor-content table tr td:nth-child(2) {
  font-size: 15px;
}

.follwo-list {
  display: flex;
  justify-content: space-between;
  border-width: 1px 0;
  border-style: solid;
  border-color: #d7dde9;
  margin-top: 20px;
  padding: 8px 0;
}
.follwo-list li a {
  font-weight: 500;
  font-size: 16px;
  transition: color 0.5s ease;
  color: #7e7f92;
}
.follwo-list li a:hover {
  color: var(--sg-primary);
}

.instructor-content table tr td {
  /* background: red; */
}

.instructor-content table tr td:nth-child(2) {
  font-size: 15px;
}

.edit-instructor-pro {
  position: absolute;
  top: 20px;
  right: 30px;
}
.edit-instructor-pro a {
  font-size: 16px;
  font-weight: 500;
  color: #556068;
  line-height: 24px;
}
.edit-instructor-pro a i {
  font-size: 20px;
}

.socialLinkBox {
  margin: 30px 0 50px;
}
.socialLinkBox ul {
  display: flex;
  justify-content: center;
  gap: 40px;
}
.socialLinkBox ul li {
  text-align: center;
}
.socialLinkBox ul li span {
  font-size: 16px;
  color: #556068;
  margin-top: 16px;
  font-weight: 500;
  line-height: 24px;
  text-transform: capitalize;
  display: block;
}
.socialLinkBox ul li a {
  height: 75px;
  width: 75px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f1f3fb;
  color: #fff;
  font-size: 40px;
}
.socialLinkBox ul li a.facebook {
  background-color: #3b5998;
}
.socialLinkBox ul li a.instagram {
  background: #fae100;
  background: linear-gradient(135deg, #fae100 14.64%, #fcb720 25.25%, #ff7950 35.86%, #ff1c74 50%, #6c1cd1 85.36%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr="#FAE100", endColorstr="#6C1CD1",GradientType=1 );
}
.socialLinkBox ul li a.twitter {
  background-color: #55acee;
}
.socialLinkBox ul li a.linkedin {
  background-color: #007ab9;
}
.socialLinkBox ul li a.pinterest {
  background-color: #cb2027;
}
.socialLinkBox ul li a.whatsapp {
  background-color: #29a71a;
}
.socialLinkBox ul li a.envelope {
  background-color: #2196f3;
}

.socilaLink {
  position: relative;
}
.socilaLink input {
  height: 60px;
  padding-right: 95px;
}
.socilaLink .socialBTNPos {
  position: absolute;
  top: 15px;
  right: 20px;
}

.admin-profile {
  display: flex !important;
  justify-content: center !important;
  width: 100%;
  background: var(--sg-body-bg-color);
  border-radius: 20px 20px 0 0;
  margin-bottom: 140px;
  max-height: 170px;
  border-bottom: 1px solid #d7dde9;
}
.admin-profile img {
  height: 220px;
  width: 220px;
  border: 5px solid #ffffff;
  border-radius: 50%;
  -o-object-fit: cover;
     object-fit: cover;
  position: relative;
  top: 50px;
}

.admin-passwordField .toggle-password {
  position: absolute;
  top: 50%;
  right: 0;
  margin-right: 20px;
  cursor: pointer;
  transform: translateY(-50%);
  font-size: 20px;
}

.about-info p {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
.about-info p.toggle {
  overflow: visible;
  height: auto;
  transition: 0.3s;
  display: inherit;
  -webkit-line-clamp: inherit;
  -webkit-box-orient: inherit;
}
.about-info a {
  color: var(--sg-primary);
}

/*======= Course Items =======*/
#instructorCourses .row .col-lg-4:not(:nth-last-child(-n+4)) {
  margin-bottom: 30px;
}

.course-item {
  border-radius: 10px;
  background-color: #ffffff;
  transition: 0.3s;
  -webkit-backdrop-filter: blur(300px);
          backdrop-filter: blur(300px);
  border: 1px solid #d7dde9;
}
.course-item p {
  border-bottom: 1px solid #d7dde9;
  padding-bottom: 10px;
  margin-bottom: 14px;
}
.course-item .course-item-thumb {
  display: block;
  position: relative;
  overflow: hidden;
}
.course-item .course-item-thumb img {
  width: 100%;
  transition: 0.3s;
  border-radius: 10px 10px 0 0;
}
.course-item .course-item-thumb .wishlist-icon {
  position: absolute;
  inset-inline-end: 20px;
  top: 14px;
  color: var(--sg-primary);
  font-size: 16px;
}
.course-item .course-badge {
  position: absolute;
  inset-block-start: 0;
  inset-inline-end: auto;
  inset-inline-start: 0;
  border-start-start-radius: 10px;
  border-end-start-radius: 0;
  border-end-end-radius: 0;
  border-start-end-radius: 0;
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
  padding: 6px 10px;
  background-color: rgba(63, 82, 227, 0.4);
  color: #ffffff;
}
.course-item .course-item-body {
  padding: 38px 20px 20px;
  background-color: #ffffff;
}
.course-item:hover {
  transform: translate(0, -10px);
}
.course-item:hover .course-item-thumb img {
  transform: scale(1);
}
.course-item .course-item-info,
.course-item .course-item-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 5px;
}
.course-item .course-item-info {
  margin-bottom: 0;
}
.course-item .course-item-info li {
  font-size: 14px;
  line-height: 21px;
}
.course-item .course-item-info li i {
  margin-inline-end: 4px;
  font-size: 20px;
}
.course-item .course-item-info:first-of-type {
  margin-top: -48px;
  align-items: flex-end;
  position: relative;
}
.course-item .course-item-info:first-of-type img {
  height: 50px;
  width: 50px;
  border: 3px solid #ffffff;
  border-radius: 50%;
  display: block;
  margin-bottom: 5px;
}
.course-item .course-item-info .total-review {
  margin-left: 0;
  background: #556068;
  font-weight: 500;
  font-size: 14px;
  line-height: 14px;
  border-radius: 2px;
  color: #ffffff;
  padding: 8px 10px;
}
.course-item .course-item-footer {
  background-color: #f8f8f8;
  padding: 13px 20px;
  border-radius: 0 0 10px 10px;
  border-top: 1px solid #d7dde9;
}
.course-item .course-item-footer .course-price {
  color: #41cc68;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
}
.course-item .course-item-footer .course-price small, .course-item .course-item-footer .course-price .small {
  color: #7e7f92;
  font-weight: 400;
  font-size: 14px;
  line-height: 21px;
  text-decoration: line-through;
  margin-inline-start: 8px;
}
.course-item .course-item-footer .course-lesson {
  font-size: 14px;
  line-height: 21px;
}
.course-item .course-item-footer .course-lesson i {
  margin-right: 6px;
}
.course-item .title {
  padding-top: 10px;
  padding-bottom: 0;
  border: 0;
  margin-bottom: 15px;
}
.course-item .title a {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  color: #556068;
}
.course-item .title a:hover {
  color: var(--sg-primary);
}
.course-item .rating-review i {
  color: #ffa600;
}
.course-item.course-progress .course-item-body {
  padding-top: 22px;
}
.course-item.course-progress .course-item-body .title {
  margin: 0;
  padding: 0;
}
.course-item.course-progress .course-item-footer {
  -moz-column-gap: 10px;
       column-gap: 10px;
}

.price-checkbox {
  align-items: center;
  justify-content: space-between;
  max-width: 250px;
  width: 100%;
}

.price-checkbox label {
  white-space: nowrap;
}

span.iconPos {
  position: absolute;
  top: 38px;
  right: 10px;
  font-size: 18px;
}

.enroll_courses li:nth-child(-n+2) {
  display: block;
}

.enroll_courses li:nth-child(n+3) {
  display: none;
}

.course-item.addons-item {
  border-radius: 5px;
}
.course-item.addons-item .course-item-thumb img {
  border-radius: 5px 5px 0 0;
}
.course-item.addons-item .course-item-body {
  padding: 10px 20px 15px;
}
.course-item.addons-item .meta-box {
  border-bottom: 1px solid #d7dde9;
  padding-bottom: 10px;
}
.course-item.addons-item .meta-box p {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
  color: #7e7f92;
}
.course-item.addons-item .meta-box p span {
  font-weight: 500;
}
.course-item.addons-item .title {
  padding-top: 15px;
  margin-bottom: 10px;
}
.course-item.addons-item .title a {
  -webkit-line-clamp: 2;
  font-size: 22px;
  font-weight: 500;
  line-height: 32px;
  color: #556068;
}
.course-item.addons-item .course-price {
  font-size: 26px;
  font-weight: 500;
  line-height: 38px;
  color: var(--sg-primary);
}
.course-item.addons-item .course-item-footer {
  background-color: transparent;
  padding: 20px 20px;
  border-radius: 0 0 10px 10px;
  border-top: none;
  padding-top: 0;
}

#filterSection {
  opacity: 0;
  max-height: 0;
  overflow: hidden;
  transition: opacity 0.3s ease, max-height 0.3s ease;
}
#filterSection.show {
  opacity: 1;
  max-height: -moz-fit-content;
  max-height: fit-content;
}

#filterBTN {
  padding: 10px 20px;
  font-size: 20px;
}

.selection-row {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.col-custom {
  flex: 0 0 18.8%;
}

.certificate-thumb img {
  height: auto;
  max-width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.resources-header {
  border-bottom: 1px solid #d7dde9;
}
.resources-header a {
  font-size: 20px;
  color: #ff2424;
}
.resources-content {
  display: flex;
  align-items: center;
  gap: 20px;
}
.resources-icon {
  background-color: #f1f3fb;
  height: 80px;
  width: 80px;
  flex: 0 0 auto;
  display: grid;
  place-content: center;
}
.resources-text-content {
  width: 68%;
  flex: 0 0 auto;
}
.resources-text-content p {
  font-size: 16px;
  font-weight: 400;
  line-height: 26px;
}

.description-img .media-uploader {
  border: 1px dashed #d7dde9;
}

.media-message span a {
  color: #556068;
  font-weight: 500;
  text-decoration: underline;
}

/* Book Style Start========= */
.ins-book .row .col-lg-3 {
  margin-bottom: 24px;
}
.ins-book .row .col-lg-3:nth-last-child(-n+4) {
  margin-bottom: 0;
}

.instructor-book-item {
  border: 1px solid #d7dde9;
  border-radius: 10px;
  padding: 20px;
  background-color: #ffffff;
}

.instructor-book-thumb img {
  width: 110px;
  height: 150px;
  -o-object-fit: contain;
     object-fit: contain;
  border-radius: 5px;
  margin: 0 auto 12px;
}

.instructor-book-content h3, .instructor-book-content .h3 {
  font-size: 16px;
  line-height: 24px !important;
  font-weight: 500;
  color: #556068;
}
.instructor-book-content .rating-review ul li {
  font-size: 12px;
  color: #fdcc0d;
}
.instructor-book-content .book-price {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.instructor-book-content .book-price h5, .instructor-book-content .book-price .h5 {
  font-size: 16px;
  font-weight: 500;
  color: var(--sg-primary);
  line-height: 24px;
}
.instructor-book-content .book-price .icon {
  font-size: 20px;
  color: #556068;
}

/* Student Profile Style Start========= */
.student-certificate h3, .student-certificate .h3 {
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  text-align: center;
  margin-bottom: 20px;
}

.certificate {
  border: 1px solid #eee;
  border-radius: 10px;
}
.certificate img {
  border-radius: 10px;
  height: 370px;
  width: 508px;
}

.student-cert .row .col-lg-6:not(:nth-last-child(-n+2)) {
  margin-bottom: 40px;
}

/* Payment Method Style Start========= */
.payment-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid #d7dde9;
  border-radius: 10px;
  padding: 30px 20px;
  transition: border-color 0.5s ease;
}
.payment-box:hover {
  border-color: var(--sg-primary);
}
.payment-icon {
  display: flex;
  align-items: center;
  gap: 20px;
}
.payment-icon img {
  width: 75px;
  height: 40auto;
}
.payment-icon img.social {
  width: auto;
  height: 40px;
}
.payment-icon .title {
  font-size: 24px;
  font-weight: 500;
  line-height: 35px;
  border-left: 1px solid #d7dde9;
  padding-left: 20px;
}
.payment-settings {
  display: flex;
  align-items: center;
  gap: 20px;
}
.payment-settings-btn a {
  font-size: 14px;
  line-height: 20px;
  font-weight: 500;
}
.payment-settings-btn a span {
  padding-left: 6px;
}
.payment-settings .setting-check {
  transform: rotate(-90deg);
  margin-top: -8px;
}

.payment-box.payment-box-v2 .payment-settings {
  border-right: 1px solid #d7dde9;
  margin-right: 25px;
  padding-right: 25px;
}

#toast-container > div {
  position: relative;
  overflow: hidden;
  margin: 0 0 6px;
  padding: 15px 15px 15px 50px;
  width: 300px;
  border-radius: 10px;
  background-position: 15px center;
  background-repeat: no-repeat;
  box-shadow: none;
  color: #556068;
  opacity: 1;
  font-size: 15px;
  font-weight: 500;
}
#toast-container > div::before {
  content: "\f00d";
  position: absolute;
  font-family: "Line Awesome Free";
  font-weight: 900;
  right: 15px;
  color: #556068;
}

.toast-message {
  word-wrap: break-word;
  padding-left: 15px;
}
.toast-success {
  background-color: #EAF7EE;
  border-color: #C0E5C9;
  border-radius: 5px;
}
.toast-warning {
  background-color: #FEF7EA !important;
  border: 1px solid #FAE5C3 !important;
}
.toast-danger {
  background-color: #FCEDE9 !important;
  border: 1px solid #F4CDC3 !important;
}
.toast-error {
  background-color: #FCEDE9 !important;
  border: 1px solid #F4CDC3 !important;
}
.toast-info {
  background-color: #E5EFFA !important;
  border: 1px solid #C3DCF2 !important;
}

#toast-container > .toast-success,
#toast-container > .toast-warning,
#toast-container > .toast-danger,
#toast-container > .toast-error,
#toast-container > .toast-info {
  background-image: none !important;
  /* background-color: red; */
}

#toast-container > .toast-success::after,
.toast.toast-warning::after,
.toast.toast-danger::after,
.toast.toast-error::after,
.toast.toast-info::after {
  position: absolute;
  top: 8px;
  left: 10px;
  font-family: "Line Awesome Free";
  font-weight: 900;
  height: 40px;
  width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  border-radius: 15px;
  font-size: 20px;
}

#toast-container > .toast-success::after {
  content: "\f00c";
  background: #41CC68;
}

.toast.toast-warning::after {
  content: "\f071";
  background: #EE9500;
}

.toast.toast-danger::after {
  content: "\f071";
  background: #EC4E2A;
}

.toast.toast-error::after {
  content: "\f071";
  background: #EC4E2A;
}

.toast.toast-info::after {
  content: "\f129";
  background: #0268DC;
}

#toast-container > :hover {
  box-shadow: none;
  opacity: 1;
  filter: alpha(opacity=100);
  cursor: pointer;
}

.website-theme {
  border: 1px solid #d7dde9;
  border-radius: 5px;
  position: relative;
}
.website-theme .website-thumb img {
  width: 485px;
  border-radius: 5px;
  height: 300px;
  -o-object-fit: cover;
     object-fit: cover;
}
.website-theme .theme-coming-soon,
.website-theme .website-theme-active {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 5px;
  opacity: 0;
  visibility: hidden;
}
.website-theme .theme-coming-soon p,
.website-theme .website-theme-active p {
  font-size: 24px;
  font-weight: 500;
  line-height: 35;
  color: #ffffff;
}
.website-theme .website-theme-active {
  background-color: rgba(0, 0, 0, 0.4);
}
.website-theme .website-theme-active .check-icon {
  color: #ffffff;
  border: 2px solid #ffffff;
  height: 50px;
  width: 50px;
  display: grid;
  place-content: center;
  border-radius: 50%;
  font-size: 30px;
  padding-left: 0;
}
.website-theme .website-theme-activity {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  height: 80px;
  background-color: rgba(255, 255, 255, 0.4);
  display: grid;
  place-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.5s ease;
}
.website-theme:hover .website-theme-activity {
  opacity: 1;
  visibility: visible;
}

.website-theme.hero-section .website-thumb img {
  height: 200px;
}

.website-theme.header-section .website-thumb img {
  width: 355px;
  height: 100px;
}

.custom-radio input[type=radio]:checked + .website-theme .website-theme-active {
  visibility: visible;
  opacity: 1;
}

.custom-radio input[type=radio]:checked + .website-theme .website-theme-active.theme-coming-soon {
  background-color: rgba(0, 0, 0, 0.8);
}

.sortable-section {
  position: relative;
  border: 1px solid #d7dde9;
  padding: 20px 60px;
  border-radius: 10px;
}

.sortable-section .custom-checkbox {
  width: 17%;
}

ul.sortable-menu-icon li {
  position: absolute;
  top: 30px;
}
ul.sortable-menu-icon li.menuMove {
  cursor: move;
}

ul.sortable-menu-icon li:first-child {
  left: 20px;
  border-right: 1px solid #d7dde9;
  padding-right: 18px;
}

ul.sortable-menu-icon li:last-child {
  right: 20px;
}

ul.sortable-menu-icon li {
  font-size: 20px;
}
ul.sortable-menu-icon li a.delete-icon {
  color: #ff2424;
}

.list-group-item + .list-group-item {
  border-top-width: 1px !important;
}

.sortable-menu-section .list-group-item:first-child.nested-1 {
  border: none;
  padding: 0;
}

.sortable-menu-section .list-group-item:first-child.nested-1 .list-group {
  padding-left: 50px;
}

.sortable-menu-section .list-group-item:first-child {
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}

.sortable-menu-section .list-group-item:last-child {
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}

#mega-menu-area.custom-checkbox label span {
  font-size: 15px;
  padding-left: 25px;
}

.popover {
  background-color: #ffffff;
  padding: 10px;
  border-radius: 5px;
  border: 1px solid #d7dde9;
  font-size: 14px;
  line-height: 20px;
  font-weight: normal;
}
.popover a {
  color: var(--sg-primary);
  font-weight: 500;
  font-size: 14px;
}

.popover-body {
  padding: 0;
  color: #7e7f92;
}

span.info-content i {
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

span.info-content:hover i {
  color: #02a2b9;
}

span.color_picker_trick {
  background-color: rgba(255, 0, 0, 0);
  opacity: 0;
  position: absolute;
  width: 26px;
  height: 30px;
  cursor: pointer;
}

input.input-medium {
  border-radius: 5px 0 0 5px !important;
}

.input-append.color.colorpicker-component.colorpicker-element {
  display: flex;
  align-items: center;
}

span.add-on {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0 5px 5px 0;
}

.colorpicker-element .add-on i,
.colorpicker-element .input-group-addon i {
  display: inline-block;
  cursor: pointer;
  height: 40px;
  vertical-align: text-top;
  width: 40px;
  border-radius: 0px 5px 5px 0;
}

div.inputTags-list {
  background-color: #ffffff;
  border: 1px solid #d7dde9;
  border-radius: 5px;
  box-shadow: none;
  padding: 6px;
  width: 100%;
}

.inputTags-list:focus-within {
  border-color: var(--sg-btn-hover-border-color);
}

div.inputTags-list input.inputTags-field {
  color: #7e7f92;
}

div.inputTags-list span.inputTags-item {
  background-color: var(--sg-primary);
  color: #ffffff;
}

p.inputTags-error {
  display: none !important;
  background-color: transparent;
  border-radius: 4px;
  color: #ff2424;
  cursor: pointer;
  margin: 0;
  padding: 0.5em 1em;
  position: relative;
}

p.inputTags-error:first-of-type {
  margin-top: 0px;
}

.line-progress {
  width: 100%;
  box-sizing: border-box;
}

.line-progress [data-progress] {
  height: 6px;
  border-radius: 0px;
  margin: 5px 0 0px 0;
  overflow: hidden;
  background-color: #d9dcf9;
}

[data-progress].animate-progress::after {
  width: var(--animate-progress);
}

[data-progress]::after {
  content: "";
  display: flex;
  justify-content: flex-end;
  align-items: center;
  background: var(--sg-primary) !important;
  border-radius: 0px;
  width: 0;
  height: 100%;
  box-sizing: border-box;
  font-size: 10px;
  color: #fff;
  padding: 0 3px;
  transition: 2s;
}

.reply-card {
  border: 1px solid #d7dde9;
  border-radius: 10px;
  background-color: #ffffff;
  position: relative;
}

.viewed {
  background: #f1f3fb;
}

.reply-action-icon {
  position: absolute;
  top: 8px;
  right: 20px;
}
.reply-action-icon a {
  font-size: 20px;
  margin-left: 12px;
}

.submitter-info {
  padding-right: 40px;
  position: relative;
}
.submitter-info::after {
  position: absolute;
  right: 0;
  height: 100%;
  content: "";
  background-color: #d7dde9;
  width: 1px;
  top: 0;
}
.submitter-info h4, .submitter-info .h4 {
  font-size: 16px;
  line-height: 24px;
}

.ticket-content {
  padding-left: 14px;
}

/*======= Progress Bar =======*/
.line-progress {
  width: 100%;
  box-sizing: border-box;
  margin-bottom: 30px;
}
.line-progress p {
  color: #000000;
  display: flex;
  justify-content: space-between;
  align-items: center;
  text-transform: capitalize;
}
.line-progress *:not([data-progress]) {
  margin: 0;
  font-size: 14px;
}
.line-progress [data-progress] {
  height: 10px;
  border-radius: 12px;
  margin-top: 8px;
  overflow: hidden;
  background-color: #f1f3fb;
}

[data-progress]::after {
  content: "";
  display: flex;
  justify-content: flex-end;
  align-items: center;
  background-color: #fdcc0d;
  border-radius: 12px;
  width: 0;
  height: 100%;
  box-sizing: border-box;
  font-size: 10px;
  color: #ffffff;
  padding: 0 3px;
  transition: 2s;
}
[data-progress].animate-progress::after {
  width: var(--animate-progress);
}

/*======= Average Rating Summary =======*/
.average-rating-summary {
  display: flex;
  flex-wrap: wrap;
  gap: 15px 30px;
  align-items: center;
  padding: 80px 30px;
}
.average-rating-summary .average-rating {
  width: 223px;
  text-align: center;
  position: relative;
}
.average-rating-summary .average-rating h2, .average-rating-summary .average-rating .h2 {
  font-weight: 500;
  font-size: 70px;
  line-height: 1;
  margin-bottom: 10px;
}
.average-rating-summary .average-rating h5, .average-rating-summary .average-rating .h5 {
  margin-bottom: 5px;
  font-weight: 500;
  font-size: 16px;
}
.average-rating-summary .average-rating .rating-star i {
  color: #fdcc0d;
}
.average-rating-summary .average-rating .rating-star span {
  margin-top: 10px;
  display: block;
  font-size: 14px;
}
.average-rating-summary .average-rating::after {
  content: "";
  position: absolute;
  height: 100%;
  width: 1px;
  background-color: #d7dde9;
  top: 0;
  inset-inline-end: -30px;
}
.average-rating-summary .rating-summary-content {
  position: relative;
  flex: 1;
}
.average-rating-summary .rating-summary-content .rating-details {
  flex: 1;
  padding-inline-start: 80px;
}
.average-rating-summary .rating-summary-content .rating-details .star-count-progress {
  display: flex;
  align-items: center;
}
.average-rating-summary .rating-summary-content .rating-details .star-count-progress .rating-star {
  width: 175px;
  margin-inline-end: 10px;
}
.average-rating-summary .rating-summary-content .rating-details .star-count-progress .rating-star i {
  color: #fdcc0d;
  font-size: 19px;
}
.average-rating-summary .rating-summary-content .rating-details .star-count-progress .line-progress {
  position: relative;
  margin: 0;
  padding-inline-end: 45px;
}
.average-rating-summary .rating-summary-content .rating-details .star-count-progress .line-progress p {
  position: absolute;
  inset-inline-end: 0;
  top: -1px;
  line-height: 1;
  color: #7e7f92;
  font-weight: 400;
  font-size: 16px;
}
.average-rating-summary .rating-summary-content .rating-details .star-count-progress .line-progress [data-progress] {
  margin: 0;
  border-radius: 5px;
}
.average-rating-summary .rating-summary-content .rating-details .star-count-progress .line-progress [data-progress]::after {
  background: #fdcc0d;
  border-radius: 5px;
}
.average-rating-summary .rating-summary-content .rating-details .star-count-progress:not(:last-child) {
  margin-bottom: 20px;
}

.package-default {
  background-color: #ffffff;
  border: 1px solid #d7dde9;
  border-radius: 10px;
}
.package-action-bar {
  background: var(--sg-body-bg-color);
  border-radius: 10px 10px 0 0;
}
.package-action-bar ul li a {
  font-size: 20px;
  transition: color 0.2s;
}
.package-action-bar ul li a:hover {
  color: #ff2424;
}
.package-header .package-title {
  font-size: 36px;
  line-height: 52px;
}
.package-header-color {
  background-color: #d9dcf9;
}
.package-content .package-pirce {
  font-size: 36px;
  line-height: 52px;
  color: var(--sg-primary);
  padding: 20px 0;
}
.package-content ul.item-bg li:nth-child(odd) {
  background-color: #f1f3fb;
}
.package-content ul li {
  color: #7e7f92;
  font-weight: 500;
}
.package-content ul li:last-child {
  border-radius: 0 0 10px 10px;
}
.package-badge {
  background: var(--sg-primary);
  padding: 8px 30px;
  color: #ffffff;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 10px 0 0 0;
}

.statistics-view.dropdown .dropdown-toggle {
  font-size: 14px;
  color: #556068;
  font-weight: 500;
}
.statistics-view.dropdown .dropdown-toggle::after {
  inset-inline-end: 0;
}

.statistics-report .analytics-content p {
  color: #7e7f92;
}
.statistics-report .analytics-content h4, .statistics-report .analytics-content .h4 {
  font-size: 24px;
  line-height: 35px;
  color: #556068 !important;
}

.statistics-report-chart {
  width: 100%;
  height: 308px;
  margin-top: 10px;
}

.best-selling-courses {
  border-collapse: separate;
}

.best-selling-courses tbody {
  height: 260px;
  width: 100%;
  overflow-y: auto;
  display: block;
}
.best-selling-courses tbody tr {
  margin-bottom: 14px;
}
.best-selling-courses tbody::-webkit-scrollbar {
  width: 5px;
}
.best-selling-courses tbody::-webkit-scrollbar-track {
  box-shadow: none;
  background-color: #d3f7ed;
  border-radius: 4px;
}
.best-selling-courses tbody::-webkit-scrollbar-thumb {
  background-color: var(--sg-primary);
  border-radius: 4px;
  -webkit-transition: background-color 0.5s ease;
  transition: background-color 0.5s ease;
}

.best-selling-courses tbody tr,
.best-selling-courses thead tr {
  display: grid;
  grid-template-columns: 3fr 1fr 1fr 1fr;
}

.best-selling-courses thead tr th {
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  color: #556068;
  padding: 6px 0;
}
.best-selling-courses thead tr th :not(:first-child) {
  margin-left: -9px;
}

.best-selling-courses tr {
  vertical-align: middle;
  color: #7e7f92;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
}

.best-selling-courses tr td {
  padding: 0 !important;
}

.selling-course-title {
  gap: 16px;
}
.selling-course-title p {
  font-weight: 500;
  width: 220px;
  line-height: 26px;
  -webkit-line-clamp: 2;
}

.selling-course-thumb {
  background-color: #d7dde9;
  border-radius: 6px;
}
.selling-course-thumb img {
  width: 70px;
  height: 50px;
}

.best-instructor tbody tr,
.best-instructor thead tr {
  grid-template-columns: 2fr 1fr 1fr 1fr;
}

.best-instructor.table-call-3 tbody tr,
.best-instructor.table-call-3 thead tr {
  grid-template-columns: 2fr 2fr 1fr;
}

.best-selling-courses .rating i {
  color: #ffa600;
}

.instructors-pro {
  gap: 20px;
}

.inst-avtar img {
  height: 40px;
  width: 40px;
  border-radius: 50%;
}

.inst-intro h6, .inst-intro .h6 {
  color: #7e7f92;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
}
.inst-intro p {
  color: #7e7f92;
  font-size: 12px;
  line-height: 17px;
  font-weight: 500;
}

.recent-transactions tbody tr,
.recent-transactions thead tr {
  grid-template-columns: 3fr 2fr 1fr;
}

.recent-transactions tbody {
  height: 108px;
  width: 100%;
  overflow-y: auto;
  display: block;
}

.color-variant-1, .color-variant-2 {
  height: 40px;
  width: 40px;
  background: #d9dcf9;
  display: grid;
  place-content: center;
  border-radius: 6px;
}

.color-variant-2 {
  background-color: #d3f7ed;
}

.statistics-info h6, .statistics-info .h6 {
  line-height: 24px;
  margin-bottom: 8px;
}
.statistics-info h4, .statistics-info .h4 {
  font-size: 24px;
  line-height: 35px;
}
.statistics-gChart {
  height: 70px;
  width: 100%;
}
.statistics-footer .sales-price {
  padding: 10px 20px;
  color: var(--sg-primary);
  border-radius: 10px;
  font-size: 14px;
  line-height: 20px;
  font-weight: 500;
  background-color: #d9dcf9;
}
.statistics-icon {
  height: 60px;
  width: 60px;
  background-color: #d9dcf9;
  border-radius: 10px;
  display: grid;
  place-content: center;
}

.color-success .statistics-info h4, .color-success .statistics-info .h4 {
  color: #24d6a5;
}
.color-success .statistics-footer .sales-price {
  color: #24d6a5;
  background-color: #d3f7ed;
}
.color-success .statistics-icon {
  background-color: #d3f7ed;
}
.color-success .statistics-icon svg path {
  fill: #24d6a5;
}

.color-warning .statistics-info h4, .color-warning .statistics-info .h4 {
  color: #ffa600;
}
.color-warning .statistics-footer .sales-price {
  color: #ffa600;
  background-color: #ffedcc;
}

.color-danger .statistics-info h4, .color-danger .statistics-info .h4 {
  color: #ff5630;
}
.color-danger .statistics-footer .sales-price {
  color: #ff5630;
  background-color: #ffddd6;
}
.color-danger .statistics-icon {
  background-color: #ffddd6;
}
.color-danger .statistics-icon svg path {
  fill: #ff5630;
}

.color-primary .statistics-info h4, .color-primary .statistics-info .h4 {
  color: var(--sg-primary);
}
.color-primary .statistics-footer .sales-price {
  color: var(--sg-primary);
  background-color: #d9dcf9;
}

.color-blue .statistics-info h4, .color-blue .statistics-info .h4 {
  color: #3f52e3;
}
.color-blue .statistics-footer .sales-price {
  color: #3f52e3;
  background-color: #d9dcf9;
}

.statistics-view ul {
  border-radius: 10px;
  inset: 10px auto auto 0 !important;
  box-shadow: 0px 5px 20px rgba(0, 0, 0, 0.15);
  border: none;
  padding: 20px 0;
  background-color: #ffffff;
}
.statistics-view ul li a {
  font-size: 16px;
  font-weight: 400;
  color: #7e7f92;
  padding: 5px 20px;
}
.statistics-view ul li a:hover {
  background-color: #f1f3fb;
  color: #7e7f92;
}
.statistics-view ul li:not(:last-child) a {
  margin-bottom: 5px;
}

.two-call-table tbody tr,
.two-call-table thead tr {
  grid-template-columns: 3fr 1fr;
}

.two-call-table thead tr th:last-child,
.two-call-table tbody tr td:last-child {
  text-align: right;
  margin-right: 10px;
}

.social-proof-section {
  gap: 75px;
  flex-wrap: wrap;
}

.single-social-proof {
  background-color: #ffffff;
  box-shadow: 0px 5px 20px rgba(0, 0, 0, 0.1);
  border-radius: 20px;
  padding: 20px 68px 20px 30px;
}

.social-proof-thumb {
  border-radius: 6px;
}
.social-proof-thumb img {
  width: 100px;
  height: 100px;
  border-radius: 6px;
}

.social-proof-content h6, .social-proof-content .h6 {
  font-size: 24px;
  line-height: 35px;
  font-weight: 500;
  color: var(--sg-primary);
}
.social-proof-content p, .social-proof-content span {
  color: #556068;
  font-weight: 400;
  font-size: 16px;
  line-height: 26px;
}

.preview-img {
  width: 258px;
}

.chat-sidebar-active-user .oftions-content-search {
  width: initial;
}
.chat-sidebar-active-user .oftions-content-search button[type=submit] {
  margin-left: auto;
}
.chat-sidebar-active-user .oftions-content-search input[type=search] {
  width: 100%;
}

.user-info-box {
  gap: 20px;
  transition: background-color 0.5s ease;
}
.user-info-box .user-img {
  position: relative;
  flex: 0 0 60px;
}
.user-info-box .user-img img {
  height: 60px;
  width: 60px;
  -o-object-fit: cover;
     object-fit: cover;
  border-radius: 50%;
}
.user-info-box .user-img span.active_status {
  top: 0px;
  inset-inline-start: 46px;
}
.user-info-box .user-name {
  font-weight: 500;
  line-height: 24px;
  -webkit-line-clamp: 2;
  flex: 0 0 205px;
}

.chat-sidebar-scrollable {
  max-height: 810px;
  min-height: 810px;
}
.chat-sidebar-scrollable .user-info-box {
  cursor: pointer;
}
.chat-sidebar-scrollable .user-info-box:hover {
  background-color: #f1f3fb;
}
.chat-sidebar-scrollable.simplebar::-webkit-scrollbar-track, .chat-sidebar-scrollable.simplebar::-webkit-scrollbar-thumb {
  display: none;
}
.chat-sidebar-scrollable.simplebar:hover::-webkit-scrollbar-thumb {
  display: block;
  background-color: #d7dde9;
}

.chat-content-header .user-info-box .user-name {
  flex: 0 0 auto;
  max-width: 100%;
  padding-right: 45px;
}

.chat-bg {
  background-color: #f1f3fb;
}
.chat-bg-primary {
  background-color: var(--sg-primary);
}

.chat-content-body {
  max-height: 728px;
  min-height: 728px;
}
.chat-content-body.simplebar::-webkit-scrollbar-track, .chat-content-body.simplebar::-webkit-scrollbar-thumb {
  display: none;
}
.chat-content-body.simplebar:hover::-webkit-scrollbar-thumb {
  display: block;
  background-color: #d7dde9;
}

.chat-content-body .message-sender:not(:last-child) {
  margin-bottom: 24px;
}

.message-sender {
  display: flex;
  justify-content: flex-end;
  flex-flow: row wrap;
  margin: 0px 15px;
}
.message-sender .message-text {
  max-width: -moz-max-content;
  max-width: max-content;
  width: 86%;
  display: flex;
  flex-direction: column;
}

.message-sender .message-text .send-date {
  text-align: right;
}
.message-sender.user {
  justify-content: flex-start !important;
}
.message-sender.user .message-text p {
  color: #7e7f92;
}
.message-sender.user .send-date {
  text-align: left;
}

.chat-form-icon li a {
  font-size: 24px;
}

.emoIcon {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  font-size: 24px;
  right: 25px;
  cursor: pointer;
}

/*======= Certificate =======*/
/* .certificate {
      width: 730px;
      height: 516px;
      position: relative;
      background: #F1F2EB;
    }
    .certificate_img img {
      height: 100%;
      width: 100%;
    }
    .certificate_info_img img {
      width: 250px;
      height: auto;
      margin: 0 auto;
    }
    .certificate_content h2 {
      font-size: 28px;
    }
    .certificate_content p {
        width: 520px;
        line-height: 22px;
    }
    .org_logo img {
      width: 85px;
      height: 85px;
      border-radius: 50%;
    }
    .certificate_content_wrap {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
    }
    .certificate_sign_info {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 100px;
    }

    .certificate_sign_info {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 48px;
        margin-top: 40px;
    }
    .signature h6 {
      font-size: 16px;
      border-top: 1px solid #666;
      padding-top: 6px;
      color: #333;
    }
    .signature img {
      max-width: 170px;
      height: auto;
      padding-bottom: 4px;
      object-fit: contain;
    }
    .certificate_content {
      margin-top: 16px;
    }
    .registration-number {
      position: absolute;
      top: 70px;
      left: 70px;
    }
    .registration-number span {
    color: #556068;
    font-size: 12px;
    line-height: 28px;
    font-weight: 400;
    } */
.certificate {
  width: 720px;
  height: 516px;
  position: relative;
  background: #f1f2eb;
}

.certificate_img img {
  height: 100%;
  width: 100%;
}

.certificate_info_img img {
  width: 250px;
  height: auto;
  margin: 0 auto;
}

.certificate_content h2, .certificate_content .h2 {
  font-size: 26px;
}

.certificate_content p {
  width: 520px;
  line-height: 22px;
  margin: 0 auto;
}

.org_logo img {
  width: 85px;
  height: 85px;
  border-radius: 50%;
}

.certificate_content_wrap {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.certificate_sign_info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 100px;
}

.certificate_sign_info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 48px;
  margin-top: 40px;
}

.signature h6, .signature .h6 {
  font-size: 16px;
  border-top: 1px solid #666;
  padding-top: 6px;
  color: #333;
}

.signature img {
  max-width: 170px;
  height: auto;
  padding-bottom: 4px;
  -o-object-fit: contain;
     object-fit: contain;
}

.certificate_content {
  margin-top: 16px;
}

.registration-number {
  position: absolute;
  top: 70px;
  left: 70px;
}

.registration-number span {
  color: #556068;
  font-size: 12px;
  line-height: 28px;
  font-weight: 400;
}

@media screen and (min-width: 1600px) and (max-width: 1799px) {
  .certificate-scrollable .certificate {
    width: 615px !important;
    height: 436px;
  }
  .certificate_info_img img {
    width: 190px !important;
  }
  .certificate_sign_info {
    gap: 35px !important;
    margin-top: 22px !important;
  }
}
@media screen and (min-width: 1200px) and (max-width: 1599px) {
  .certificate-scrollable {
    overflow-y: scroll !important;
  }
  .certificate-scrollable .certificate {
    width: 470px !important;
  }
  .certificate.certificate-respon {
    width: inherit;
    height: inherit;
  }
  .certificate-respon .certificate_sign_info {
    gap: 30px;
    margin-top: 15px;
  }
  .certificate-respon .certificate_info_img img {
    width: 145px;
  }
  .certificate-respon .certificate_content h2, .certificate-respon .certificate_content .h2 {
    font-size: 18px;
  }
  .certificate-respon .certificate_content p {
    width: 398px;
    line-height: 16px;
    margin: 0 auto;
    font-size: 13px;
  }
  .certificate-respon .signature img {
    max-width: 110px;
  }
  .certificate-respon .signature h6, .certificate-respon .signature .h6 {
    font-size: 14px;
  }
  .certificate-respon .org_logo img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
  }
  .certificate-respon .registration-number {
    position: absolute;
    top: 70px;
    left: 50px;
  }
}
@media screen and (max-width: 991px) {
  .certificate {
    width: inherit;
    height: inherit;
  }
  .certificate_info_img img {
    width: 200px;
  }
}
@media screen and (max-width: 775px) {
  .certificate_content h2, .certificate_content .h2 {
    font-size: 18px;
  }
  .certificate_content p {
    width: 420px;
    font-size: 14px;
    margin: 0 auto;
  }
  .certificate_sign_info {
    gap: 30px;
    margin-top: 30px;
  }
  .signature img {
    max-width: 125px;
  }
  .signature h6, .signature .h6 {
    font-size: 14px;
  }
  .org_logo img {
    width: 60px;
    height: 60px;
  }
}
@media screen and (max-width: 575px) {
  .certificate_info_img img {
    width: 150px;
  }
  .certificate_content h2, .certificate_content .h2 {
    font-size: 16px;
  }
  .certificate_content p {
    width: 350px;
    font-size: 14px;
    margin: 0 auto;
  }
  .certificate_sign_info {
    gap: 25px;
    margin-top: 20px;
  }
  .signature img {
    max-width: 120px;
  }
  .signature h6, .signature .h6 {
    font-size: 12px;
  }
  .org_logo img {
    width: 50px;
    height: 50px;
  }
  .certificate-scrollable {
    overflow-y: scroll;
  }
  .certificate-scrollable .certificate {
    width: 470px !important;
  }
}
.card {
  background-color: #ffffff;
  border: 1px solid #d7dde9;
}
.card-header {
  padding: 20px 30px;
  border-bottom: 1px solid #d7dde9;
  background-color: transparent;
}
.card-header h4, .card-header .h4 {
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
}
.card-body {
  padding: 30px;
}

.form-control {
  background: #ffffff;
  height: 40px;
  padding-left: 20px;
  color: #7e7f92 !important;
  border-radius: 5px;
}
.form-control:focus {
  border-color: var(--sg-btn-hover-border-color);
}
.form-control:focus, .form-control.is-invalid:focus {
  outline: none;
  box-shadow: none;
}
.form-control.is-invalid:focus {
  border-color: #ff2424;
}
.form-control::-moz-placeholder {
  color: #7e7f92;
}
.form-control::placeholder {
  color: #7e7f92;
}

textarea.form-control {
  min-height: 100px;
  resize: none;
}

.form-floating label {
  color: #bfbfbf;
}

.custom-radio input[type=radio] {
  display: none;
}
.custom-radio input[type=radio] + span::before, .custom-radio input[type=radio] + span::after {
  transition: all 0.3s;
}
.custom-radio input[type=radio] + span::after {
  position: absolute;
  left: 0px;
  top: 3px;
  display: inline-block;
  content: "";
  width: 20px;
  height: 20px;
  border: 2px solid #d7dde9;
  border-radius: 50%;
  background-color: #ffffff;
  z-index: 100;
}
.custom-radio input[type=radio]:checked + span::after {
  background-color: #ffffff;
  border-color: var(--sg-primary);
}
.custom-radio input[type=radio]:checked + span::before {
  transform: rotate(45deg);
  position: absolute;
  left: 4px;
  top: 7px;
  width: 12px;
  height: 12px;
  background-color: var(--sg-primary);
  content: "";
  z-index: 900;
  border-radius: 50%;
}
.custom-radio label {
  position: relative;
  cursor: pointer;
}
.custom-radio label span {
  font-weight: 400;
  font-size: 16px;
  line-height: 26px;
  color: #7e7f92;
  position: relative;
  padding-left: 25px;
}

.custom-checkbox input[type=checkbox] {
  display: none;
}
.custom-checkbox input[type=checkbox] + span::before, .custom-checkbox input[type=checkbox] + span::after {
  transition: all 0.3s;
}
.custom-checkbox input[type=checkbox] + span::after {
  position: absolute;
  left: 0px;
  top: 3px;
  display: inline-block;
  content: "";
  width: 20px;
  height: 20px;
  border: 2px solid #d7dde9;
  border-radius: 0px;
  background-color: #ffffff;
  z-index: 100;
}
.custom-checkbox input[type=checkbox] + span.redious-border-5::after {
  border-radius: 5px;
}
.custom-checkbox input[type=checkbox]:checked + span::after {
  background-color: #ffffff;
  border-color: var(--sg-primary);
}
.custom-checkbox input[type=checkbox]:checked + span::before {
  transform: rotate(45deg);
  position: absolute;
  left: 7px;
  top: 6px;
  width: 6px;
  height: 11px;
  border-width: 2px;
  border-style: solid;
  border-top: 0;
  border-left: 0;
  border-color: var(--sg-primary);
  content: "";
  z-index: 900;
}
.custom-checkbox label {
  position: relative;
  cursor: pointer;
}
.custom-checkbox label span {
  font-weight: 400;
  font-size: 16px;
  line-height: 26px;
  color: #7e7f92;
  position: relative;
  padding-left: 35px;
}

.setting-check {
  position: relative;
  top: 4px;
}
.setting-check input {
  display: none;
  opacity: 0;
}
.setting-check input:checked ~ label {
  background: var(--sg-primary);
}
.setting-check input:checked ~ label:after {
  left: 22px;
}
.setting-check label {
  position: relative;
  width: 44px;
  height: 24px;
  display: inline-block;
  background: #d7dde9;
  border-radius: 30px;
  cursor: pointer;
  transition: 0.3s;
}
.setting-check label::after {
  content: "";
  position: absolute;
  left: 2px;
  top: 2px;
  width: 20px;
  height: 20px;
  background: #ffffff;
  border-radius: 50%;
  transition: 0.3s;
}

.lang-select {
  width: 120px;
  height: 40px;
}

.phone-field {
  border: 1px solid #d7dde9;
  transition: all 0.5s ease;
}
.phone-field:focus-within {
  border-color: var(--sg-btn-hover-border-color);
}

.country-code-select {
  height: 40px;
  padding: 7px 30px 7px 10px;
  border-radius: 5px 0 0 5px;
  transition: all 0.5s ease;
  border-right: 1px solid #d7dde9;
}
.country-code-select:hover {
  background-color: #f1f3fb;
  color: #556068;
}

.phone-number {
  width: 81.9%;
}
.phone-number input {
  height: 40px;
  border-radius: 5px;
  width: 100%;
  border: none;
  padding: 0 10px;
  color: #556068;
}
.phone-number input:focus {
  outline: 0;
}

.country-code-filter {
  position: relative;
}
.country-code-filter li a {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #7e7f92;
}
.country-code-filter li a .country-code-number {
  color: #7e7f92;
  font-size: 16px;
  font-weight: 400;
}
.country-code-filter li a .country-flag img {
  height: auto;
  width: 20px;
}
.country-code-filter li a.dropdown-toggle::after {
  inset-inline-end: -25px;
  font-size: 15px;
}
.country-code-filter li ul {
  width: 100%;
  max-height: 200px;
  overflow-y: auto;
  padding-top: 0;
  inset: 8px auto auto -11px !important;
  border: 1px solid #d7dde9;
  border-radius: 5px;
}
.country-code-filter li ul li {
  transition: all 0.5s ease;
}
.country-code-filter li ul li:not(:last-child) {
  margin-bottom: 5px;
}
.country-code-filter li ul li a:hover, .country-code-filter li ul li a:focus {
  background-color: #f1f3fb !important;
}
.country-code-filter li ul .country-search {
  position: sticky;
  top: 0px;
  left: 0;
  border: none;
  border-bottom: 1px solid #d7dde9;
  padding-left: 10px;
  font-size: 14px;
  width: 100%;
  height: 35px;
  margin-bottom: 5px;
}
.country-code-filter li ul .country-search:focus {
  outline: none;
  border-bottom: 1px solid var(--sg-btn-hover-border-color);
}

.country-code-select.orgInstructor {
  width: 26%;
}

.fixed-btn .country-code-select {
  border-right: 0;
  border-left: 1px solid #d7dde9;
  border-radius: 0px 5px 5px 0px;
}

.add-coupon .phone-number {
  width: 91%;
}
.add-coupon .country-code-select {
  width: 9%;
  border-right: 0;
  border-inline-start: 1px solid #d7dde9;
  border-radius: 0 5px 5px 0;
}

.input-group .custom-radio,
.input-group .custom-checkbox {
  position: absolute;
  right: 58px;
  top: 6px;
  z-index: 99;
}
.input-group .custom-radio span,
.input-group .custom-checkbox span {
  padding-left: 25px;
}
.input-group .custom-radio .icon,
.input-group .custom-checkbox .icon {
  font-size: 20px;
  padding-left: 13px;
  margin-bottom: -3px;
  cursor: pointer;
}
.input-group .custom-radio .icon :hover,
.input-group .custom-checkbox .icon :hover {
  color: #ff2424;
}

span.input-group-text {
  color: #7e7f92;
  cursor: pointer;
  background-color: #d7dde9;
}

.select-type-v2.selectField {
  position: absolute;
  top: 0;
  right: 0;
  height: 80%;
  background: transparent;
}

.customDiscountField {
  position: relative;
}

.customDiscountField .form-control {
  padding-right: 150px;
}

.calender-icon {
  position: relative;
}
.calender-icon::after {
  position: absolute;
  content: "\f073";
  font-family: "Line Awesome Free";
  font-weight: 900;
  top: 50%;
  right: 15px;
  transform: translateY(-50%);
  font-size: 20px;
}

input#addon_file.form-control {
  padding-left: 0;
}

input#addon_file[type=file] {
  padding-left: 12px;
}

.note-modal-footer {
  height: 80px !important;
  padding: 10px 30px !important;
  text-align: center;
}

.form-label {
  display: block;
  color: #7e7f92;
  line-height: 26px;
  margin-bottom: 4px;
  font-size: 16px;
  font-weight: 500;
}

.file-upload-text {
  border: 1px solid #d7dde9;
  border-radius: 5px;
  width: 100%;
  height: 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-left: 16px;
  cursor: pointer;
}
.file-upload-text span.file-btn {
  color: #7e7f92;
  background: #d7dde9;
  padding: 7px 20px;
  font-size: 16px;
  line-height: 26px;
  cursor: pointer;
  border-radius: 0 5px 5px 0;
}
.file-upload-text p {
  text-overflow: ellipsis;
  display: -webkit-box;
  overflow: hidden;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  max-width: 75%;
}

.selected-files-item {
  position: relative;
}
.selected-files-item .selected-img {
  height: 80px;
  width: 80px;
  background: #e5e5e5;
  border-radius: 5px;
  position: relative;
  -o-object-fit: contain;
     object-fit: contain;
}
.selected-files-item .remove-icon {
  position: absolute;
  top: -10px;
  right: -10px;
  background: #c7c7c7;
  height: 20px;
  width: 20px;
  display: grid;
  place-content: center;
  border-radius: 50px;
  cursor: pointer;
}
.selected-files-item .remove-icon i {
  color: #434343;
  font-size: 12px;
}

.file-upload-text.reverse-btn {
  padding-left: 0;
  justify-content: start;
  gap: 20px;
  flex-direction: row-reverse;
}
.file-upload-text.reverse-btn .file-btn {
  border-radius: 5px 0 0 5px;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-background-clip: text;
  -webkit-text-fill-color: #7e7f92;
  font-weight: normal;
  -webkit-transition: background-color 5000s ease-in-out 0s;
  transition: background-color 5000s ease-in-out 0s;
}

.select2-container--default {
  width: 100% !important;
}
.select2-container--default.select2-container--focus.select2-container--open .select2-selection--single {
  border-color: var(--sg-btn-hover-border-color);
}
.select2-container--default .select2-selection--single {
  background-color: #ffffff;
  border: 1px solid #d7dde9;
  border-radius: 5px;
  height: 40px;
  width: 100% !important;
  display: flex;
  align-items: center;
  transition: border-color 0.5s ease;
}
.select2-container--default .select2-selection--single .select2-selection__arrow {
  height: 26px;
  position: absolute;
  top: 50%;
  right: 15px;
  width: 20px;
  transform: translateY(-50%);
  transition: all 0.5 ease;
  transform-origin: center;
}
.select2-container--default .select2-selection--single .select2-selection__arrow b {
  border-color: transparent;
  border-style: solid;
  border-width: 0;
}
.select2-container--default .select2-selection--single .select2-selection__arrow::after {
  position: absolute;
  content: "\f107";
  font-family: "Line Awesome Free";
  font-weight: 900;
  transition: all 0.5 ease;
  transform-origin: center;
}
.select2-container--default .select2-selection--single .select2-selection__rendered {
  padding-left: 20px;
  color: #7e7f92;
  line-height: 26px;
}
.select2-container--default .select2-selection--single .select2-selection__placeholder {
  color: #7e7f92;
  font-weight: 400;
  font-size: 16px;
  line-height: 26px;
}
.select2-container--default .select2-selection--single:focus-visible {
  outline: 0;
  border: 1px solid var(--sg-btn-hover-border-color);
}
.select2-container--default .select2-dropdown {
  border-radius: 0 0 5px 5px;
  border-color: var(--sg-btn-hover-border-color);
  z-index: 1018;
}
.select2-container--default .select2-results__option {
  padding: 2px 20px;
  -moz-user-select: none;
       user-select: none;
  -webkit-user-select: none;
  font-weight: 400;
  line-height: 26px;
  font-size: 16px;
}
.select2-container--default .select2-results__option--highlighted.select2-results__option--selectable {
  background-color: #f1f3fb;
  color: #7e7f92;
}
.select2-container--default .select2-results__option--highlighted.select2-results__option--selectable:last-child {
  border-radius: 0 0 5px 5px;
}
.select2-container--default .select2-results__option--selected {
  background-color: #f1f3fb;
}
.select2-container--default .select2-search--dropdown .select2-search__field {
  border: 1px solid #d7dde9;
  height: 30px;
  border-radius: 5px;
  padding-left: 10px;
  color: #7e7f92;
  font-size: 15px;
}
.select2-container--default .select2-search--dropdown .select2-search__field:focus {
  outline: 0;
}
.select2-container--default .oftions-content-left .select2-container--default .select2-selection__arrow {
  right: 10%;
}
.select2-container--default .select2-results > .select2-results__options::-webkit-scrollbar {
  width: 8px;
  margin-right: 20px;
}
.select2-container--default .select2-results > .select2-results__options::-webkit-scrollbar-track {
  box-shadow: none;
  background-color: #d3f7ed;
  border-radius: 2px;
  margin: 10px 0px;
}
.select2-container--default .select2-results > .select2-results__options::-webkit-scrollbar-thumb {
  background-color: var(--sg-primary);
  border-radius: 3px;
  -webkit-transition: background-color 0.5s ease;
  transition: background-color 0.5s ease;
}
.select2-container--default .select2-selection--multiple {
  background-color: #ffffff;
  border: 1px solid #d7dde9;
  border-radius: 5px;
  cursor: text;
  padding-bottom: 0px;
  padding-right: 0px;
  position: relative;
  min-height: 40px;
  display: flex;
  align-items: center;
}
.select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #d7dde9;
  border: 1px solid #d7dde9;
  font-size: 14px;
  font-weight: 400;
  line-height: 24px;
  color: #556068;
  border-radius: 4px;
  margin-top: 0px;
  padding: 2px 20px 2px 15px;
  margin-bottom: 2px;
}
.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  background-color: transparent;
  border: none;
  border-right: none;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
  color: #999;
  cursor: pointer;
  font-size: 1em;
  font-weight: bold;
  padding: 0 4px;
  position: absolute;
  left: auto;
  top: 1px;
  right: 4px;
}
.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  background-color: transparent;
}
.select2-container--default.select2-container--focus .select2-selection--multiple {
  border: 1px solid var(--sg-btn-hover-border-color);
  outline: 0;
}

.select2-container--default.select2-container--open .select2-selection--single .select2-selection__arrow b {
  border-color: transparent !important;
  border-width: 0 !important;
}

.select2-container--default.select2-container--open .select2-selection--single .select2-selection__arrow::after {
  transform: rotate(180deg);
}

.select-type-v2 .select2-container--default .select2-selection--single {
  border-radius: 5px;
}
.select-type-v2 .select2-container--default .select2-dropdown {
  border-radius: 5px;
}
.select-type-v2 .select2-container--default .select2-dropdown {
  border-radius: 5px;
}

.select-type-v3 .select2-container--default .select2-selection--single {
  border-radius: 20px;
}
.select-type-v3 .select2-container--default .select2-dropdown {
  border-radius: 5px;
}
.select-type-v3 .select2-container--default .select2-dropdown {
  border-radius: 5px;
}

.oftions-content-left .select2-container--default {
  width: 15% !important;
}

span.select2-search.select2-search--inline {
  min-height: 40px;
  display: flex !important;
  align-items: center !important;
  vertical-align: middle;
}

.select2-container .select2-search--inline .select2-search__field {
  font-family: "Outfit", sans-serif;
  margin-top: 0;
  margin-left: 20px;
  padding-top: 8px;
  height: 40px;
}
.select2-container .select2-search--inline .select2-search__field::-moz-placeholder {
  color: #7e7f92;
  font-size: 16px;
  line-height: 26px;
  font-weight: 400;
}
.select2-container .select2-search--inline .select2-search__field::placeholder {
  color: #7e7f92;
  font-size: 16px;
  line-height: 26px;
  font-weight: 400;
}

.select2-container--default.select2-container--open.select2-container--below .select2-selection--single,
.select2-container--default.select2-container--open.select2-container--below .select2-selection--multiple {
  border-color: var(--sg-btn-hover-border-color);
}

.choices .choices__inner {
  display: block;
  width: 100%;
  padding: 7px 20px;
  font-size: 1rem;
  font-weight: 400;
  line-height: 26px;
  color: #556068;
  border-radius: 0;
  border-color: #d7dde9;
  background-color: #ffffff;
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 16px;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  min-height: 40px;
}
.choices[data-type*=select-one]::after {
  content: "\f107";
  font-family: "Line Awesome Free";
  font-weight: 900;
  height: 20px;
  width: 20px;
  border-style: none;
  border-color: transparent;
  border-width: 0;
  position: absolute;
  right: 20px;
  top: 50%;
  margin-top: 0;
  pointer-events: none;
  transform: translateY(-50%);
  transform-origin: center;
  transition: 0.5s ease;
  display: grid;
  place-content: center;
}
.choices[data-type*=select-one].is-open::after {
  content: "\f106";
  border-color: transparent;
  margin-top: 0;
}
.choices[data-type*=select-one] .choices__inner {
  padding-bottom: 7px;
  border-color: #d7dde9 !important;
}
.choices:focus {
  outline: none;
  box-shadow: none;
}

.choices__list--single {
  display: inline-block;
  padding: 0;
  width: 100%;
}

.choices__list--dropdown .choices__item,
.choices__list[aria-expanded] .choices__item {
  position: relative;
  padding: 0px 20px;
  font-size: 16px;
  color: #7e7f92;
  line-height: 26px;
}

.choices__list--single .choices__item {
  color: #7e7f92;
}

.is-focused .choices__inner,
.is-open .choices__inner,
.is-open .choices__list--dropdown,
.is-open .choices__list[aria-expanded] {
  border-color: var(--sg-btn-hover-border-color);
}

.choices__list--dropdown .choices__list {
  padding: 7px;
}

.list-space .choices__list--dropdown .choices__list {
  padding: 50px 0 7px;
}

.multi-select-v2 .choices__list--dropdown .choices__list {
  padding: 7px 0;
}

input.instructor-search-field.choices__input--cloned {
  background-color: transparent;
}

.choices__list--dropdown,
.choices__list[aria-expanded] {
  z-index: 990;
}

.choices__list--dropdown .choices__item--selectable.is-highlighted,
.choices__list[aria-expanded] .choices__item--selectable.is-highlighted {
  background-color: transparent;
  color: #556068;
  transition: color 0.3s ease;
}

.select-type-v2 .choices__inner {
  border-radius: 5px;
}

.select-type-v2 .is-open .choices__inner {
  border-radius: 5px 5px 0 0;
}

.select-type-v2 .choices__list {
  border-radius: 0 0 5px 5px !important;
}

.select-type-v3 .choices__inner,
.select-type-v3 .is-open .choices__inner,
.select-type-v3 .choices__list,
.select-type-v3 .choices[data-type*=select-one] .instructor-search-field {
  border-radius: 20px;
}

.select-type-v3 .choices__list--dropdown,
.select-type-v3 .choices__list[aria-expanded] {
  top: 104%;
}

.choices[data-type*=select-multiple] .choices__button {
  position: relative;
  display: inline-block;
  margin: 0;
  padding-left: 16px;
  border-left: none;
  color: #7e7f92;
}

.choices__list--multiple .choices__item {
  display: inline-block;
  vertical-align: middle;
  border-radius: 0;
  padding: 2px 10px;
  font-size: 14px;
  font-weight: 500;
  margin-right: 8px;
  margin-bottom: 3px;
  background-color: #d7dde9;
  border: 1px solid #d7dde9;
  color: #7e7f92;
  word-break: break-all;
  box-sizing: border-box;
  line-height: 20px;
}

.choices[data-type*=select-multiple] .choices__inner {
  cursor: text;
  padding-bottom: inherit;
}

.choices__list--multiple .choices__item.is-highlighted {
  background-color: #d7dde9;
  border: 1px solid #d7dde9;
}

.multi-select-v1 .choices .choices__inner,
.multi-select-v2 .choices .choices__inner {
  padding: 2px 10px 0px;
  display: flex;
  align-items: center;
  border-color: #d7dde9;
}

.multi-select-v2 .choices .choices__inner,
.multi-select-v2 .choices__list--multiple .choices__item {
  border-radius: 5px;
}

.multi-select-v2 .is-open .choices__inner {
  border-radius: 5px 5px 0 0;
}

.instructor-search-field {
  padding: 2px 0 2px 2px;
}

.oftions-content-left .choices {
  width: 85px;
}

.organisationPayment {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}
.organisationPayment label {
  color: #556068;
  font-weight: 500;
}
.organisationPayment .select-type-v2 {
  width: 300px;
}

.choices[data-type*=select-one] .instructor-search-field {
  display: block;
  width: initial;
  padding: 5px 10px;
  border: 1px solid #d7dde9;
  background-color: #fff;
  margin: 12px 20px 8px;
  height: 30px;
  position: absolute;
  left: 0;
  right: 0;
  z-index: 5;
}

.choices[data-type*=select-one] .instructor-search-field:focus {
  outline: 0;
}

.instructor-mult-field {
  display: inline-block;
  vertical-align: baseline;
  background-color: transparent;
  font-size: 14px;
  margin-bottom: 5px;
  border: 0;
  border-radius: 0;
  max-width: 100%;
  padding: 4px 0 4px 2px;
}

.instructor-mult-field:focus {
  outline: 0;
}

.select-type-v2 .choices[data-type*=select-one] .instructor-search-field {
  border-radius: 5px;
}

.choices__list.choices__list--dropdown {
  --scrollbarBG: #e5e5e5;
  --scrollbarTHUMB: #7e7f92;
  overflow-y: auto;
  overflow-x: hidden;
}
.choices__list.choices__list--dropdown::-webkit-scrollbar {
  width: 8px;
}
.choices__list.choices__list--dropdown::-webkit-scrollbar-track {
  box-shadow: none;
  background-color: var(--scrollbarBG);
  border-radius: 4px;
}
.choices__list.choices__list--dropdown::-webkit-scrollbar-thumb {
  background-color: var(--scrollbarTHUMB);
  border-radius: 4px;
  -webkit-transition: background-color 0.5s ease;
  transition: background-color 0.5s ease;
}

.choices__list--dropdown .choices__list::-webkit-scrollbar {
  width: 8px;
  margin-right: 20px;
}

.choices__list--dropdown .choices__list::-webkit-scrollbar-track {
  box-shadow: none;
  background-color: #bfbfbf;
  border-radius: 2px;
  margin: 10px 0px;
}

.list-space .choices__list--dropdown .choices__list::-webkit-scrollbar-track {
  margin: 50px 0px 10px;
}

.choices__list--dropdown .choices__list::-webkit-scrollbar-thumb {
  background-color: #556068;
  border-radius: 3px;
  -webkit-transition: background-color 0.5s ease;
  transition: background-color 0.5s ease;
}

.select-type-v1 .choices__list--dropdown .choices__list::-webkit-scrollbar-track,
.select-type-v1 .choices__list--dropdown .choices__list::-webkit-scrollbar-thumb {
  border-radius: 0px;
}

.select-type-v3 .choices__list--dropdown .choices__list::-webkit-scrollbar-track,
.select-type-v3 .choices__list--dropdown .choices__list::-webkit-scrollbar-thumb {
  border-radius: 4px;
}

.pad-rt .select2-container .select2-selection--single .select2-selection__rendered {
  padding-right: 48px;
}

.customDiscountField .select-type-v2 {
  width: 150px;
}

.customDiscountField .select-type-v2 .select2-container--default .select2-selection--single {
  border-radius: 0 5px 5px 0;
  transition: all 0.3s;
  border: 1px solid transparent;
  background: transparent;
}

.customDiscountField .select2-container--default.select2-container--focus.select2-container--open .select2-selection--single {
  border-radius: 0 5px 0 0 !important;
}

.customDiscountField .select-type-v2 .select2-container--default .select2-selection--single {
  background-color: #f1f3fb;
  color: #556068;
}

.customDiscountField .select-type-v2 .select2-container--default.select2-container--open .select2-selection--single {
  border-color: var(--sg-btn-hover-border-color) !important;
}


.accordion {
  margin-top: 30px;
}

.accordion-item {
  margin-bottom: 24px;
  background-color: #ffffff;
  border: 1px solid #d7dde9;
  border-radius: 20px;
}
.accordion-item:first-of-type {
  border-radius: 20px;
}
.accordion-item:first-of-type .accordion-button {
  border-radius: 20px 20px 0 0;
}
.accordion-item:first-of-type .accordion-button.collapsed {
  border-radius: 20px;
}
.accordion-item:not(:first-of-type) {
  border-top: 1px solid #d7dde9;
}
.accordion-item:last-of-type {
  border-radius: 20px;
  border-top: 1px solid #d7dde9;
}
.accordion-item:last-of-type .accordion-button {
  border-radius: 20px;
}
.accordion-item:last-of-type .accordion-button.collapsed {
  border-radius: 20px;
}
.accordion-header {
  position: relative;
  cursor: pointer;
}


.accordion-header .delete-icon {
  position: absolute;
  top: 50%;
  right: 30px;
  font-size: 20px;
  transform: translateY(-50%);
  z-index: 9;
  transition: all 0.5s ease;
}
.accordion-header .delete-icon:hover {
  color: #ff2424;
}
.accordion-button {
  border-radius: 20px;
  padding: 30px;
  border: 0;
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  color: #556068;
  gap: 30px;
}
.accordion-button img {
  height: 20px;
  width: 20px;
}
.accordion-button span {
  border-left: 1px solid #d7dde9;
  padding-left: 30px;
}
.accordion-button:focus {
  box-shadow: none;
  border-color: transparent;
}
.accordion-button:not(.collapsed) {
  color: #556068;
  background-color: #ffffff;
  box-shadow: none;
}
.accordion-button:not(.collapsed)::after {
  background-image: none;
}
.accordion-button:not(.collapsed)::before {
  position: absolute;
  bottom: 0;
  left: 50%;
  width: calc(100% - 60px);
  background: #d7dde9;
  content: "";
  height: 1px;
  z-index: 1;
  transform: translateX(-50%);
  transition: all 0.5s ease;
}
.accordion-button::after {
  flex-shrink: 0;
  width: 20px;
  height: 20px;
  margin-left: auto;
  content: "\f063";
  background-image: var(--bs-accordion-btn-icon);
  background-repeat: no-repeat;
  background-size: var(--bs-accordion-btn-icon-width);
  transition: var(--bs-accordion-btn-icon-transition);
  font-family: "Line Awesome Free";
  font-weight: 900;
  background-image: none;
  font-size: 20px;
  /* margin-right: 50px; */
}
.accordion-body {
  padding: 30px;
}

.homepage-content button.accordion-button {
  cursor: move !important;
}
.homepage-content .accordion-header {
  cursor: move !important;
}

.accordion.editCourseCurriculum .accordion-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 30px;
}
.accordion.editCourseCurriculum .accordion-button:not(.collapsed)::before {
  display: none;
}
.accordion.editCourseCurriculum .accordion-button::after {
  display: none;
}
.accordion.editCourseCurriculum .course-edit-action li a {
  font-size: 14px;
  line-height: 20px;
  padding: 9px 20px;
  font-weight: 500;
  width: -moz-max-content;
  width: max-content;
}
.accordion.editCourseCurriculum .course-edit-action li a.icon {
  color: var(--sg-primary) !important;
  font-size: 20px;
  padding: 8px 10px !important;
}
.accordion.editCourseCurriculum .course-edit-action li a.icon svg path {
  fill: var(--sg-primary);
}
.accordion.editCourseCurriculum .course-edit-action li a.btn:hover {
  color: var(--sg-primary);
  background-color: transparent;
  border-color: var(--sg-btn-hover-border-color);
}
.accordion.editCourseCurriculum .course-edit-action .select2-container--default .select2-selection--single {
  border: 1px solid var(--sg-primary);
}
.accordion.editCourseCurriculum .course-edit-action .select2-container--default .select2-selection--single .select2-selection__rendered {
  line-height: 16px;
  vertical-align: middle;
  padding-right: 50px;
  color: var(--sg-primary);
  font-weight: 500;
  font-size: 14px;
}
.accordion.editCourseCurriculum .course-edit-action .select2-container--default .select2-selection--single .select2-selection__arrow {
  height: 20px;
  right: 20px;
  width: 20px;
}
.accordion.editCourseCurriculum .course-edit-action .select2-container--default .select2-selection--single .select2-selection__arrow::after {
  color: var(--sg-primary);
  font-size: 16px;
}
.accordion.editCourseCurriculum .course-edit-action .select2-container--default .select2-selection--single .select2-selection__placeholder {
  font-size: 14px;
  line-height: 20px;
  vertical-align: middle;
  color: var(--sg-primary);
  font-weight: 500;
}
.accordion.editCourseCurriculum .accordion-body {
  padding: 10px 30px 30px;
}

.accordion.accordion-v2 .accordion-item {
  border: 0;
}
.accordion.accordion-v2 .accordion-item:last-child {
  margin-bottom: 0;
}
.accordion.accordion-v2 .accordion-header:hover .accordion-button ul {
  opacity: 1;
  visibility: visible;
}
.accordion.accordion-v2 .accordion-button {
  padding: 23px;
  background: #f1f3fb;
  border-radius: 5px !important;
}
.accordion.accordion-v2 .accordion-button::before {
  display: none;
}
.accordion.accordion-v2 .accordion-button:not(.collapsed) {
  border-radius: 5px 5px 0 0 !important;
}
.accordion.accordion-v2 .accordion-button::after {
  content: "\f107";
  margin-right: 0;
}
.accordion.accordion-v2 .accordion-button ul {
  position: absolute;
  right: 60px;
  top: 50%;
  transform: translateY(-50%);
  opacity: 0;
  visibility: hidden;
}
.accordion.accordion-v2 .accordion-button .icon {
  font-size: 24px;
}
.accordion.accordion-v2 .accordion-body {
  padding: 20px 30px;
  border: 1px solid #d7dde9;
  border-radius: 0 0 5px 5px;
}
.accordion.accordion-v2 .accordion-body .faqAns p {
  line-height: 26px;
  color: #7e7f92;
}

.modal {
  --mdlPadding: 30px;
  z-index: 999999 !important;
}
.modal-content {
  background-color: #fff;
  padding: var(--mdlPadding);
}

.btn-close.modal-close {
  position: absolute;
  top: auto;
  right: 40px;
  opacity: 1;
  background: none;
}
.btn-close.modal-close::after {
  content: "\f00d";
  font-family: "Line Awesome Free";
  font-weight: 900;
  font-size: 20px;
  position: absolute;
  top: -5px;
  right: 0;
  color: #556068;
  opacity: 1;
}
.btn-close.modal-close:focus {
  outline: 0;
  box-shadow: none;
}

.deleteModalContent .icon {
  background: rgba(255, 36, 36, 0.1);
  color: #FF2424;
  height: 70px;
  width: 70px;
  display: flex;
  border-radius: 50%;
  font-size: 30px;
  text-align: center;
  justify-content: center;
  align-items: center;
  margin: auto;
}
.deleteModalContent h2, .deleteModalContent .h2 {
  line-height: 35px;
  font-size: 24px;
  font-weight: 500;
  margin: 20px 0 8px;
}
.deleteModalContent P {
  color: #7e7f92;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 30px;
}

.datepicker {
  border-radius: 10px;
  z-index: 9999 !important;
  font-family: "Outfit";
  border: 1px solid #d7dde9;
}

.datepicker-days {
  padding: 10px 16px;
}

.datepicker th {
  color: #556068;
  font-weight: 500;
  line-height: 26px;
}

.datepicker td,
.datepicker th {
  text-align: center;
  width: 40px;
  height: 40px;
  border-radius: 0px;
  border: none;
  color: #7e7f92;
}

.datepicker table tr td.active,
.datepicker table tr td.active.disabled,
.datepicker table tr td.active.disabled:hover,
.datepicker table tr td.active:hover {
  background-color: #f1f3fb !important;
  background-image: none;
  background-repeat: repeat-x;
  filter: none;
  border-color: #3f52e3 !important;
  filter: none;
  color: var(--sg-primary);
  text-shadow: none;
  border-width: 2px;
  border-style: solid;
  border-radius: 50px !important;
  font-weight: 500;
}

.datepicker-dropdown:after {
  content: "";
  display: inline-block;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 6px solid #ffffff;
  border-top: 0;
  position: absolute;
}

.datepicker-dropdown:before {
  content: "";
  display: inline-block;
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  border-bottom: 7px solid transparent;
  border-top: 0;
  border-bottom-color: #d7dde9;
  position: absolute;
}

.datepicker table tr td.new,
.datepicker table tr td.old,
.datepicker table tr td span.new,
.datepicker table tr td span.old,
.datepicker .datepicker-years table tr td.new,
.datepicker .datepicker-years table tr td.old {
  color: #BFBFBF !important;
}

.datepicker .datepicker-switch,
.datepicker .next,
.datepicker .prev,
.datepicker tfoot tr th {
  transition: all 0.5s ease;
}

.datepicker .datepicker-switch:hover,
.datepicker .next:hover,
.datepicker .prev:hover,
.datepicker tfoot tr th:hover {
  background: #f1f3fb;
}

.datepicker table tr td span.focused,
.datepicker table tr td span:hover {
  background: #d9dcf9;
  border-radius: 0;
  color: #3f52e3 !important;
}

.datepicker table tr td span {
  display: block;
  width: 31%;
  height: 70px;
  line-height: 26px;
  float: left;
  margin: 1%;
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.datepicker table tr td.day.focused,
.datepicker table tr td.day:hover {
  background: #f1f3fb;
  cursor: pointer;
  border-radius: 50%;
}

.datepicker table tr td.active.day.focused,
.datepicker table tr td.active.day:hover {
  border-radius: 50%;
}

.datepicker .datepicker-months table tbody tr td {
  width: initial;
  height: initial;
}

.datepicker .datepicker-years table tbody td,
.datepicker .datepicker-centuries table tbody tr td,
.datepicker .datepicker-decades table tbody tr td {
  width: 92px;
}

.daterangepicker {
  font-family: "Outfit";
}
.daterangepicker .drp-calendar.left {
  padding: 16px 0 16px 16px;
}
.daterangepicker .drp-calendar.right {
  padding: 16px 16px 16px 10px;
}
.daterangepicker .calendar-table th,
.daterangepicker .calendar-table td {
  white-space: nowrap;
  text-align: center;
  vertical-align: middle;
  min-width: 36px;
  width: 32px;
  height: 36px;
  line-height: 24px;
  font-size: 16px;
  border-radius: 0px;
  font-weight: 400;
  color: #7e7f92;
}
.daterangepicker .calendar-table th,
.daterangepicker th.month {
  width: auto;
  color: #556068;
  font-weight: 500 !important;
}

.daterangepicker td.off,
.daterangepicker td.off.in-rang .daterangepicker td.off.start-date .daterangepicker td.off.end-date {
  background-color: #fff;
  border-color: transparent;
  color: #bfbfbf;
}

.daterangepicker td.active, .daterangepicker td.active:hover {
  background-color: #f1f3fb;
  border-color: transparent;
  color: var(--sg-primary);
  font-weight: 500;
}

.daterangepicker td.active.in-range {
  color: var(--sg-primary);
}

.daterangepicker td.in-range {
  background-color: #f1f3fb;
  border-color: transparent;
  color: #7e7f92;
  border-radius: 0;
}

.daterangepicker td.available:hover, .daterangepicker th.available:hover {
  background-color: #f1f3fb;
  border-color: transparent;
  color: inherit;
}

.daterangepicker .drp-selected {
  display: inline-block;
  font-size: 14px;
  color: #7e7f92;
  padding-right: 8px;
  line-height: 24px;
}

.daterangepicker .drp-buttons {
  clear: both;
  text-align: right;
  padding: 20px 16px;
  border-top: 1px solid #d7dde9;
  display: none;
  line-height: 24px;
}

.daterangepicker .drp-buttons .btn {
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  padding: 5px 10px;
  border-radius: 5px;
}

.daterangepicker .drp-buttons .btn:active,
.drp-buttons .cancelBtn {
  border: 1px solid var(--sg-primary);
  margin-left: 30px;
  color: var(--sg-primary);
}

.daterangepicker td.start-date.end-date {
  border-radius: 0px;
}/*# sourceMappingURL=app.css.map */

.correct-symbol{
  color:#24d6a5;
  font-weight: bold;
  font-size: 100px;
}
.wrong-symbol{
  color:red;
}

.rating {
  unicode-bidi: bidi-override;
  direction: ltr;
  text-align: center;
}

.rating > span {
  display: inline-block;
  position: relative;
  color: orange!important;
  font-size: 30px;
  cursor: pointer;
}

.rating > span:hover:before,
.rating > span.checked:before {
  content: "\2605";
  position: absolute;
}

.stars{
  color: orange!important;
  font-size: 24px;
}

.no-line-braek{
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.client_payment_box {
  cursor: pointer;
}

.client_payment_box:hover {
  border-color: #d7dde9;
}
.whatsapp-bg{
  background-color: #0d8d2d !important;
}

.telegram-bg{
  background-color: #0C7DBD !important;
}

  /* .whatsapp-badge {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    padding: 1px 34px;
    color: #101223;
    letter-spacing: 1px;
    background: #82f3a9;
    border-radius: 100px;
    border: 1px solid #16a34b;
    box-shadow: 4px 4px 0 0 #136831;
  } */

  .whatsapp-badge {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    padding: 5px 37px;
    color: #fff;
    letter-spacing: 1px;
    background: #24d6a5;
    border-radius: 100px;
    /* font-weight: 600; */
    border: 1px solid #fff;
    /* box-shadow: 4px 4px 0 0 #136831; */
}

.telegram-badge {
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  padding: 1px 34px;
  color: #ffffff;
  letter-spacing: 1px;
  background: #0088CC;
  border-radius: 100px;
  /*font-weight: 600;*/
  border: 1px solid #179CDE;
  box-shadow: 4px 4px 0 0 #333333;
}


.active_pg {
  border: 3px solid var(--sg-primary) !important;
}

.disabled_a {
  pointer-events: none;
  cursor: default;
  opacity: 0.8;
}
.canned_response_div{
  /*border: 2px solid var(--sg-primary);*/
  padding: 10px;
  border-radius: 5px;
  cursor: pointer;
  background: #f1f3fb;
  font-size: 14px;
  font-weight: 500;
}

.log-social li a {
  height: 50px;
  width: 50px;
  display: flex;
  border: 2px solid;
  align-items: center;
  justify-content: center;
}

.log-social li:nth-child(1) a {
  border-color: #3b5998 !important;
}

.log-social li:nth-child(2) a {
  border-color: #db4437 !important;
}

.log-social li:nth-child(3) a {
  border-color: #55acee !important;
}

.log-social li:nth-child(4) a {
  border-color: #666666 !important;
}

ul.login-BTN {
  -moz-column-count: 2;
  column-count: 2;
}

.login-BTN a.template-btn.bordered-btn-secondary {
  background-color: transparent !important;
}

.login-BTN a.template-btn.bordered-btn-secondary:hover {
  border-color: var(--color-dark) !important;
}

.login-BTN li {
  margin-bottom: 20px;
}

.login-as {
  padding: 20px;
  border: 1px solid var(--color-soft-white-7);
  position: relative;
  border-radius: 10px;
  padding-bottom: 0;
}
.login-as h6 {
  position: absolute;
  top: -12px;
  background: #fff;
  left: 50%;
  transform: translateX(-50%);
  color: #556068;
  text-align: center;
  font-size: 16px;
  font-weight: 500;
  padding: 0 10px;
}

.forgot-pass-btn h6 {
  color: #556068;
  text-align: center;
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
}

/*======= Scroll to Top =======*/
.back-to-top {
  position: fixed;
  transform: translateY(100px);
  z-index: 999;
  width: 42px;
  height: 42px;
  line-height: 42px;
  background: var(--color-secondary-4);
  color: #fff;
  font-size: 20px;
  text-align: center;
  border-radius: 50px;
  transition: all 0.5s;
  opacity: 0;
  visibility: hidden;
  z-index: 1;
  inset-block-end: 25px;
  inset-inline-end: 20px;
}
.lern-more{
  color:rgba(10, 120, 190, 1);
}


/* Dashboard Css */
@media (max-width: 1440px) {
  .analytics-box {
    padding: 20px !important;
  }

}

.analytics-content h4, .analytics-content .h4 {
  white-space: nowrap;
}

div#sendTemplateModal .modal-body {
  overflow: initial;
}

div#sendTemplateModal .modal-body .add-coupon {
  max-height: 380px;
}



/* whatsapp contact page */

/* .add-coupon .file_picker {
  display: block !important;
  border: 1px solid #d7dde9;
  border-radius: 5px;
  width: 100%;
  height: 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 3px;
  cursor: pointer;
  position: relative;
} */

.file-upload-text {
  position: relative;
}

.file-upload-text:after {
  content: 'Choose file';
  color: #7e7f92;
  background: #d7dde9;
  padding: 7px 20px;
  font-size: 16px;
  line-height: 26px;
  cursor: pointer;
  border-radius: 0 5px 5px 0;
  white-space: nowrap;
  display: flex;
  align-items: center;
}

@media (max-width: 479px) {
  .file-upload-text:after {
    padding: 7px 6px;
  }
}
.file-upload-text span.file-btn {
  display: none;
}




/* Contact Manage Tab Css Start */
.contactManage__tabs .nav {
  gap: 10px;
}

.contactManage__tabs .nav {
  gap: 10px;
  border-color: #efefef;
}
.contactManage__tabs .nav .nav-item .nav-link {
  font-size: 16px;
  /* border: 1px solid rgba(63, 82, 227, 0.2); */
  border-radius: 4px 4px 0 0;
  padding: 4px 20px;
  color: var(--bs-dark);
  text-transform: capitalize;
  transition: all 300ms ease-in-out;
}

.contactManage__tabs .nav .nav-item .nav-link:hover,
.contactManage__tabs .nav .nav-item .nav-link.active {
  /* background: var(--bs-primary); */
  /* color: var(--bs-body-bg); */
  color: var(--bs-primary);
}


.contactManage__body {
  display: flex;
  gap: 15px;
  flex-direction: column;
}

.contactManage__body .contactManage__left {
    /* max-width: 250px; */
    width: 100%;
    border: 1px solid #efefef;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
}
.contactManage__body .contactManage__right {
  flex: 1;
}

.contactManage__body .avatar {
  height: 50px;
  width: 50px;
  border-radius: 50%;
  display: inline-block;
  border: 2px solid var(--bs-primary);
}

.contactManage__body .avatar img {
  width: 100%;
  height: auto;
  display: inline-block;
  border-radius: 50%;
}

.contactManage__body .avatar__inform {
  border-top: 1px solid #efefef;
  margin-top: 15px;
  padding-top: 15px;
}

.contactManage__body .avatar__inform ul {
  max-width: 250px;
  margin: auto;
}

.contactManage__body .avatar__inform ul li {
  font-size: 15px;
  font-weight: 400;
  display: flex;
  align-items: baseline;
  line-height: 16px;
  gap: 5px;
  margin-bottom: 10px;
}

.contactManage__body .avatar__inform ul li:last-of-type {
  margin-bottom: 0;
}
.contactManage__body .avatar__inform ul li i {
  font-size: 18px;
  position: relative;
  top: 2px;
}

.contactManage__tabs .badge {
  background: #f6f6f6;
}

.note__tabs {
  background: #f6f6f6;
  border-radius: 6px;
  padding: 10px 30px;
  display: inline-block;
}

.note__tabs .title {
  font-weight: 500;
}

.contactViewModal.fade {

}


.right.fade .modal-dialog {
  right: -100%;
  -webkit-transition: opacity 0.3s linear, right 0.3s ease-out;
  -moz-transition: opacity 0.3s linear, right 0.3s ease-out;
  -o-transition: opacity 0.3s linear, right 0.3s ease-out;
  transition: opacity 0.3s linear, right 0.3s ease-out;
  margin: 0;
  left: auto;
  margin-left: auto;
  width: 500px;
  position: fixed;
}
.right.fade.show .modal-dialog {
  right: 0;
}

.contactManage__tabs .tab-content {
  overflow-y: auto;
  height: calc(100vh - 150px);
  overflow: hidden;
  overflow-y: auto;
  padding-right: 10px;
  border: 1px solid #efefef;
  padding: 10px;
  border-radius: 0 0px 8px 8px;
  border-top: none;
}

.contactManage__tabs .tab-content .content__title {
  margin-top: 10px;
  margin-bottom: 0;
}
#contactViewModal .modal-body {
  padding: 20px 10px 20px 20px;
}
#contactViewModal .modal-content {
  padding: 0;
  border: none;
  border-radius: 6px 0 0 6px;
}

.contactManage__tabs .tab-content::-webkit-scrollbar {
  width: 5px;
}
.contactManage__tabs .tab-content::-webkit-scrollbar-track {
  box-shadow: none;
  background-color: #e5e5e5;
  border-radius: 4px;
}
.contactManage__tabs .tab-content::-webkit-scrollbar-thumb {
  background-color: var(--sg-primary);
  border-radius: 4px;
  -webkit-transition: background-color 0.5s ease;
  transition: background-color 0.5s ease;
}



@media (max-width: 575px) {
  .right.fade .modal-dialog {
    width: 400px;
  }
  .contactManage__tabs .nav .nav-item .nav-link  {
    font-size: 14px;
    padding: 2px 12px;
  }
}

@media (max-width: 400px) {
  .right.fade .modal-dialog {
    width: 350px;
  }
  .contactManage__tabs .nav {
    gap: 8px;
  }
}
@media (max-width: 374px) {
  .right.fade .modal-dialog {
    width: 320px;
  }
}
/* Contact Manage Tab Css End */

.whatsapp-button {
  background-color: #25D366;
  /* WhatsApp green */
  border: 0;
  border-radius: 4px;
  color: #fff;
  cursor: pointer;
  font-family: Helvetica, Arial, sans-serif;
  font-size: 16px;
  font-weight: bold;
  height: 40px;
  padding: 0 24px;
  /* display: flex; */
  align-items: center;
  justify-content: center;
  gap: 8px;
  /* Space between icon and text */
}

.whatsapp-button i {
  font-size: 20px;
}

.whatsapp-button:hover {
  color: #fff;
}

.whatsapp__icon {
  width: 60px;
  margin: 0 auto 20px;
}

/* whatsapp__card start */
.whatsapp__card .card h5 {
  text-transform: capitalize;
  border-bottom: 1px solid #dfdfdf;
  padding-bottom: 10px;
}


.whatsapp__card .card .flex__item {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0 15px;
}
.whatsapp__card .card .mb-2 {
  /* background: #f9f9f9; */
  padding: 3px 3px 3px 10px;
  border-radius: 5px;
}
.whatsapp__card .card .mb-2 strong,
.whatsapp__card .card .mb-3 strong {
  /* display: block; */
  color: #545560;
  line-height: 26px;
  /* margin-bottom: 4px; */
  font-size: 16px;
  font-weight: 500;
  text-transform: capitalize;
}

.whatsapp__card .card .mb-2 strong i {
  color: var(--sg-sidebar-menu-active);
}

.whatsapp__card .card .mb-2 .text-muted {
  /* background: blue; */
}
.social-post .image-previews {
  display: grid;
  gap: 10px;
  grid-template-columns: repeat(auto-fit, minmax(48%, 1fr));
}
.social-post .image-previews img{
  width: 100%;
}

.social-post .social-header .text-primary {
  color: #2fabf7 !important;
}
/* .whatsapp__card .card .mb-3 span.text-muted {
  display: block;
  width: 100%;
  padding: 6px 12px;
  padding-left: 22px;
  font-size: 15px;
  font-weight: 400;
  line-height: 1.5;
  color: #212529;
  background-color: #ffffff;
  background-clip: padding-box;
  border: 1px solid #dfdfdf;
  border-radius: var(--bs-border-radius);
} */
/* whatsapp__card end */


/* platform__wrapper Start */
.platform__wrapper {
  background: #FAFAFA;
  border: 1px solid #EEEEEE;
  border-radius: 6px;
  padding: 24px;
}

@media (max-width: 1199px) {
  .platform__wrapper {
    margin-bottom: 24px;
  }
}

.social__platform {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}


.social__select {
  /* margin-bottom: 16px; */
  /* width: 100%; */
}
/* .social__select:last-of-type {
  margin-bottom: 0;
} */
.social__select input[type="radio"] {
  display: none;
}

.social__select .social__item {
  font-family: Outfit;
  font-size: 14px;
  font-weight: 500;
  line-height: 17.64px;
  color: #121A1F;
  display: flex;
  align-items: center;
  gap: 12px;
  justify-content: center;
  /* padding: 16px; */
  border: 1px solid #EEEEEE;
  border-radius: 6px;
  cursor: pointer;
  background: #fff;
  min-width: 60px;
  height: 60px;
}

.social__select input[type="radio"]:checked + .content__item,
.social__select input[type="radio"]:checked + .social__item {
  border: 1px solid #2FABF799;
}

/* ================================= */


.contentIdea__wrapper .content__title {
  font-family: Outfit;
  font-size: 16px;
  font-weight: 500;
  line-height: 25.2px;
  color: #121A1F;
  padding: 19px 0 10px;
  margin: 0;
}

.contentIdea__inner {
  display: grid;
  grid-template-columns: auto;
  gap: 10px;
  overflow: auto;
  /* max-height: 450px; */
  max-height: 545px;
  padding-right: 10px;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .contentIdea__inner {
    grid-template-columns: repeat(2, 1fr);

  }
}

.social__select .content__item {
  background: #fff;
  padding: 17px;
  border: 1px solid #fff;
  border-radius: 6px;
  cursor: pointer;
}

.social__select .social__item .icon svg {
  width: 23px;
}
.social__select .content__item .title {
  font-family: Outfit;
  font-weight: 500;
  font-size: 16px;
  line-height: 24.28px;
  color: #121A1F;
  cursor: pointer;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.social__select .content__item .content {
  font-family: Outfit;
  font-weight: 400;
  font-size: 14.98px;
  line-height: 21.41px;
  color: #666666;
  margin-bottom: 0;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}


.platform__bottom {
  background: #FAFAFA;
  border: 1px solid #EEEEEE;
  border-radius: 6px;
  padding: 21px 17px;
}

@media (max-width: 400px) {
  .social__platform {
    grid-template-columns: repeat(3, 1fr);
  }
  .social__select .social__item {
    padding: 8px;
  }
  .social__select .social__item svg {
    width: 18px;
  }
  .platform__wrapper {
    padding: 16px;
  }
  .social__select .content__item .title {
    font-size: 16px;
  }
}

.btn-dark {
  font-family: Outfit;
  font-weight: 500;
  font-size: 16.06px;
  line-height: 25.69px;
  letter-spacing: 0.11px;
  padding: 9px 25px;
  border: none;
  outline: none;
  background: #121A1F;
  color: #FFFFFFDE;
  border-radius: 6px;
}

.create__dropdown .select2-container {
  width: 200px !important;
}
.create__dropdown .select2-container--default .select2-selection--single .select2-selection__rendered {
  padding-right: 33px;
}


/* platform__wrapper End */



/* Custom Scroll Bar Start */
::-webkit-scrollbar-track
{
	-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
	background-color: #EFEFEF;
  border-radius: 30px;
}

::-webkit-scrollbar
{
	width: 4px;
  height: 4px;
	background-color: #EFEFEF;
  border-radius: 30px;
}

::-webkit-scrollbar-thumb
{
	background: #121A1F;
  border-radius: 30px;

	/* border: 2px solid #555555; */
}
/* Custom Scroll Bar End */

.dateItem {
  display: flex;
  align-items: center;
  border: 1px solid #d7dde9;
  border-radius: 6px;
}

.dateItem .delete_btn {
  padding: 0px 10px;
    font-size: 20px;
}

.dateItem .btn:focus {
  border: none;
  outline: none;
}

.dateItem .form-control {
  border: none;
    outline: none;
    border-right: 1px solid #d7dde9;
    /* height: auto; */
    background-color: transparent;
}

.card-footer .add_btn {
  padding: 0;
  color: #2FABF7;
  border: none;
}

.card-footer .add_btn:focus {
  border: none;
  outline: none;
}

.post__wrap .post__by {
  display: none;
}

.custom__tab .nav-tabs {
  border: none !important;
  background: rgba(47, 171, 247, 0.1);
  padding: 10px;
  border-radius: 8px;
  /* justify-content: space-between; */
}
.custom__tab .nav-tabs .nav-link {
  border: none;
  padding: 4px 10px;
    font-size: 15px;
    border-radius: 6px;
}
.custom__tab .nav-tabs .nav-link:hover {
  color: #2fabf7;
}
.custom__tab .nav-tabs .nav-link.active {
  border-radius: 6px;
  background: #2fabf7;
  color: #fff;
}


/* Custom Select Start */
.selectBox_withImage .select2-results__option {
  padding-right: 20px;
  vertical-align: middle;
  position: relative;
    border-bottom: 1px dashed #e1e5ee;
    padding: 5px 20px;
}

.selectBox_withImage .select2-results__option:last-of-type {
  border: none;
}

.selectBox_withImage .select2-results__option span {
  display: flex;
  align-items: center;
  gap: 10px;
}

.selectBox_withImage .select2-results__option img {
  width: 30px;
  height: 30px;
  border-radius: 6px;
}

.selectBox_withImage .select2-results__option:before {
  content: "";
  display: inline-block;
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  height: 20px;
  width: 20px;
  border: 2px solid #e9e9e9;
  border-radius: 4px;
  background-color: #fff;
  margin-right: 20px;
  vertical-align: middle;
}
.selectBox_withImage .select2-results__option--selected:before {
  font-family: 'Line Awesome Free';
  content: "\f00c";
  /* position: relative;
  left: 0; */
  text-align: center;
  vertical-align: middle;
  height: 20px;
  width: 20px;
  background: #2fabf7;
  border-color: #2fabf7;
  color: #fff;
  font-size: 13px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 900;
}


.select2-container--default .select2-selection--multiple .select2-selection__choice__display span {
  display: flex;
  align-items: center;
  gap: 10px;
}

.select2-selection.select2-selection--multiple .select2-selection__choice__display img {
  width: 20px;
  height: 20px;
  border-radius: 6px;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__display {
  display: flex;
  align-items: center;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice {
  padding-left: 5px;
}
.select2-container--default .select2-dropdown {
  z-index: 9999;
}

/* Custom Select End */



/* Flatpicker Calender Start */
.flatpickr-calendar {
  padding: 10px;
  width: 320px;
}

.flatpickr-innerContainer {
  padding-top: 10px;
}

.flatpickr-current-month {
  margin-top: 0;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
.flatpickr-current-month .flatpickr-monthDropdown-months {
  margin: 0;
  padding: 0;
  color: #3F4254;
  font-size: 15px;
}

.flatpickr-current-month .numInputWrapper {
  font-size: 15px;
  color: #3F4254;
}

.flatpickr-months .flatpickr-prev-month, .flatpickr-months .flatpickr-next-month {
  position: relative;
}


.flatpickr-weekdaycontainer span {
  font-weight: 500;
  color: #3F4254;
  font-size: 13px;
}


.flatpickr-innerContainer .flatpickr-day {
  font-weight: 500;
  /* color: #2FABF7; */
  color: #3F4254;
  font-size: 13px;
}



.flatpickr-innerContainer .flatpickr-day.today {
  border-color: #2FABF7;
}

.flatpickr-innerContainer .flatpickr-day.selected,
.flatpickr-innerContainer .flatpickr-day:hover,
.flatpickr-innerContainer .flatpickr-day.today.selected {
  background-color: #2FABF7;
  border-color: #2FABF7;
  color: #fff;
}

.flatpickr-innerContainer .flatpickr-disabled {
  color: rgba(57,57,57,0.1) !important;
}

.flatpickr-innerContainer .flatpickr-disabled:hover {
  color: rgba(57,57,57,0.1) !important;
  background: #e6e6e6;
  border-color: #e6e6e6;
}


.flatpickr-time input.flatpickr-hour {
  font-weight: 500;
}

/* Flatpicker Calender End */


/*********************************
/* Tabs Start
*********************************/
.custom__tabs .nav {
  background: rgba(47, 171, 247, 0.1);
  padding: 6px;
  border-radius: 6px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -ms-flex-wrap: nowrap;
      flex-wrap: nowrap;
  gap: 14px;
  margin-bottom: 42px;
}
.custom__tabs .nav li .nav-link {
  font-family: var(--font-default);
  font-size: 16px;
  font-weight: var(--font-regular);
  line-height: 18.75px;
  padding: 8px 16px;
  background: transparent;
  color: #556068;
  border: none;
  border-radius: 4px;
  min-width: 85px;
  text-transform: capitalize;
}
.custom__tabs .nav li .nav-link.active {
  background: var(--sg-sidebar-menu-active);
  color: #fff;
}

/*********************************
/* Tabs End
*********************************/