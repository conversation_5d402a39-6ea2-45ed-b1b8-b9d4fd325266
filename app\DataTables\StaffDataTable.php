<?php

namespace App\DataTables;

use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;
use Ya<PERSON>ra\DataTables\EloquentDataTable;
use Ya<PERSON>ra\DataTables\Html\Builder as HtmlBuilder;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class StaffDataTable extends DataTable
{
    public function dataTable(QueryBuilder $query): EloquentDataTable
    {
        return (new EloquentDataTable($query))
            ->addIndexColumn()
            ->addColumn('action', function ($staff) {
                return view('backend.admin.staff.action', compact('staff'));
            })->addColumn('status', function ($staff) {
                return view('backend.admin.staff.status', compact('staff'));
            })->addColumn('phone', function ($staff) {
                return isDemoMode() ? '+*********' : $staff->phone;
            })->addColumn('name', function ($user) {
                return view('backend.admin.staff.name', compact('user'));
            })->addColumn('last_login', function ($user) {
                return $user->lastActivity ? __('active').' '.Carbon::parse($user->lastActivity->created_at)->diffForHumans() : '';
            })
            ->setRowId('id');
    }

	public function query(): QueryBuilder
	{
		$search = request('search.value');

		return User::with('lastActivity')
			->whereNotIn('role_id', [3, 1])
			->when($search, function ($query) use ($search) {
				$query->where(function ($query) use ($search) {
					$query->where('first_name', 'like', "%{$search}%")
						->orWhere('last_name', 'like', "%{$search}%")
						->orWhere('email', 'like', "%{$search}%")
						->orWhere('phone', 'like', "%{$search}%");
				});
			})
			->latest();
	}

	public function html(): HtmlBuilder
    {
        return $this->builder()
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->orderBy(1)
            ->selectStyleSingle()
            ->setTableAttribute('style', 'width:99.8%')
            ->footerCallback('function ( row, data, start, end, display ) {

                $(".dataTables_length select").addClass("form-select form-select-lg without_search mb-3");
                selectionFields();
            }')
            ->parameters([
                'dom'        => 'Blfrtip',
                'buttons'    => [
                    [],
                ],
                'lengthMenu' => [[10, 25, 50, 100, 250], [10, 25, 50, 100, 250]],
                'language'   => [
                    'searchPlaceholder' => __('search'),
                    'lengthMenu'        => '_MENU_ '.__('staff_per_page'),
                    'search'            => '',
                ],
            ]);
    }

    public function getColumns(): array
    {
        return [
			Column::computed('id')->data('DT_RowIndex')->title('#')->width(10),
            Column::computed('name')->title(__('name_and_mail')),
            Column::computed('phone')->title(__('phone')),
            Column::computed('last_login')->title(__('last_login')),
            Column::computed('status')->title(__('status'))->exportable(false)
                ->printable(false),
            Column::computed('action')->title(__('action'))
                ->exportable(false)
                ->printable(false)
                ->searchable(false)->addClass('action-card')->width(10),
            Column::make('email')->title(__('email'))->addClass('d-none'),
        ];
    }

    protected function filename(): string
    {
        return 'Currency_'.date('YmdHis');
    }
}
