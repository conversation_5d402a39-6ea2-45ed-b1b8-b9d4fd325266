<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PostSchedule extends Model
{
    use HasFactory;

    protected $casts = ['repost_frequency',
        'social_accounts' => 'array',
        ];

    public function post(): BelongsTo
    {
        return $this->belongsTo(Post::class);
    }
}
