<?php
namespace App\Http\Controllers\Admin;
use App\DataTables\CityDataTable;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\CityRequest;
use App\Repositories\CityRepository;
use App\Repositories\CountryRepository;
use App\Repositories\StateRepository;
use Exception;
use Illuminate\Http\Request;

class CityController extends Controller
{
    protected $city;

    public function __construct(CityRepository $city)
    {
        $this->city = $city;
    }

    public function index(CityDataTable $dataTable,CountryRepository $country)
    {
        $data = [
            'countries' => $country->activeCountries()
        ];
        return $dataTable->render('backend.admin.city.index',$data);
    }

    public function store(CityRequest $request): \Illuminate\Http\JsonResponse
    {
        if (isDemoMode()) {
            $data = [
                'status'  => false,
                'message' => __('this_function_is_disabled_in_demo_server'),
                'title'   => 'error',
            ];

            return response()->json($data);
        }
        try {
            $this->city->store($request->all());

            return response()->json(['success' => __('create_successful')]);
        } catch (Exception $e) {
            return response()->json(['status' => false,'error' => 'Something went wrong, please try again']);
        }
    }

    public function edit($id,CountryRepository $country,StateRepository $stateRepository): \Illuminate\Http\JsonResponse
    {
        try {
            $city = $this->city->find($id);

            $vars = [
                'city'      => $city,
                'countries' => $country->activeCountries(),
                'states'    => $stateRepository->stateByCountry($city->country_id)
            ];

            $data           = [
                'html'      => view('backend.admin.city.edit_city', $vars)->render(),
                'success'   => true
            ];

            return response()->json($data);
        } catch (Exception $e) {
            return response()->json(['status' => false,'error' => 'Something went wrong, please try again']);
        }
    }

    public function update(CityRequest $request, $id): \Illuminate\Http\JsonResponse
    {
        if (isDemoMode()) {
            $data = [
                'status'  => false,
                'message' => __('this_function_is_disabled_in_demo_server'),
                'title'   => 'error',
            ];

            return response()->json($data);
        }
        try {
            $this->city->update($request->all(), $id);

            return response()->json(['success' => __('update_successful')]);
        } catch (Exception $e) {
            return response()->json(['status' => false,'error' => 'Something went wrong, please try again']);
        }
    }

    public function destroy($id): \Illuminate\Http\JsonResponse
    {
        if (isDemoMode()) {
            $data = [
                'status'  => false,
                'message' => __('this_function_is_disabled_in_demo_server'),
                'title'   => 'error',
            ];

            return response()->json($data);
        }
        try {
            $this->city->destroy($id);

            $data = [
                'status'  => 'success',
                'message' => __('delete_successful'),
                'title'   => __('success'),
            ];

            return response()->json($data);
        } catch (Exception $e) {
            $data = [
                'status'  => 400,
                'message' => 'Something went wrong, please try again',
                'title'   => 'error',
            ];

            return response()->json($data);
        }
    }

    public function statusChange(Request $request): \Illuminate\Http\JsonResponse
    {
        if (isDemoMode()) {
            $data = [
                'status'  => false,
                'message' => __('this_function_is_disabled_in_demo_server'),
                'title'   => 'error',
            ];

            return response()->json($data);
        }
        try {
            $this->city->statusChange($request->all());

            $data = [
                'status'  => 200,
                'message' => __('update_successful'),
                'title'   => 'success',
            ];

            return response()->json($data);
        } catch (Exception $e) {
            $data = [
                'status'  => 400,
                'message' => 'Something went wrong, please try again',
                'title'   => 'error',
            ];

            return response()->json($data);
        }
    }
}
